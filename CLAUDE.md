# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Texas Hold'em poker server implementation in Go that supports both TCP and WebSocket connections. The application uses dependency injection (uber-go/fx), Ent ORM for database operations, and a custom event-driven protocol for real-time poker game communication.

## Development Commands

### Running the Application
```bash
# Development run with debug mode
make run

# Docker deployment
make docker-run
make docker-stop  
make docker-restart
```

### Database Operations
```bash
# Generate Ent schema code
make ent-generate

# Create new database migration (interactive)
make ent-migrate
```

### Environment Setup
```bash
# Copy environment configuration
cp .env.example .env
```

## Architecture

### Core Components

- **main.go**: Application bootstrap using fx dependency injection
- **config/**: Configuration management with environment variable support
- **internal/server/**: Multi-protocol servers (HTTP, TCP, WebSocket)
- **internal/handler/**: Event handlers for game actions
- **internal/service/**: Business logic layer (authorize, table, player services)
- **internal/event/**: Event definitions for client-server communication
- **internal/states/**: Game state machine implementation
- **internal/db/ent/**: Database schema and generated ORM code
- **pkg/poker/**: Poker game logic and card handling

### Database Schema

The application uses PostgreSQL with Ent ORM. Key entities:
- **Games**: Poker game sessions with players, board state, pot, etc.
- **Tables**: Poker tables with seating and game configuration

### Event System

Communication follows a custom JSON event protocol over TCP/WebSocket:
- Events have `id`, `src_id`, and `t` (type) fields
- Client events: ping, authorize, join_table, bet, etc.
- Server events: pong, authorized, table_state, bet_prompt, etc.

### Authentication Flow

1. Client obtains session token through Firebase authentication (frontend)
2. Client sends `authorize` event with session token directly
3. Server validates session token against Redis and responds with `authorized` event
4. Client must send periodic `ping` events for heartbeat

### Game Flow Architecture

- **Scheduler**: Manages game timing and state transitions
- **States**: State machine pattern for poker game phases (pre-flop, flop, turn, river, etc.)
- **Peer Management**: Connection handling and event routing

## Important Files for Modification

- **internal/event/**: Modify for new event types
- **internal/handler/**: Add new game action handlers
- **internal/states/**: Extend game state machine
- **internal/db/ent/schema/**: Database schema changes
- **pkg/poker/**: Core poker logic modifications

## Testing

The project includes test files in the service layer. Run tests with:
```bash
go test ./...
```

## Dependencies

Key Go modules:
- `entgo.io/ent`: ORM and schema management
- `github.com/gin-gonic/gin`: HTTP server framework
- `github.com/gorilla/websocket`: WebSocket support
- `go.uber.org/fx`: Dependency injection
- `github.com/redis/go-redis/v9`: Redis client