package async

import (
	"bytes"
	"fmt"
	"hamster/pkg/log"
	"runtime"
)

func Go(functions ...func() error) {
	for _, fun := range functions {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					printStackTrace(err)
				}
			}()
			err := fun()
			if err != nil {
				log.Error(err)
			}
		}()
	}
}

func printStackTrace(err interface{}) {
	buf := bytes.Buffer{}
	buf.WriteString(fmt.Sprintf("%v\n", err))
	for i := 1; ; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		buf.WriteString(fmt.Sprintf("%s:%d (0x%x)\n", file, line, pc))
	}
	fmt.Println(buf.String())
}
