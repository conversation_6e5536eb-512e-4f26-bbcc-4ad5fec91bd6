package queue

import "encoding/json"

type Queue[T string | int] struct {
	items  []T
	filter map[T]bool
}

func New[T string | int]() *Queue[T] {
	return &Queue[T]{items: make([]T, 0), filter: make(map[T]bool)}
}

func (q *Queue[T]) Enqueue(item T) {
	if q.filter[item] {
		return
	}
	q.items = append(q.items, item)
	q.filter[item] = true
}

func (q *Queue[T]) Dequeue() (T, bool) {
	if len(q.items) == 0 {
		var zero T
		return zero, false
	}
	item := q.items[0]
	q.items = q.items[1:]
	delete(q.filter, item)
	return item, true
}

func (q *Queue[T]) Remove(item T) int {
	if !q.filter[item] {
		return -1
	}
	delete(q.filter, item)
	for i, v := range q.items {
		if v == item {
			q.items = append(q.items[:i], q.items[i+1:]...)
			return i
		}
	}
	return -1
}

func (q *Queue[T]) Front() (T, bool) {
	if len(q.items) == 0 {
		var zero T
		return zero, false
	}
	return q.items[0], true
}

func (q *Queue[T]) IsEmpty() bool {
	return len(q.items) == 0
}

func (q *Queue[T]) Size() int {
	return len(q.items)
}

func (q *Queue[T]) MarshalJSON() ([]byte, error) {
	return json.Marshal(q.items)
}

func (q *Queue[T]) UnmarshalJSON(data []byte) error {
	err := json.Unmarshal(data, &q.items)
	if err != nil {
		return err
	}
	for _, item := range q.items {
		q.filter[item] = true
	}
	return nil
}
