package bufio

import (
	"bufio"
	"io"
)

type ReadWriterCloser struct {
	io.Reader
	io.Writer
	io.Closer
}

func NewReadWriterCloser(r io.Reader, w io.Writer, c io.Closer) io.ReadWriteCloser {
	return &ReadWriterCloser{r, w, c}
}

func NewReadSize(r io.Reader, size int) *bufio.Reader {
	return bufio.NewReaderSize(r, size)
}

func NewWriteSize(w io.Writer, size int) *bufio.Writer {
	return bufio.NewWriterSize(w, size)
}
