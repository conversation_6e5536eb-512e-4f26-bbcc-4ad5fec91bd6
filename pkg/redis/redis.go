package redis

import (
	"context"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"hamster/config"
	"hamster/pkg/log"
	"time"
)

type Client struct {
	*redis.Client
}

func New(c config.Config) *Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:           c.Redis.Addr,
		Password:       c.Redis.Password,
		DB:             c.Redis.Database,
		MaxIdleConns:   c.Redis.MaxIdle,
		MaxActiveConns: c.Redis.MaxActive,
	})
	return &Client{Client: rdb}
}

func (c *Client) TryLock(ctx context.Context, lockKey string, ttl time.Duration) (func(), error) {
	expire := time.Now().Add(ttl)
	for {
		suc, err := c.Lock(ctx, lockKey, ttl)
		if err != nil {
			return nil, err
		}
		if suc {
			return func() {
				err = c.Unlock(ctx, lockKey)
				if err != nil {
					log.Errorf("unlock failed, key: %s, err: %v", lockKey, err)
				}
			}, nil
		}
		if time.Now().After(expire) {
			return nil, errors.New("system busy")
		}
		time.Sleep(100 * time.Millisecond)
	}
}

// Lock attempts to acquire a lock, allowing up to `limit` concurrent locks
func (c *Client) Lock(ctx context.Context, lockKey string, ttl time.Duration) (bool, error) {
	var script = `
	local key = KEYS[1]
	local limit = tonumber(ARGV[1])
	local count = tonumber(redis.call('get', key) or "0")
	
	if count + 1 > limit then
		return 0
	else
		redis.call("INCRBY", key, 1)
		redis.call("EXPIRE", key, ARGV[2])
		return 1
	end
    `
	result, err := c.Eval(ctx, script, []string{lockKey}, 1, int(ttl.Seconds())).Result()
	if err != nil {
		return false, err
	}
	return cast.ToInt(result) == 1, nil
}

// Unlock decrements the lock count
func (c *Client) Unlock(ctx context.Context, lockKey string) error {
	var script = `
    local key = KEYS[1]
	local count = tonumber(redis.call('get', key) or "0")
	
	if count > 0 then
		redis.call("DECRBY", key, 1)
		return 1
	else
		return 0
	end
    `
	_, err := c.Eval(ctx, script, []string{lockKey}).Result()
	if err != nil {
		return err
	}
	return nil
}

func IsNil(err error) bool {
	return errors.Is(err, redis.Nil)
}
