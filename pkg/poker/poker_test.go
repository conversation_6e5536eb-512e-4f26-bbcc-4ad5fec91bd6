package poker

import (
	"fmt"
	"testing"
)

func TestAnalyseTexasHand(t *testing.T) {
	cards := makeCards(1, 2)
	cards[0].Suit = Diamond
	cards[1].Suit = Diamond
	board := makeCards(3, 4, 5, 8, 9)
	board[0].Suit = Diamond
	hand := AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(3, 3)
	board = makeCards(3, 4, 5, 8, 9)
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(3, 3)
	board = makeCards(3, 4, 5, 8, 1)
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(1, 5)
	cards[0].Suit = Diamond
	cards[1].Suit = Diamond
	board = makeCards(11, 10, 12, 13, 8)
	board[0].Suit = Diamond
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(1, 5)
	board = makeCards(10, 11, 12, 13, 8)
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(9, 5)
	board = makeCards(10, 11, 12, 13, 8)
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(1, 2)
	board = makeCards(3, 4, 5, 8, 9)
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)

	cards = makeCards(1, 12)
	cards[0].Suit = Diamond
	cards[1].Suit = Diamond
	board = makeCards(8, 8, 9, 9, 13)
	board[0].Suit = Diamond
	hand = AnalyseTexasHand(cards, board)
	fmt.Println(hand.Level, hand.Score, hand.Cards, hand.Desc)
}

func makeCards(keys ...int) Cards {
	cards := Cards{}
	for _, key := range keys {
		cards = append(cards, Card{Key: key})
	}
	return cards
}
