package poker

import (
	"github.com/spf13/cast"
	"hamster/pkg/poker/holdem"
	"math/rand"
	"time"
)

type Suit string

const (
	Spade   Suit = "spade"
	Heart   Suit = "heart"
	Club    Suit = "club"
	Diamond Suit = "diamond"
)

func (s Suit) Index() int {
	switch s {
	case Spade:
		return 0
	case Heart:
		return 1
	case Club:
		return 2
	case Diamond:
		return 3
	}
	return 0
}

var (
	Suits = []Suit{Spade, Heart, Club, Diamond}
)

type Card struct {
	Key  int  `json:"key"`
	Suit Suit `json:"suit"`
}

func (c Card) String() string {
	name := cast.ToString(c.Key)
	switch c.Key {
	case 1:
		name = "A"
	case 11:
		name = "J"
	case 12:
		name = "Q"
	case 13:
		name = "K"
	}
	return name
}

type Cards []Card

func (cards Cards) Shuffle(n int, k int) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := n - 1; i > 0; i -= k {
		j := int(r.Int31n(int32(i + 1)))
		cards.Swap(i, j)
	}
}

func (cards Cards) Swap(i, j int) {
	cards[i], cards[j] = cards[j], cards[i]
}

func (cards *Cards) Pop(size int) Cards {
	if size > len(*cards) {
		size = len(*cards)
	}
	pop := (*cards)[:size]
	*cards = (*cards)[size:]
	return pop
}

func (cards *Cards) Push(pop Cards) {
	*cards = append(*cards, pop...)
}

func (cards Cards) String() string {
	str := ""
	for _, c := range cards {
		str += c.String() + " "
	}
	return str
}

func InitPokers(filter func(p Card) bool) Cards {
	pokers := Cards{}
	for k := 1; k <= 13; k++ {
		for t := 0; t < 4; t++ {
			p := Card{Key: k, Suit: Suits[t]}
			if filter(p) {
				pokers = append(pokers, p)
			}
		}
	}
	for k := 14; k <= 15; k++ {
		p := Card{Key: k, Suit: ""}
		if filter(p) {
			pokers = append(pokers, p)
		}
	}
	return pokers
}

func InitTexas() Cards {
	return InitPokers(func(p Card) bool {
		return p.Key <= 13
	})
}

type TexasHand struct {
	// Score is the hand score, if level is the same, compare the score
	Score int64 `json:"score"`
	// Level is the hand level
	//皇家同花顺：10
	//同花顺    ：9
	//四条      ：8
	//葫芦      ：7
	//同花      ：6
	//顺子      ：5
	//三条      ：4
	//两对      ：3
	//一对      ：2
	//高牌      ：1
	Level int `json:"level"`
	// Cards is the best 5 cards
	Cards Cards `json:"hand"`
	// Desc is the hand description
	Desc string `json:"desc"`
}

func AnalyseTexasHand(hand, board Cards) TexasHand {
	cards := make(Cards, 0)
	cards = append(cards, hand...)
	cards = append(cards, board...)
	if len(cards) < 7 {
		return TexasHand{}
	}
	holdemCards := [7]holdem.Card{}
	for i, c := range cards {
		val := c.Key
		if val == 1 {
			val = 14
		}
		val <<= 4
		switch c.Suit {
		case Spade:
			val |= 1
		case Club:
			val |= 2
		case Heart:
			val |= 3
		case Diamond:
			val |= 4
		}
		holdemCards[i] = holdem.Card(val)
	}

	score, highHand := holdem.HighestHandValue(holdemCards)
	level := (score >> 20) + 1
	desc := ""

	switch level {
	case 1:
		desc = "High card " + highHand[0].String() + " and " + highHand[1:].String()
	case 2:
		desc = "Pair of " + highHand[0].String() + " and " + highHand[1:].String()
	case 3:
		desc = "Two pairs of " + highHand[0].String() + "," + highHand[2].String() + " and " + highHand[4].String()
	case 4:
		desc = "Three of a kind " + highHand[0].String() + " and " + highHand[3:].String()
	case 5:
		desc = "Straight " + highHand.String()
	case 6:
		desc = "Flush " + highHand.String()
	case 7:
		desc = "Full house " + highHand[0].String() + " and " + highHand[3:].String()
	case 8:
		desc = "Four of a kind " + highHand[0].String() + " and " + highHand[4].String()
	case 9:
		desc = "Straight flush " + highHand.String()
	case 10:
		desc = "Royal flush " + highHand.String()
	}

	return TexasHand{
		Level: int(level),
		Score: int64(score),
		Cards: cards,
		Desc:  desc,
	}
}
