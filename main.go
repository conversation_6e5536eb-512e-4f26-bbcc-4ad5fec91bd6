package main

import (
	"go.uber.org/fx"
	"hamster/config"
	"hamster/internal/api"
	"hamster/internal/db"
	"hamster/internal/handler"
	"hamster/internal/scheduler"
	"hamster/internal/server"
	"hamster/internal/service"
	"hamster/internal/service/authorize"
	"hamster/internal/service/player"
	"hamster/internal/service/table"
	"hamster/pkg/redis"
	"hamster/pkg/util/async"
)

func main() {
	fx.New(
		fx.Provide(config.New),
		fx.Provide(db.New),
		fx.Provide(redis.New),
		fx.Provide(service.New, authorize.New, table.New, player.New),
		fx.Provide(server.NewHTTPServer, server.NewTCPServer, server.NewWebsocketServer),
		fx.Provide(api.New, handler.New, scheduler.New),
		fx.Invoke(func(http *server.HTTPServer, tcp *server.TCPServer, wss *server.WebsocketServer, sch *scheduler.Scheduler) {
			async.Go(http.Serve, tcp.Serve, wss.Serve, sch.Run)
		}),
	).Run()
}
