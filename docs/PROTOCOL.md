## 网络协议
目前服务器同时支持TCP和WebSocket协议，客户端可以选择使用其中一种协议连接服务器。客户端和服务器通过事件进行通讯。
### TCP
协议由数据长度以及数据载体构成，其中数据长度size以大端序4个字节表示，向后size个字节为数据载体。数据载体为JSON格式的事件。
### WebSocket
直接传输JSON格式的事件。

## 事件结构
```
{
    "id": "1736675305850",
    "src_id": "1736675305847",
    "t": "table_state",
    ...data fields...
}
```
- id: 它是事件的唯一标识符。这用于确保事件按正确的顺序处理。
- src_id: 它是事件的源标识符。这用于确定事件的来源。例如Pong的src_id是Ping的id。
- t: 表示事件类型的字符串。其余字段与事件类型相关。

其中，从端到端来看，事件分为客户端和服务器，客户端事件是由客户端发出的，服务器事件是由服务器发出的。

### 客户端事件
- ping: 用于心跳，服务器返回pong事件。
- authorize: 用于认证，服务器返回authorized事件。
- get_tables: 获取table列表，服务器返回tables事件。
- get_table_state: 获取table状态，服务器返回table_state事件。
- get_player_state: 获取player状态，服务器返回player_state事件。
- get_game_state:获取game状态，服务器返回game_state事件。
- join_table: 加入房间，服务器返回go_to_table，table_state事件，广播joined_table事件。
- leave_table: 客户端发送leave_table事件，广播left_table事件。
- join_waiting_list: 加入等待队列，广播joined_waiting_list事件。
- leave_waiting_list: 离开等待队列，广播left_waiting_list事件。
- sit_down: 落座, 服务器返回buy_in_prompt事件。
- buy_in: 买入。
- stand_up: 站起，成功后服务器广播stood事件。
- bet: 下注，服务器广播player_bet事件。
- send_message: 发送聊天消息，服务器广播chat事件。

事件具体结构参考`/internal/event/client.go`以及`/internal/event/server.go`文件。

### 服务器事件
基于客户端事件的额外补充: 
- go_to_table: 服务器通知客户端前往table，一般在加入房间成功后向客户端发出这个事件。
- bet_prompt: 当轮到某个玩家下注时，服务器广播bet_prompt事件，对应玩家应再规定时限内下注。
- buy_in_prompt: 提示玩家买入，玩家应再规定时限内买入。
- chat: 广播聊天消息。

## 认证
客户端连接服务器后，需要立刻发送authorize事件进行认证。服务器返回authorized事件表示认证成功，否则服务器会在5s内强制断开连接。

认证使用基于Redis的会话令牌系统。客户端需要通过前端认证流程获取session_token，然后直接在authorize事件中发送：

```json
{
  "id": 1234567890,
  "t": "authorize",
  "session_token": "your-session-token-here"
}
```

服务器将验证session_token是否在Redis中存在且未过期，验证成功后返回authorized事件。


## 心跳
客户端需要定时发送ping事件，服务器返回pong事件。如果客户端在一定事件内没有发送ping事件，服务器会强制断开连接。

## Demo
在`/client`目录下有一个简单的客户端示例，可以用于测试服务器。

## 流程图
### 认证和心跳
```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Client->>Server: authorize
    Server-->>Client: authorized
    loop Heartbeat
        Client->>Server: ping
        Server-->>Client: pong
    end
```
### 加入房间
```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Client->>Server: get_tables
    Server-->>Client: tables
    
    Client->>Server: join_table
    Server-->>Client: go_to_table, broadcast(table_state, game_state)
    
    Client->>Server: join_waiting_list
    Server-->>Client: broadcast(table_state, joined_waiting_list)
    
    Client->>Server: leave_waiting_list
    Server-->>Client: broadcast(table_state, left_waiting_list)
    
    Client->>Server: sit_down
    Server-->>Client: buy_in_prompt, broadcast(table_state, seated)
    
    Client->>Server: buy_in
    Server-->>Client: broadcast(table_state)
    
    Client->>Server: send_message
    Server-->>Client: broadcast(chat)
 
    Client->>Server: stand_up
    Server-->>Client: broadcast(table_state, stood)
    
    Client->>Server: leave_table
    Server-->>Client: broadcast(table_state, left_table)
```

### 游戏流程
```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Server-->>Client: broadcast(game_start_countdown)
    
    loop Bet
        Server->>Client: broadcast(game_state)
        Server->>Client: broadcast(bet_prompt)
        Client->>Server: bet
        Server-->>Client: broadcast(game_state,player_bet)
    end
    
    Server-->>Client: showdown_prompt
    Client->>Server: showdown

    Server-->>Client: broadcast(table_state)
```