## 20250123
- Bugs fix
- `Table`结构变动，增加seated字段，用于记录当前落座玩家数。增加avg pot.

## 20250122
- `GameSeat.Hand`字段变动:
  - 新增`cards`字段，用于展示当前玩家最好的牌型。
  - 新增`desc`字段，用于展示当前玩家最好的牌型描述, eg. Royal flush A K Q J 10。
    (参考: `/pkg/poker/poker.go`文件中的`TexasHand`结构)

## 20250120
- 新增`game_start_countdown`服务器事件，用于通知客户端游戏开始倒计时。
- 新增`showdown_prompt`服务器事件，用于结算后通知客户端摊牌。
- 新增`showdown`客户端事件，用于摊牌。
- `GameSeat`字段变动，新增`cards`,`hand`字段，用于展示玩家手牌和最终牌型，只有showdown的玩家才会展示这两个字段。

## 20250119
- `GameState`字段变动:
  - 重命名`max_bets`字段为`max_bet`。
- `GameSeat`字段变动:
  - 新增`current_bet`字段，用于记录当前回合玩家的下注额。
  - 新增`current_action`字段，用于记录当前回合玩家的动作。
  - 新增`total_bet`字段，用于记录玩家当前游戏总下注额。
  - 新增`winnings`字段，用于记录玩家当前游戏总赢取额。
  - 移除`bets`,`pot`字段。
- 新增游戏间隔和回合间隔时间设置，详情请看`/internal/consts/consts.go`文件。

## 20250118
- 新增`buy_in_prompt`服务器事件，用于提示玩家在sit_down后买入。
- 新增`buy_in`客户端事件，用于买入。
- 重构`player_state`, `table_state`, `game_state`事件, 详情请看`/internal/event/server.go`文件。
- 修改`table`结构，新增`total_hands`字段，用于记录总对局数，新增`total_pots`字段，用于记录总奖池数。

## 20250116
- `send_message`事件修改为传递code，code对应的消息映射参考`/internal/consts/consts.go`文件。
- `get_tables`事件新增code字段，用于查询指定code的table。
- 新增`go_to_table`服务器事件，用于通知客户端前往table。
- 新增`get_table`事件，用于获取指定table的信息。
- 优化客户端短线重连，主动退出等后续逻辑。