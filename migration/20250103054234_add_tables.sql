-- Create "tables" table
CREATE TABLE "tables" ("id" serial NOT NULL, "name" character varying NOT NULL, "code" character varying NOT NULL, "type" character varying NOT NULL DEFAULT '6p', "status" character varying NOT NULL DEFAULT 'active', "big_blind" bigint NOT NULL, "small_blind" bigint NOT NULL, "is_visible" boolean NOT NULL DEFAULT true, "expired_at" timestamptz NULL, "created_at" timestamptz NOT NULL, "created_by" character varying NULL, "updated_by" character varying NULL, "updated_at" timestamptz NOT NULL, "deleted_at" timestamptz NULL, PRIMARY KEY ("id"));
-- Create index "tables_code" to table: "tables"
CREATE UNIQUE INDEX "tables_code" ON "tables" ("code");
