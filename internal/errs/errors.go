package errs

var (
	ErrInternalError          = New("INTERNAL_ERROR", "internal error")
	ErrUnauthorized           = New("UNAUTHORIZED", "unauthorized")
	ErrAccessForbidden        = New("ACCESS_FORBIDDEN", "access forbidden")
	ErrGetTablesFailed        = New("GET_TABLES_FAILED", "get tables failed")
	ErrGetTableFailed         = New("GET_TABLE_FAILED", "get table failed")
	ErrCreateTableFailed      = New("CREATE_TABLE_FAILED", "create table failed")
	ErrUpdateTableFailed      = New("UPDATE_TABLE_FAILED", "update table failed")
	ErrGetPlayerFailed        = New("GET_PLAYER_FAILED", "get player failed")
	ErrCreatePlayerFailed     = New("CREATE_PLAYER_FAILED", "create player failed")
	ErrUpdatePlayerFailed     = New("UPDATE_PLAYER_FAILED", "update player failed")
	ErrInvalidSeatNum         = New("INVALID_SEAT_NUM", "invalid seat number")
	ErrSeatAlreadyTaken       = New("SEAT_ALREADY_TAKEN", "seat already taken")
	ErrInvalidUserState       = New("INVALID_PLAYER_STATE", "invalid player state")
	ErrJoinWaitingListFailed  = New("JOIN_WAITING_LIST_FAILED", "join waiting list failed")
	ErrPopWaitingListFailed   = New("POP_WAITING_LIST_FAILED", "pop waiting list failed")
	ErrPlayerNotSeated        = New("PLAYER_NOT_SEATED", "player not seated")
	ErrLeaveWaitingListFailed = New("LEAVE_WAITING_LIST_FAILED", "leave waiting list failed")
	ErrPlayerNotInWaitingList = New("PLAYER_NOT_IN_WAITING_LIST", "player not in waiting list")
	ErrPlayerNotInTable       = New("PLAYER_NOT_IN_TABLE", "player not in table")
	ErrInvalidSeat            = New("INVALID_SEAT", "invalid seat")
	ErrPlayerAlreadyInTable   = New("PLAYER_ALREADY_IN_TABLE", "player already in table")
	ErrInvalidTableID         = New("INVALID_TABLE_ID", "invalid table id")
	ErrInvalidTableState      = New("INVALID_TABLE_STATE", "invalid table state")
	ErrInvalidGameState       = New("INVALID_GAME_STATE", "invalid game state")
	ErrPlayerAlreadySeated    = New("PLAYER_ALREADY_SEATED", "player already seated")
	ErrUnsupportedBetAction   = New("UNSUPPORTED_BET_ACTION", "unsupported bet action")
	ErrInsufficientBalance    = New("INSUFFICIENT_BALANCE", "insufficient balance")
	ErrInvalidAllIn           = New("INVALID_ALL_IN", "invalid all in")
	ErrInvalidCheck           = New("INVALID_CHECK", "invalid check")
	ErrInvalidRaise           = New("INVALID_RAISE", "invalid raise")
	ErrInvalidPlayerState     = New("INVALID_PLAYER_STATE", "invalid player state")
	ErrInvalidBuyIn           = New("INVALID_BUY_IN", "invalid buy in")
	ErrInvalidCall            = New("INVALID_CALL", "invalid call")
	ErrInvalidBet             = New("INVALID_BET", "invalid bet")
	ErrWaitingListFull        = New("WAITING_LIST_FULL", "waiting list full")
	ErrInvalidBuyInAmount     = New("INVALID_BUY_IN_AMOUNT", "invalid buy in amount")
	ErrInvalidBuyInAction     = New("INVALID_BUY_IN_ACTION", "invalid buy in action")
)

type Error struct {
	Code    string
	Message string
}

func New(code string, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

func (e *Error) Error() string {
	return e.Message
}

func (e *Error) WithMessage(message string) *Error {
	e.Message += ";" + message
	return e
}
