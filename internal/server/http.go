package server

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	cors "github.com/rs/cors/wrapper/gin"
	"go.uber.org/fx"
	"hamster/config"
	apix "hamster/internal/api"
	"hamster/pkg/log"
	"net/http"
	"time"
)

const (
	_defaultReadHeaderTimeout = 3 * time.Second
	_defaultShutdownTimeout   = 15 * time.Second
)

type HTTPServer struct {
	server *http.Server
	api    *apix.API
}

func NewHTTPServer(lc fx.Lifecycle, cfg config.Config, api *apix.API) (*HTTPServer, error) {
	// init gin engine.
	r := gin.Default()

	// default middlewares.
	r.Use(cors.Default())
	r.Use(gin.CustomRecovery(func(c *gin.Context, err any) {
		apix.Fail(c, errors.New(fmt.Sprintf("%v", err)))
	}))
	// todo authorize

	// Session-based authentication - no HTTP endpoints needed for auth
	// All authentication is handled via WebSocket/TCP with session tokens

	s := &HTTPServer{
		server: &http.Server{
			Addr:              fmt.Sprintf("%s:%d", cfg.HTTP.Host, cfg.HTTP.Port),
			Handler:           r,
			ReadTimeout:       time.Duration(cfg.HTTP.ReadTimeout) * time.Second,
			WriteTimeout:      time.Duration(cfg.HTTP.WriteTimeout) * time.Second,
			ReadHeaderTimeout: _defaultReadHeaderTimeout,
		},
		api: api,
	}
	lc.Append(fx.Hook{OnStop: s.Shutdown})
	return s, nil
}

func (s *HTTPServer) Serve() error {
	go func() { log.Fatal(s.server.ListenAndServe()) }()
	log.Infof("HTTP server is listening on %s", s.server.Addr)
	return nil
}

func (s *HTTPServer) Shutdown(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, _defaultShutdownTimeout)
	defer cancel()

	return s.server.Shutdown(ctx)
}
