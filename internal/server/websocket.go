package server

import (
	"fmt"
	"github.com/gorilla/websocket"
	"hamster/config"
	"hamster/internal/handler"
	netx "hamster/internal/net"
	"hamster/pkg/log"
	"net/http"
)

type WebsocketServer struct {
	addr     string
	upgrader websocket.Upgrader
	handler  *handler.Handler
}

func NewWebsocketServer(cfg config.Config, handler *handler.Handler) *WebsocketServer {
	return &WebsocketServer{
		addr: fmt.Sprintf("%s:%d", cfg.Websocket.Host, cfg.Websocket.Port),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  cfg.Websocket.ReadBufferSize,
			WriteBufferSize: cfg.Websocket.WriteBufferSize,
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
		handler: handler,
	}
}

func (w WebsocketServer) Serve() error {
	http.HandleFunc("/ws", w.serveWs)
	log.Infof("Websocket server listener on %s", w.addr)
	return http.ListenAndServe(w.addr, nil)
}

func (w WebsocketServer) serveWs(rw http.ResponseWriter, r *http.Request) {
	conn, err := w.upgrader.Upgrade(rw, r, nil)
	if err != nil {
		log.Error(err)
		return
	}
	w.handler.Handle(r.Context(), netx.NewWebsocketReadWriteCloser(conn))
}
