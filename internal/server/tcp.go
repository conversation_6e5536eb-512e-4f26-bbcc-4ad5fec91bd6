package server

import (
	"context"
	"fmt"
	"hamster/config"
	"hamster/internal/handler"
	netx "hamster/internal/net"
	"hamster/pkg/log"
	"net"
)

type TCPServer struct {
	addr                            string
	readBufferSize, writeBufferSize int
	handler                         *handler.Handler
}

func NewTCPServer(cfg config.Config, handler *handler.Handler) *TCPServer {
	return &TCPServer{
		addr:            fmt.Sprintf("%s:%d", cfg.TCP.Host, cfg.TCP.Port),
		readBufferSize:  cfg.TCP.ReadBufferSize,
		writeBufferSize: cfg.TCP.WriteBufferSize,
		handler:         handler,
	}
}

func (t TCPServer) Serve() error {
	l, err := net.Listen("tcp", t.addr)
	if err != nil {
		log.Error(err)
		return err
	}
	log.Infof("TCP server listening on %s", t.addr)
	for {
		conn, err := l.Accept()
		if err != nil {
			log.Errorf("listener.Accept err %v", err)
			continue
		}
		t.handler.<PERSON>(context.Background(), netx.NewTcpReadWriteCloser(conn, t.readBufferSize))
	}
}
