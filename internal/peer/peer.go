package peer

import (
	"encoding/json"
	"github.com/tidwall/sjson"
	"hamster/internal/consts"
	"hamster/internal/event"
	net2 "hamster/internal/net"
	"hamster/pkg/log"
	"sync"
	"time"
)

type Peer struct {
	net2.ReadWriteCloser
	sync.Mutex
	hooks map[event.Type]*Hook

	ID string `json:"id"`
}

func New(c net2.ReadWriteCloser, id string) *Peer {
	return &Peer{
		ReadWriteCloser: c,
		ID:              id,
		hooks:           make(map[event.Type]*Hook),
	}
}

func (p *Peer) Write(event event.Event) {
	p.Lock()
	defer p.Unlock()

	bs, err := json.Marshal(event)
	if err != nil {
		log.Error(err)
		return
	}

	bs, err = sjson.SetBytes(bs, "t", event.Type())
	if err != nil {
		log.Error(err)
		return
	}

	err = p.ReadWriteCloser.Write(&net2.Packet{
		Size: uint32(len(bs)),
		Body: bs,
	})
	if err != nil {
		log.Error(err)
		return
	}
}

func (p *Peer) Hook(t event.Type) (*Hook, bool) {
	p.Lock()
	defer p.Unlock()

	ch, ok := p.hooks[t]
	return ch, ok
}

func (p *Peer) AddHook(t event.Type, size int) *Hook {
	p.Lock()
	defer p.Unlock()

	h := &Hook{
		ch:    make(chan *net2.Packet, size),
		event: t,
		peer:  p,
	}
	p.hooks[t] = h
	return h
}

func (p *Peer) Listening() error {
	type emptyEvent struct {
		T event.Type `json:"t"`
	}
	for {
		err := p.SetReadDeadline(time.Now().Add(consts.PingTimeout))
		if err != nil {
			return err
		}

		packet, err := p.Read()
		if err != nil {
			return err
		}

		empty := emptyEvent{}
		err = json.Unmarshal(packet.Body, &empty)
		if err != nil {
			return err
		}

		events := []event.Type{empty.T, event.TypeAll}
		for _, ev := range events {
			if hook, ok := p.Hook(ev); ok {
				select {
				case hook.ch <- packet:
				default:
				}
			}
		}
	}
}

func (p *Peer) Close() {
	p.Lock()
	defer p.Unlock()

	for _, h := range p.hooks {
		h.Close()
	}
	_ = p.ReadWriteCloser.Close()
}

type Hook struct {
	sync.Mutex
	peer   *Peer
	ch     chan *net2.Packet
	event  event.Type
	closed bool
}

func (h *Hook) OnPacket() <-chan *net2.Packet {
	return h.ch
}

func (h *Hook) Close() {
	h.Lock()
	defer h.Unlock()

	if !h.closed {
		close(h.ch)
		delete(h.peer.hooks, h.event)
	}
	h.closed = true
}
