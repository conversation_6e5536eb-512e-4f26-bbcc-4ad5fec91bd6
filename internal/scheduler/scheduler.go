package scheduler

import (
	"hamster/internal/handler"
	"hamster/internal/service"
	"hamster/internal/states"
	"time"
)

type Scheduler struct {
	s         *service.Service
	h         *handler.Handler
	ringgames map[uint64]*states.States
}

func New(s *service.Service, h *handler.Handler) *Scheduler {
	return &Scheduler{s: s, h: h, ringgames: make(map[uint64]*states.States)}
}

func (s *Scheduler) Run() error {
	for {
		tables := s.s.Table.List()

		tableIdx := make(map[uint64]bool)
		for _, t := range tables {
			tableIdx[t.ID] = true
			if _, ok := s.ringgames[t.ID]; !ok {
				s.ringgames[t.ID] = states.New(s.s, s.h, t)
				s.ringgames[t.ID].Run()
			}
		}

		for id, game := range s.ringgames {
			if !tableIdx[id] {
				game.Stop()
			}
			if game.Stopped() {
				delete(s.ringgames, id)
			}
		}

		time.Sleep(2 * time.Second)
	}
}
