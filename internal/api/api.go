package api

import (
	"github.com/gin-gonic/gin"
	"hamster/internal/errs"
	"hamster/internal/service"
)

type API struct {
	svc *service.Service
}

func New(svc *service.Service) *API {
	return &API{svc: svc}
}

type Response struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

func Success(c *gin.Context, data any) {
	c.JSON(200, &Response{Code: "SUCCESS", Msg: "success", Data: data})
}

func Fail(c *gin.Context, err error) {
	if e, ok := err.(*errs.Error); ok {
		c.JSON(200, &Response{Code: e.Code, Msg: e.Message})
		return
	}
	c.JSON(200, &Response{Code: errs.ErrInternalError.Code, Msg: err.Error()})
}
