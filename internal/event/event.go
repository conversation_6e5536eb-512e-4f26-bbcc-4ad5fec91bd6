package event

import (
	"github.com/modern-go/reflect2"
	"hamster/pkg/util/timex"
)

type Type string

const (
	TypeAll    Type = "all"
	TypeSystem Type = "system"
)

type State string

const (
	Success = "success"
	Error   = "error"
)

type Event interface {
	ID() uint64
	Type() Type
}

type Base struct {
	I uint64 `json:"id"`
	T Type   `json:"t"`
}

func (e Base) ID() uint64 { return e.I }
func (e Base) Type() Type { return e.T }

type Response struct {
	I       uint64 `json:"id"`
	T       Type   `json:"t"`
	SrcID   uint64 `json:"src_id,omitempty"`
	State   State  `json:"state"`
	Message string `json:"message"`
}

func NewResponse(src Event, state State, message string) Response {
	return Response{
		I:       timex.UnixMilli(),
		T:       GetType(src),
		SrcID:   GetID(src),
		State:   state,
		Message: message,
	}
}

func NewErrorResponse(src Event, err error) Response {
	return NewResponse(src, Error, err.Error())
}

func NewSuccessResponse(src Event) Response {
	return NewResponse(src, Success, "success")
}

func (e Response) ID() uint64 { return e.I }
func (e Response) Type() Type { return e.T }

func GetID(e Event) uint64 {
	if !reflect2.IsNil(e) {
		return e.ID()
	}
	return 0
}

func GetType(e Event) Type {
	if !reflect2.IsNil(e) {
		return e.Type()
	}
	return TypeSystem
}
