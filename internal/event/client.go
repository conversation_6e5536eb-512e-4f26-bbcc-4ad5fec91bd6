package event

import (
	"hamster/internal/model"
	"hamster/pkg/util/timex"
)

const (
	TypePing             Type = "ping"
	TypeAuthorize        Type = "authorize"
	TypeGetTable         Type = "get_table"
	TypeGetTables        Type = "get_tables"
	TypeGetTableState    Type = "get_table_state"
	TypeGetPlayerState   Type = "get_player_state"
	TypeGetGameState     Type = "get_game_state"
	TypeJoinTable        Type = "join_table"
	TypeJoinWaitingList  Type = "join_waiting_list"
	TypeLeaveWaitingList Type = "leave_waiting_list"
	TypeLeaveTable       Type = "leave_table"
	TypeSitDown          Type = "sit_down"
	TypeStandUp          Type = "stand_up"
	TypeBet              Type = "bet"
	TypeBuyIn            Type = "buy_in"
	TypeSendMessage      Type = "send_message"
	TypeShowdown         Type = "showdown"
)

type PingEvent struct {
	I uint64 `json:"id"`
}

func NewPingEvent() PingEvent {
	return PingEvent{
		I: timex.UnixMilli(),
	}
}

func (p PingEvent) ID() uint64 { return p.I }
func (p PingEvent) Type() Type { return TypePing }

type AuthorizeEvent struct {
	I            uint64 `json:"id"`
	SessionToken string `json:"session_token"`
}

func NewAuthorizeEvent(sessionToken string) AuthorizeEvent {
	return AuthorizeEvent{
		I:            timex.UnixMilli(),
		SessionToken: sessionToken,
	}
}

func (a AuthorizeEvent) ID() uint64 { return a.I }
func (a AuthorizeEvent) Type() Type { return TypeAuthorize }

type GetTablesEvent struct {
	I    uint64  `json:"id"`
	Code *string `json:"code,omitempty"`
}

func NewGetTablesEvent() GetTablesEvent {
	return GetTablesEvent{
		I: timex.UnixMilli(),
	}
}

func (g GetTablesEvent) ID() uint64 { return g.I }
func (g GetTablesEvent) Type() Type { return TypeGetTables }

type GetTableEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewGetTableEvent(tableID uint64) GetTableEvent {
	return GetTableEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (g GetTableEvent) ID() uint64 { return g.I }
func (g GetTableEvent) Type() Type { return TypeGetTable }

type GetPlayerStateEvent struct {
	I uint64 `json:"id"`
}

func NewGetPlayerStateEvent() GetPlayerStateEvent {
	return GetPlayerStateEvent{
		I: timex.UnixMilli(),
	}
}

func (g GetPlayerStateEvent) ID() uint64 { return g.I }
func (g GetPlayerStateEvent) Type() Type { return TypeGetPlayerState }

type GetGameStateEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewGetGameStateEvent(tableID uint64) GetGameStateEvent {
	return GetGameStateEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (g GetGameStateEvent) ID() uint64 { return g.I }
func (g GetGameStateEvent) Type() Type { return TypeGetGameState }

type GetTableStateEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewGetTableStateEvent(tableID uint64) GetTableStateEvent {
	return GetTableStateEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (g GetTableStateEvent) ID() uint64 { return g.I }
func (g GetTableStateEvent) Type() Type { return TypeGetTableState }

type JoinTableEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewJoinTableEvent(tableID uint64) JoinTableEvent {
	return JoinTableEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (j JoinTableEvent) ID() uint64 { return j.I }
func (j JoinTableEvent) Type() Type { return TypeJoinTable }

type LeaveTableEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewLeaveTableEvent(tableID uint64) LeaveTableEvent {
	return LeaveTableEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (l LeaveTableEvent) ID() uint64 { return l.I }
func (l LeaveTableEvent) Type() Type { return TypeLeaveTable }

type LeaveWaitingListEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewLeaveWaitingListEvent(tableID uint64) LeaveWaitingListEvent {
	return LeaveWaitingListEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (l LeaveWaitingListEvent) ID() uint64 { return l.I }
func (l LeaveWaitingListEvent) Type() Type { return TypeLeaveWaitingList }

type SitDownEvent struct {
	I       uint64 `json:"id"`
	Seat    int    `json:"seat"`
	TableID uint64 `json:"table_id"`
}

func NewSitDownEvent(tableID uint64, seat int) SitDownEvent {
	return SitDownEvent{
		I:       timex.UnixMilli(),
		Seat:    seat,
		TableID: tableID,
	}
}

func (s SitDownEvent) ID() uint64 { return s.I }
func (s SitDownEvent) Type() Type { return TypeSitDown }

type StandUpEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewStandUpEvent(tableId uint64) StandUpEvent {
	return StandUpEvent{
		I:       timex.UnixMilli(),
		TableID: tableId,
	}
}

func (l StandUpEvent) ID() uint64 { return l.I }
func (l StandUpEvent) Type() Type { return TypeStandUp }

type JoinWaitingListEvent struct {
	I       uint64 `json:"id"`
	TableID uint64 `json:"table_id"`
}

func NewJoinWaitingListEvent(tableID uint64) JoinWaitingListEvent {
	return JoinWaitingListEvent{
		I:       timex.UnixMilli(),
		TableID: tableID,
	}
}

func (j JoinWaitingListEvent) ID() uint64 { return j.I }
func (j JoinWaitingListEvent) Type() Type { return TypeJoinWaitingList }

type BetEvent struct {
	I      uint64          `json:"id"`
	Action model.BetAction `json:"action"`
	Amount uint64          `json:"amount"`
}

func NewBetEvent(action model.BetAction, amount uint64) BetEvent {
	return BetEvent{
		I:      timex.UnixMilli(),
		Action: action,
		Amount: amount,
	}
}

func (a BetEvent) ID() uint64 { return a.I }
func (a BetEvent) Type() Type { return TypeBet }

type BuyInAction string

const (
	BuyInActionBuyIn BuyInAction = "buy_in"
	BuyInActionLeave BuyInAction = "leave"
)

type BuyInEvent struct {
	I      uint64      `json:"id"`
	Action BuyInAction `json:"action"`
	Amount uint64      `json:"amount"`
}

func NewBuyInEvent(action BuyInAction, amount uint64) BuyInEvent {
	return BuyInEvent{
		I:      timex.UnixMilli(),
		Action: action,
		Amount: amount,
	}
}

func (b BuyInEvent) ID() uint64 { return b.I }
func (b BuyInEvent) Type() Type { return TypeBuyIn }

type SendMessageEvent struct {
	I    uint64 `json:"id"`
	Code uint16 `json:"code"`
}

func NewSendMessageEvent(code uint16) SendMessageEvent {
	return SendMessageEvent{
		I:    timex.UnixMilli(),
		Code: code,
	}
}

func (c SendMessageEvent) ID() uint64 { return c.I }
func (c SendMessageEvent) Type() Type { return TypeSendMessage }

type ShowdownEvent struct {
	I uint64 `json:"id"`
}

func NewShowdownEvent() ShowdownEvent {
	return ShowdownEvent{
		I: timex.UnixMilli(),
	}
}

func (s ShowdownEvent) ID() uint64 { return s.I }
func (s ShowdownEvent) Type() Type { return TypeShowdown }
