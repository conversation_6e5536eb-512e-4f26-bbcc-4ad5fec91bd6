package event

import (
	"hamster/internal/db/ent/tables"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"hamster/pkg/util/ptr"
	"hamster/pkg/util/timex"
	"time"
)

const (
	TypePong               Type = "pong"
	TypeAuthorized         Type = "authorized"
	TypeTables             Type = "tables"
	TypeTable              Type = "table"
	TypeTableState         Type = "table_state"
	TypeGameState          Type = "game_state"
	TypePlayerState        Type = "player_state"
	TypeChat               Type = "chat"
	TypePlayerBet          Type = "player_bet"
	TypeSeated             Type = "seated"
	TypeStood              Type = "stood"
	TypeGoToTable          Type = "go_to_table"
	TypeJoinedTable        Type = "joined_table"
	TypeLeftTable          Type = "left_table"
	TypeJoinedWaitingList  Type = "joined_waiting_list"
	TypeLeftWaitingList    Type = "left_waiting_list"
	TypeBetPrompt          Type = "bet_prompt"
	TypeBuyInPrompt        Type = "buy_in_prompt"
	TypeGameStartCountdown Type = "game_start_countdown"
	TypeShowdownPrompt     Type = "showdown_prompt"
)

type PongEvent struct {
	I     uint64 `json:"id"`
	SrcID uint64 `json:"src_id,omitempty"`
}

func NewPongEvent(src Event) PongEvent {
	return PongEvent{
		I:     timex.UnixMilli(),
		SrcID: GetID(src),
	}
}

func (p PongEvent) ID() uint64 { return p.I }
func (p PongEvent) Type() Type { return TypePong }

type AuthorizedEvent struct {
	I     uint64 `json:"id"`
	SrcID uint64 `json:"src_id,omitempty"`
}

func NewAuthorizedEvent(src Event) AuthorizedEvent {
	return AuthorizedEvent{
		I:     timex.UnixMilli(),
		SrcID: GetID(src),
	}
}

func (a AuthorizedEvent) ID() uint64 { return a.I }
func (a AuthorizedEvent) Type() Type { return TypeAuthorized }

type JoinedTableEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	TableID  uint64 `json:"table_id"`
	PlayerID string `json:"player_id"`
}

func NewJoinedTableEvent(src Event, tableID uint64, playerID string) JoinedTableEvent {
	return JoinedTableEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
	}
}

func (g JoinedTableEvent) ID() uint64 { return g.I }
func (g JoinedTableEvent) Type() Type { return TypeJoinedTable }

type GoToTableEvent struct {
	I     uint64 `json:"id"`
	SrcID uint64 `json:"src_id,omitempty"`
	Table *Table `json:"table"`
}

func NewGoToTableEvent(src Event, table *model.Table) GoToTableEvent {
	return GoToTableEvent{
		I:     timex.UnixMilli(),
		SrcID: GetID(src),
		Table: ToTable(table),
	}
}

func (g GoToTableEvent) ID() uint64 { return g.I }
func (g GoToTableEvent) Type() Type { return TypeGoToTable }

type TableStateEvent struct {
	I          uint64            `json:"id"`
	SrcID      uint64            `json:"src_id,omitempty"`
	TableState *model.TableState `json:"table_state"`
}

func NewTableStateEvent(src Event, ts *model.TableState) TableStateEvent {
	return TableStateEvent{
		I:          timex.UnixMilli(),
		SrcID:      GetID(src),
		TableState: ts,
	}
}

func (t TableStateEvent) ID() uint64 { return t.I }
func (t TableStateEvent) Type() Type { return TypeTableState }

type TablesEvent struct {
	I      uint64   `json:"id"`
	SrcID  uint64   `json:"src_id,omitempty"`
	Tables []*Table `json:"tables"`
}

type Table struct {
	ID         uint64      `json:"id"`
	Name       string      `json:"n"`
	Code       string      `json:"c"`
	Type       tables.Type `json:"t"`
	BigBlind   uint64      `json:"bb"`
	SmallBlind uint64      `json:"sb"`
	ExpiredAt  *time.Time  `json:"ea"`
	MinBuyIn   uint64      `json:"nbi"`
	MaxBuyIn   uint64      `json:"xbi"`
	IsVisible  bool        `json:"iv"`
	TotalPot   uint64      `json:"tp"`
	TotalHands uint64      `json:"th"`
	AvgPot     uint64      `json:"ap"`
	Seated     uint64      `json:"sd"`
}

func NewTablesEvent(src Event, tables []*model.Table) TablesEvent {
	return TablesEvent{
		I:      timex.UnixMilli(),
		SrcID:  GetID(src),
		Tables: ToTables(tables),
	}
}

func (t TablesEvent) ID() uint64 { return t.I }
func (t TablesEvent) Type() Type { return TypeTables }

type TableEvent struct {
	I     uint64 `json:"id"`
	SrcID uint64 `json:"src_id,omitempty"`
	Table *Table `json:"table"`
}

func NewTableEvent(src Event, table *model.Table) TableEvent {
	return TableEvent{
		I:     timex.UnixMilli(),
		SrcID: GetID(src),
		Table: ToTable(table),
	}
}

func (t TableEvent) ID() uint64 { return t.I }
func (t TableEvent) Type() Type { return TypeTable }

type GameStateEvent struct {
	I         uint64    `json:"id"`
	SrcID     uint64    `json:"src_id,omitempty"`
	GameState GameState `json:"game_state"`
}

type GameState struct {
	TableID uint64 `json:"ti"`
	// State is the current state of the game.
	State string `json:"s"`
	// Round is the current round of the game.
	Round string `json:"r"`
	// Button is the seat index of the button.
	Button int `json:"b"`
	// BB is the seat index of the big blind.
	BB int `json:"bb"`
	// SB is the seat index of the small blind.
	SB int `json:"sb"`
	// Active is the seat index of current turn player.
	Active int `json:"a"`
	// Board is the board cards.
	Board poker.Cards `json:"bd"`
	// Pot is the total pot amount.
	Pot uint64 `json:"pt"`
	// Cards is the current player's cards.
	Cards poker.Cards `json:"c"`
	// MaxBet is the max bet amount.
	MaxBet uint64 `json:"mb"`
	// MinRaise is the min raise amount.
	MinRaise uint64 `json:"mr"`
	// Events is the all events of current events. instance of PlayerBetEvent.
	Events []any `json:"es"`
	// Seats is the all seats of the game.
	Seats []*GameSeat `json:"ss"`
}

type GameSeat struct {
	// PlayerID is the player id.
	PlayerID string `json:"pi"`
	// Seat is the seat index.
	Seat int `json:"s"`
	// Status is the seat status.
	Status model.TableSeatStatus `json:"ss"`
	// Balance is the player buy in balance.
	Balance uint64 `json:"b"`
	// Playing is the player in game.
	Playing bool `json:"p"`
	// AllIn is the player all in status.
	AllIn bool `json:"ai"`
	// Folded is the player folded status.
	Folded bool `json:"f"`
	// TotalBet is the player total bet amount in the game.
	TotalBet uint64 `json:"tb"`
	// CurrentBet is the player current bet amount in current round.
	CurrentBet uint64 `json:"cb"`
	// CurrentAction is the player current bet action.
	CurrentAction model.BetAction `json:"ca"`
	// Winnings is the player winnings pot in the game.
	Winnings uint64 `json:"w"`
	// Hand is the player hand.
	Hand *poker.TexasHand `json:"h,omitempty"`
	// Cards is the player's cards.
	Cards *poker.Cards `json:"cs,omitempty"`
	// Showdown is the player showdown status.
	Showdown bool `json:"sd"`
}

func NewGameStateEvent(src Event, gs *model.GameState, playerId string) GameStateEvent {
	egs := GameState{
		TableID:  gs.TableID,
		State:    gs.State,
		Round:    gs.Round,
		Button:   gs.Button,
		BB:       gs.BB,
		SB:       gs.SB,
		Active:   gs.Active,
		Board:    gs.Board,
		Pot:      gs.Pot,
		MaxBet:   gs.MaxBet,
		MinRaise: gs.MinRaise,
		Events:   gs.Events,
	}
	if p, ok := gs.Players[playerId]; ok {
		egs.Cards = p.Cards
	}
	egs.Seats = make([]*GameSeat, len(gs.Seats))
	for i, s := range gs.Seats {
		egs.Seats[i] = &GameSeat{
			PlayerID: s.PlayerID,
			Seat:     s.Seat,
			Status:   s.Status,
			Balance:  s.Balance,
			Playing:  s.Playing,
		}
		if p, ok := gs.Players[s.PlayerID]; ok {
			egs.Seats[i].AllIn = p.AllIn
			egs.Seats[i].Folded = p.Folded
			egs.Seats[i].Winnings = p.Winnings
			egs.Seats[i].TotalBet = p.TotalBet
			egs.Seats[i].CurrentBet = p.CurrentBet
			egs.Seats[i].CurrentAction = p.CurrentAction
			egs.Seats[i].Showdown = p.Showdown
			if playerId == p.ID || p.Showdown {
				egs.Seats[i].Cards = ptr.Any(p.Cards)
				egs.Seats[i].Hand = ptr.Any(p.Hand)
			}
		}
	}
	return GameStateEvent{
		I:         timex.UnixMilli(),
		SrcID:     GetID(src),
		GameState: egs,
	}
}

func (g GameStateEvent) ID() uint64 { return g.I }
func (g GameStateEvent) Type() Type { return TypeGameState }

type PlayerStateEvent struct {
	I           uint64             `json:"id"`
	SrcID       uint64             `json:"src_id,omitempty"`
	PlayerState *model.PlayerState `json:"player_state"`
}

func NewPlayerStateEvent(src Event, ps *model.PlayerState) PlayerStateEvent {
	return PlayerStateEvent{
		I:           timex.UnixMilli(),
		SrcID:       GetID(src),
		PlayerState: ps,
	}
}

func (p PlayerStateEvent) ID() uint64 { return p.I }
func (p PlayerStateEvent) Type() Type { return TypePlayerState }

type BetPromptEvent struct {
	I        uint64 `json:"id"`
	Timeout  int    `json:"timeout"`
	PlayerID string `json:"player_id"`
}

func NewBetPromptEvent(timeout int, playerId string) BetPromptEvent {
	return BetPromptEvent{
		I:        timex.UnixMilli(),
		Timeout:  timeout,
		PlayerID: playerId,
	}
}

func (b BetPromptEvent) ID() uint64 { return b.I }
func (b BetPromptEvent) Type() Type { return TypeBetPrompt }

type BuyInPromptEvent struct {
	I        uint64 `json:"id"`
	MinBuyIn uint64 `json:"min_buy_in"`
	MaxBuyIn uint64 `json:"max_buy_in"`
	Timeout  int    `json:"timeout"`
}

func NewBuyInPromptEvent(minBuyIn, maxBuyIn uint64, timeout int) BuyInPromptEvent {
	return BuyInPromptEvent{
		I:        timex.UnixMilli(),
		MinBuyIn: minBuyIn,
		MaxBuyIn: maxBuyIn,
		Timeout:  timeout,
	}
}

func (b BuyInPromptEvent) ID() uint64 { return b.I }
func (b BuyInPromptEvent) Type() Type { return TypeBuyInPrompt }

type ChatEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	PlayerID string `json:"player_id"`
	Message  string `json:"message"`
}

func NewChatEvent(src Event, playerID, message string) ChatEvent {
	return ChatEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		PlayerID: playerID,
		Message:  message,
	}
}

func (c ChatEvent) ID() uint64 { return c.I }
func (c ChatEvent) Type() Type { return TypeChat }

type PlayerBetEvent struct {
	I        uint64          `json:"id"`
	PlayerID string          `json:"player_id"`
	Action   model.BetAction `json:"action"`
	Amount   uint64          `json:"amount,omitempty"`
}

func NewPlayerBetEvent(playerID string, action model.BetAction, amount uint64) PlayerBetEvent {
	return PlayerBetEvent{
		I:        timex.UnixMilli(),
		PlayerID: playerID,
		Action:   action,
		Amount:   amount,
	}
}

func (p PlayerBetEvent) ID() uint64 { return p.I }
func (p PlayerBetEvent) Type() Type { return TypePlayerBet }

type SeatedEvent struct {
	I        uint64           `json:"id"`
	SrcID    uint64           `json:"src_id,omitempty"`
	TableID  uint64           `json:"table_id"`
	PlayerID string           `json:"player_id"`
	Seat     *model.TableSeat `json:"seat"`
}

func NewSeatedEvent(src Event, tableID uint64, playerID string, seat *model.TableSeat) SeatedEvent {
	return SeatedEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
		Seat:     seat,
	}
}

func (s SeatedEvent) ID() uint64 { return s.I }
func (s SeatedEvent) Type() Type { return TypeSeated }

type StoodEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	TableID  uint64 `json:"table_id"`
	PlayerID string `json:"player_id"`
	Seat     int    `json:"seat"`
}

func NewStoodEvent(src Event, tableID uint64, playerID string, seat int) StoodEvent {
	return StoodEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
		Seat:     seat,
	}
}

func (s StoodEvent) ID() uint64 { return s.I }
func (s StoodEvent) Type() Type { return TypeStood }

type LeftTableEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	TableID  uint64 `json:"table_id"`
	PlayerID string `json:"player_id"`
}

func NewLeftTableEvent(src Event, tableID uint64, playerID string) LeftTableEvent {
	return LeftTableEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
	}
}

func (l LeftTableEvent) ID() uint64 { return l.I }
func (l LeftTableEvent) Type() Type { return TypeLeftTable }

type JoinedWaitingListEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	TableID  uint64 `json:"table_id"`
	PlayerID string `json:"player_id"`
}

func NewJoinedWaitingListEvent(src Event, tableID uint64, playerID string) JoinedWaitingListEvent {
	return JoinedWaitingListEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
	}
}

func (j JoinedWaitingListEvent) ID() uint64 { return j.I }
func (j JoinedWaitingListEvent) Type() Type { return TypeJoinedWaitingList }

type LeftWaitingListEvent struct {
	I        uint64 `json:"id"`
	SrcID    uint64 `json:"src_id,omitempty"`
	TableID  uint64 `json:"table_id"`
	PlayerID string `json:"player_id"`
}

func NewLeftWaitingListEvent(src Event, tableID uint64, playerID string) LeftWaitingListEvent {
	return LeftWaitingListEvent{
		I:        timex.UnixMilli(),
		SrcID:    GetID(src),
		TableID:  tableID,
		PlayerID: playerID,
	}
}

func (l LeftWaitingListEvent) ID() uint64 { return l.I }
func (l LeftWaitingListEvent) Type() Type { return TypeLeftWaitingList }

type TypeGameStartCountdownEvent struct {
	I       uint64 `json:"id"`
	SrcID   uint64 `json:"src_id,omitempty"`
	Timeout int    `json:"timeout"`
}

func NewGameStartCountdownEvent(src Event, timeout int) TypeGameStartCountdownEvent {
	return TypeGameStartCountdownEvent{
		I:       timex.UnixMilli(),
		SrcID:   GetID(src),
		Timeout: timeout,
	}
}

func (g TypeGameStartCountdownEvent) ID() uint64 { return g.I }
func (g TypeGameStartCountdownEvent) Type() Type { return TypeGameStartCountdown }

type ShowdownPromptEvent struct {
	I       uint64 `json:"id"`
	Timeout int    `json:"timeout"`
}

func NewShowdownPromptEvent(timeout int) ShowdownPromptEvent {
	return ShowdownPromptEvent{
		I:       timex.UnixMilli(),
		Timeout: timeout,
	}
}

func (s ShowdownPromptEvent) ID() uint64 { return s.I }
func (s ShowdownPromptEvent) Type() Type { return TypeShowdownPrompt }
