package event

import (
	"hamster/internal/model"
	"hamster/pkg/util/mathx"
)

func ToTable(t *model.Table) *Table {
	return &Table{
		ID:         t.ID,
		Name:       t.Name,
		Code:       t.Code,
		Type:       t.Type,
		BigBlind:   t.<PERSON>lind,
		SmallBlind: t.<PERSON>,
		ExpiredAt:  t.<PERSON>,
		MinBuyIn:   t.<PERSON>,
		MaxBuyIn:   t.Max<PERSON>uyIn,
		IsVisible:  t.IsVisible,
		TotalPot:   t.TotalPot,
		TotalHands: t.TotalHands,
		AvgPot:     mathx.Div(t.TotalPot, t.TotalHands),
		Seated:     t.TableState.Seated,
	}
}

func ToTables(tables []*model.Table) []*Table {
	result := make([]*Table, 0, len(tables))
	for _, t := range tables {
		result = append(result, ToTable(t))
	}
	return result
}
