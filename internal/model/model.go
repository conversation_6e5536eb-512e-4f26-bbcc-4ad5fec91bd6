package model

import (
	"hamster/internal/db/ent/tables"
	"hamster/pkg/poker"
	"hamster/pkg/util/queue"
	"sync"
	"time"
)

type TableType string

const (
	TableType6P TableType = "6p"
	TableType9P TableType = "9p"
)

func (gt TableType) Count() uint64 {
	switch gt {
	case TableType6P:
		return 6
	case TableType9P:
		return 9
	default:
		return 0
	}
}

type Table struct {
	sync.Mutex
	ID         uint64      `json:"id,omitempty"`
	Name       string      `json:"name,omitempty"`
	Code       string      `json:"code,omitempty"`
	Type       tables.Type `json:"type,omitempty"`
	BigBlind   uint64      `json:"big_blind,omitempty"`
	SmallBlind uint64      `json:"small_blind,omitempty"`
	ExpiredAt  *time.Time  `json:"expired_at,omitempty"`
	MinBuyIn   uint64      `json:"min_buy_in"`
	MaxBuyIn   uint64      `json:"max_buy_in"`
	IsVisible  bool        `json:"is_visible"`
	TotalPot   uint64      `json:"total_pot"`
	TotalHands uint64      `json:"hands"`

	TableState *TableState `json:"-"`
	GameState  *GameState  `json:"-"`
}

func (t *Table) MaxPlayers() int {
	switch t.Type {
	case tables.Type6p:
		return 6
	case tables.Type9p:
		return 9
	}
	return 6
}

type TableState struct {
	TableID uint64 `json:"ti"`
	// Seats is the table seats
	Seats []*TableSeat `json:"ss"`
	// Seated is the seated players count
	Seated uint64 `json:"sd"`
	// Players is the players in the table
	Players map[string]bool `json:"ps"`
	// WaitingList is the waiting list
	WaitingList *queue.Queue[string] `json:"wl"`
}

func (ts *TableState) GetPlayerSeat(playerId string) (*TableSeat, bool) {
	for _, seat := range ts.Seats {
		if seat.PlayerID == playerId {
			return seat, true
		}
	}
	return nil, false
}

func (ts *TableState) GetSeat(seat int) (*TableSeat, bool) {
	if seat < 0 || seat >= len(ts.Seats) {
		return nil, false
	}
	return ts.Seats[seat], true
}

type TableSeatStatus string

const (
	// TableSeatStatusEmpty is the empty seat status
	TableSeatStatusEmpty TableSeatStatus = "empty"
	// TableSeatStatusOccupied is the occupied seat status，player is already buy in
	TableSeatStatusOccupied TableSeatStatus = "occupied"
	// TableSeatStatusReserved is the reserved seat status，player is waiting for buy in
	TableSeatStatusReserved TableSeatStatus = "reserved"
)

type TableSeat struct {
	// PlayerID is the player id
	PlayerID string `json:"pi"`
	// Seat is the seat index
	Seat int `json:"s"`
	// Status is the seat status
	Status TableSeatStatus `json:"ss"`
	// Balance is the player buy in balance
	Balance uint64 `json:"b"`
	// Playing is the player of this seat in game
	Playing bool `json:"p"`
	// Leaved is the player leaved
	Leaved bool `json:"l"`
}

func (ts *TableSeat) SetEmpty() {
	ts.PlayerID = ""
	ts.Status = TableSeatStatusEmpty
	ts.Balance = 0
	ts.Playing = false
	ts.Leaved = true
}

func (ts *TableSeat) SetReserved(playerId string) {
	ts.Status = TableSeatStatusReserved
	ts.PlayerID = playerId
	ts.Playing = false
	ts.Leaved = false
}

func (ts *TableSeat) SetOccupied(playerId string, balance uint64) {
	ts.Status = TableSeatStatusOccupied
	ts.PlayerID = playerId
	ts.Balance = balance
	ts.Playing = false
	ts.Leaved = false
}

type PlayerState struct {
	// ID is the player id
	ID string `json:"id"`
	// TableID is the table id
	TableID uint64 `json:"table_id"`
	// Online is the player online status
	Online bool `json:"online"`
}

type (
	GameStatus string
)

type GameState struct {
	sync.Mutex
	*TableState

	TableID uint64                 `json:"table_id"`
	Players map[string]*GamePlayer `json:"players"`
	Stakes  []uint64               `json:"stakes"`
	PN      uint64                 `json:"pn"`
	Pot     uint64                 `json:"pot"`
	Button  int                    `json:"button"`
	SB      int                    `json:"sb"`
	BB      int                    `json:"bb"`
	Pool    poker.Cards            `json:"pool"`
	Board   poker.Cards            `json:"board"`
	MaxBet  uint64                 `json:"max_bet"`
	Folded  uint64                 `json:"folded"`
	AllIn   uint64                 `json:"all_in"`
	State   string                 `json:"state"`
	Round   string                 `json:"round"`

	// reset every round
	Leader   int    `json:"leader"`
	Active   int    `json:"active"`
	MinRaise uint64 `json:"min_raise"`
	Events   []any  `json:"events"`

	// reset every hand
	LastState *GameState `json:"last_state"`
}

func (gs *GameState) Reset() {
	gs.Leader = -1
	gs.Active = gs.NextTurnPlayer(gs.Button + 1)
	gs.MinRaise = gs.Stakes[1]
	gs.Events = make([]any, 0)
	for _, p := range gs.Players {
		p.CurrentBet = 0
		p.CurrentAction = ""
	}
}

func (gs *GameState) GetPlayer(index int) (*GamePlayer, bool) {
	if s := gs.Seats[index%len(gs.Seats)]; s != nil {
		p, ok := gs.Players[s.PlayerID]
		return p, ok
	}
	return nil, false
}

func (gs *GameState) GetPlayerID(index int) string {
	if p, ok := gs.GetPlayer(index); ok {
		return p.PlayerID
	}
	return ""
}

func (gs *GameState) NextTurnPlayer(from int) int {
	if gs.Folded+gs.AllIn == gs.PN || gs.Folded == gs.PN-1 {
		return -1
	}
	for i := from; i < from+len(gs.Seats); i++ {
		if s, ok := gs.GetPlayer(i); ok && !s.AllIn && !s.Folded && s.Valid() {
			return i % len(gs.Seats)
		}
	}
	return -1
}

func (gs *GameState) Bet(index int, amount uint64, action BetAction) {
	player, ok := gs.GetPlayer(index)
	if !ok || !player.Valid() || player.AllIn || player.Folded {
		return
	}

	player.Bet(amount, action)
	gs.Pot += amount

	if gs.Leader == -1 {
		gs.Leader = index
	}
	if player.Balance == 0 {
		player.AllIn = true
		gs.AllIn++
	}
	if player.TotalBet > gs.MaxBet {
		gs.Leader = index
		gs.MinRaise = player.TotalBet - gs.MaxBet
		gs.MaxBet = player.TotalBet
	}
	if player.AllIn && gs.Leader == index {
		gs.Leader = -1
	}
}

func (gs *GameState) Fold(index int) {
	player, ok := gs.GetPlayer(index)
	if !ok || !player.Valid() || player.AllIn || player.Folded {
		return
	}

	player.Folded = true
	gs.Folded++
}

type BetAction string

const (
	BetActionBet   BetAction = "bet"
	BetActionCall  BetAction = "call"
	BetActionCheck BetAction = "check"
	BetActionFold  BetAction = "fold"
	BetActionRaise BetAction = "raise"
	BetActionAllIn BetAction = "all_in"
)

type GamePlayer struct {
	*TableSeat
	ID            string          `json:"id"`
	Index         int             `json:"index"`
	Cards         poker.Cards     `json:"cards"`
	Folded        bool            `json:"folded"`
	AllIn         bool            `json:"all_in"`
	TotalBet      uint64          `json:"total_bet"`
	CurrentBet    uint64          `json:"current_bet"`
	CurrentAction BetAction       `json:"current_bet_action"`
	Winnings      uint64          `json:"winnings"`
	Hand          poker.TexasHand `json:"hand"`
	Showdown      bool            `json:"showdown"`
}

func (s *GamePlayer) Valid() bool {
	return s.TableSeat != nil &&
		s.ID == s.TableSeat.PlayerID &&
		s.TableSeat.Playing
}

func (s *GamePlayer) Bet(amount uint64, action BetAction) {
	s.TotalBet += amount
	s.CurrentBet += amount
	s.CurrentAction = action
	s.Balance -= amount
}
