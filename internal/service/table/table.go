package table

import (
	"context"
	"github.com/samber/lo"
	"hamster/internal/db"
	"hamster/internal/model"
	"hamster/internal/service"
	"hamster/pkg/log"
	"hamster/pkg/util/queue"
	"sort"
	"sync"
	"time"
)

type tableService struct {
	sync.Mutex

	db     *db.Client
	tables *sync.Map
}

func New(db *db.Client) (service.TableService, error) {
	s := &tableService{db: db, tables: &sync.Map{}}
	err := s.loadTables()
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (r *tableService) loadTables() error {
	tables, err := r.db.Tables.Query().All(context.Background())
	if err != nil {
		log.Error(err)
		return err
	}
	for _, table := range tables {
		v := &model.Table{
			ID:         table.ID,
			Name:       table.Name,
			Code:       table.Code,
			Type:       table.Type,
			BigBlind:   table.BigBlind,
			SmallBlind: table.SmallBlind,
			ExpiredAt:  table.ExpiredAt,
			MinBuyIn:   table.MinBuyIn,
			MaxBuyIn:   table.MaxBuyIn,
			IsVisible:  table.IsVisible,
		}

		// init table state
		v.TableState = &model.TableState{
			TableID:     table.ID,
			Seats:       make([]*model.TableSeat, v.MaxPlayers()),
			Players:     make(map[string]bool),
			WaitingList: queue.New[string](),
		}
		for i := range v.TableState.Seats {
			v.TableState.Seats[i] = &model.TableSeat{
				Seat:   i,
				Status: model.TableSeatStatusEmpty,
			}
		}

		// store table in memory
		r.tables.Store(table.ID, v)
	}
	return nil
}

func (r *tableService) List() []*model.Table {
	results := make([]*model.Table, 0)

	r.tables.Range(func(key, value any) bool {
		results = append(results, value.(*model.Table))
		return true
	})
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].ID < results[j].ID
	})
	return lo.Filter(results, func(item *model.Table, index int) bool {
		return item.ExpiredAt == nil || item.ExpiredAt.After(time.Now())
	})
}

func (r *tableService) Get(id uint64) (*model.Table, bool) {
	val, ok := r.tables.Load(id)
	if !ok {
		return nil, false
	}
	return val.(*model.Table), true
}
