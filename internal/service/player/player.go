package player

import (
	"hamster/internal/model"
	"hamster/internal/service"
	"sync"
)

type playerService struct {
	sync.Mutex
	states *sync.Map
}

func New() service.PlayerService {
	return &playerService{
		states: &sync.Map{},
	}
}

func (p *playerService) GetState(id string) *model.PlayerState {
	p.Lock()
	defer p.Unlock()

	val, ok := p.states.Load(id)
	if !ok {
		val = &model.PlayerState{ID: id}
		p.states.Store(id, val)
	}
	return val.(*model.PlayerState)
}

func (p *playerService) SetState(id string, state *model.PlayerState) {
	p.states.Store(id, state)
}
