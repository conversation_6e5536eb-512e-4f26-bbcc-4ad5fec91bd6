package authorize

import (
	"context"
	"encoding/json"
	"fmt"
	"hamster/config"
	"hamster/internal/service"
	"hamster/pkg/redis"
	"time"
)

type authorizeService struct {
	cfg   config.Config
	redis *redis.Client
}

type SessionData struct {
	UserEmail   string    `json:"user_email"`
	UserID      string    `json:"user_id"`
	DisplayName string    `json:"display_name"`
	CreatedAt   time.Time `json:"created_at"`
}

func (a authorizeService) ValidateSession(sessionToken string) (*service.SessionData, error) {
	ctx := context.Background()
	key := fmt.Sprintf("session:%s", sessionToken)

	sessionDataStr, err := a.redis.Get(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("session not found or expired: %w", err)
	}

	var sessionData SessionData
	err = json.Unmarshal([]byte(sessionDataStr), &sessionData)
	if err != nil {
		return nil, fmt.Errorf("invalid session data: %w", err)
	}

	// Convert to service.SessionData format
	return &service.SessionData{
		UserEmail:   sessionData.UserEmail,
		UserID:      sessionData.UserID,
		DisplayName: sessionData.DisplayName,
	}, nil
}

func New(cfg config.Config, redisClient *redis.Client) service.AuthorizeService {
	return &authorizeService{
		cfg:   cfg,
		redis: redisClient,
	}
}
