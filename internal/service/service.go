package service

import (
	"hamster/internal/model"
)

type Service struct {
	Authorize AuthorizeService
	Table     TableService
	Player    PlayerService
}

func New(a AuthorizeService, table TableService, player PlayerService) *Service {
	return &Service{
		Authorize: a,
		Table:     table,
		Player:    player,
	}
}

type SessionData struct {
	UserEmail   string `json:"user_email"`
	UserID      string `json:"user_id"`
	DisplayName string `json:"display_name"`
}

type AuthorizeService interface {
	ValidateSession(sessionToken string) (*SessionData, error)
}

type TableService interface {
	List() []*model.Table
	Get(id uint64) (*model.Table, bool)
}

type PlayerService interface {
	GetState(id string) *model.PlayerState
	SetState(id string, state *model.PlayerState)
}
