package net

import (
	"encoding/json"
	"strconv"
)

type Packet struct {
	Size uint32 `json:"size"`
	Body []byte `json:"data"`
}

func (p Packet) Int() (int, error) {
	v, err := strconv.ParseInt(p.String(), 10, 64)
	return int(v), err
}

func (p Packet) Int64() (int64, error) {
	v, _ := strconv.ParseInt(p.String(), 10, 64)
	return v, nil
}

func (p Packet) String() string {
	return string(p.Body)
}

func (p Packet) Unmarshal(v interface{}) error {
	return json.Unmarshal(p.Body, v)
}
