package net

import (
	"bufio"
	"encoding/binary"
	"errors"
	"github.com/gorilla/websocket"
	"hamster/internal/consts"
	"io"
	"net"
	"strconv"
	"time"
)

type ReadWriteCloser interface {
	Read() (*Packet, error)
	Write(msg *Packet) error
	Close() error
	SetReadDeadline(t time.Time) error
}

type TCPReadWriteCloser struct {
	conn   net.Conn
	reader io.Reader
}

func NewTcpReadWriteCloser(conn net.Conn, readBufferSize int) ReadWriteCloser {
	var reader io.Reader = conn
	if readBufferSize > 0 {
		reader = bufio.NewReaderSize(conn, readBufferSize)
	}
	return TCPReadWriteCloser{
		conn:   conn,
		reader: reader,
	}
}

func (t TCPReadWriteCloser) Read() (*Packet, error) {
	size, err := readUint32(t.reader)
	if err != nil {
		return nil, err
	}
	if size > consts.MaxPacketSize {
		return nil, errors.New("Overflow max packet size " + strconv.Itoa(consts.MaxPacketSize))
	}

	dataBytes := make([]byte, size)
	_, err = io.ReadFull(t.reader, dataBytes)
	if err != nil {
		return nil, err
	}

	return &Packet{
		Size: size,
		Body: dataBytes,
	}, nil
}

func (t TCPReadWriteCloser) Write(msg *Packet) error {
	lenBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(lenBytes, uint32(len(msg.Body)))
	data := make([]byte, 0)
	data = append(data, lenBytes...)
	data = append(data, msg.Body...)
	_, err := t.conn.Write(data)
	return err
}

func (t TCPReadWriteCloser) Close() error {
	return t.conn.Close()
}

func (t TCPReadWriteCloser) SetReadDeadline(t1 time.Time) error {
	return t.conn.SetReadDeadline(t1)
}

type WebsocketReadWriteCloser struct {
	conn *websocket.Conn
}

func NewWebsocketReadWriteCloser(conn *websocket.Conn) ReadWriteCloser {
	return WebsocketReadWriteCloser{conn: conn}
}

func (w WebsocketReadWriteCloser) Read() (*Packet, error) {
	_, b, err := w.conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	p := &Packet{
		Size: uint32(len(b)),
		Body: b,
	}
	return p, nil
}

func (w WebsocketReadWriteCloser) Write(msg *Packet) error {
	return w.conn.WriteMessage(websocket.TextMessage, msg.Body)
}

func (w WebsocketReadWriteCloser) Close() error {
	return w.conn.Close()
}

func (w WebsocketReadWriteCloser) SetReadDeadline(t time.Time) error {
	return w.conn.SetReadDeadline(t)
}

func readUint32(reader io.Reader) (uint32, error) {
	data := make([]byte, 4)
	_, err := io.ReadFull(reader, data)
	if err != nil {
		return 0, err
	}
	return binary.BigEndian.Uint32(data), nil
}

func readUint16(reader io.Reader) (uint16, error) {
	data := make([]byte, 2)
	_, err := io.ReadFull(reader, data)
	if err != nil {
		return 0, err
	}
	return binary.BigEndian.Uint16(data), nil
}
