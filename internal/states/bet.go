package states

import (
	"hamster/internal/consts"
	"hamster/internal/errs"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/pkg/log"
	"hamster/pkg/util/async"
	"time"
)

type BettingState struct {
	*States
}

func (s *BettingState) Handle(gs *model.GameState) (State, time.Duration) {
	timeout := consts.BetTimeout
	active, _ := gs.GetPlayer(gs.Active)

	for {
		begin := time.Now()
		e := s.awaitingForPlayerBet(active, timeout)
		err := s.handlePlayerBet(gs, active, e)
		if err == nil {
			playerBetEvent := event.NewPlayerBetEvent(active.ID, e.Action, e.Amount)
			s.h.Broadcast(s.table.ID, playerBetEvent)
			gs.Events = append(gs.Events, playerBetEvent)
			break
		}
		s.h.Notify(active.ID, event.NewErrorResponse(e, err))
		timeout -= time.Now().Sub(begin)
	}

	// if the player is leaved, kick the user from table
	if active.Leaved {
		s.kickPlayer(active)
	}

	// if n-1 players are folded, the game is over
	if gs.Folded == gs.PN-1 {
		return Settle, consts.FlopRoundInterval
	}

	// update next active player
	gs.Active = gs.NextTurnPlayer(gs.Active + 1)
	if gs.Active == -1 || gs.Active == gs.Leader {
		gs.Active = -1
		switch State(gs.Round) {
		case PreFlop:
			return Flop, consts.RoundInterval
		case Flop:
			return Turn, consts.RoundInterval
		case Turn:
			return River, consts.RoundInterval
		default:
			return Settle, consts.RoundInterval
		}
	}
	return Bet, consts.BetRoundInterval
}

func (s *BettingState) awaitingForPlayerBet(active *model.GamePlayer, timeout time.Duration) event.BetEvent {
	fallback := event.NewBetEvent(model.BetActionFold, 0)

	s.h.Broadcast(s.table.ID, event.NewBetPromptEvent(int(timeout.Seconds()), active.PlayerID))

	ps := s.h.Player.GetState(active.PlayerID)
	if !ps.Online || active.Leaved {
		return fallback
	}

	p, ok := s.h.Peer(active.PlayerID)
	if !ok {
		return fallback
	}
	h := p.AddHook(event.TypeBet, 1)
	defer h.Close()

	select {
	case packet := <-h.OnPacket():
		if packet == nil {
			return fallback
		}
		e := event.BetEvent{}
		err := packet.Unmarshal(&e)
		if err != nil {
			log.Error(err)
			return fallback
		}
		return e
	case <-time.After(timeout):
		return fallback
	}
}

func (s *BettingState) handlePlayerBet(gs *model.GameState, active *model.GamePlayer, e event.BetEvent) error {
	switch e.Action {
	case model.BetActionFold:
		gs.Fold(active.Index)
	case model.BetActionAllIn:
		if active.Balance == 0 {
			return errs.ErrInsufficientBalance
		}
		if active.Balance != e.Amount {
			return errs.ErrInvalidAllIn
		}
		gs.Bet(active.Index, e.Amount, e.Action)
	case model.BetActionCheck:
		if active.TotalBet < gs.MaxBet {
			return errs.ErrInvalidCheck
		}
		gs.Bet(active.Index, 0, e.Action) // trigger the change of leader
	case model.BetActionCall:
		if gs.MaxBet <= active.TotalBet {
			return errs.ErrInvalidCall
		}
		if e.Amount != gs.MaxBet-active.TotalBet {
			return errs.ErrInvalidCall
		}
		if e.Amount > active.Balance {
			return errs.ErrInsufficientBalance
		}
		gs.Bet(active.Index, e.Amount, e.Action)
	case model.BetActionRaise:
		if e.Amount > active.Balance {
			return errs.ErrInsufficientBalance
		}
		if e.Amount+active.TotalBet < gs.MaxBet ||
			e.Amount+active.TotalBet-gs.MaxBet < gs.MinRaise {
			return errs.ErrInvalidRaise
		}
		gs.Bet(active.Index, e.Amount, e.Action)
	case model.BetActionBet:
		if e.Amount > active.Balance {
			return errs.ErrInsufficientBalance
		}
		if active.TotalBet != gs.MaxBet {
			return errs.ErrInvalidBet
		}
		gs.Bet(active.Index, e.Amount, e.Action)
	default:
		return errs.ErrUnsupportedBetAction
	}
	return nil
}

func (s *BettingState) kickPlayer(player *model.GamePlayer) {
	async.Go(func() error {
		s.table.Lock()
		defer s.table.Unlock()
		time.Sleep(1 * time.Second)
		s.h.LeaveTable(nil, s.table, player.PlayerID)
		return nil
	})
}
