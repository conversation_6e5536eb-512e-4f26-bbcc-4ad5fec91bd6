package states

import (
	"hamster/internal/consts"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"time"
)

type InitState struct {
	*States
}

func (s *InitState) Handle(gs *model.GameState) (State, time.Duration) {
	pool := poker.InitTexas()
	pool.Shuffle(len(pool), 1)

	button := 0
	if gs.LastState != nil {
		button = gs.NextTurnPlayer(gs.LastState.Button + 1)
	}

	gs.Pool = pool
	gs.Button = button
	gs.SB = gs.NextTurnPlayer(button + 1)
	gs.BB = gs.NextTurnPlayer(gs.SB + 1)
	gs.Bet(gs.SB, s.table.SmallBlind, model.BetActionBet)
	gs.Bet(gs.BB, s.table.BigBlind, model.BetActionBet)
	gs.Events = append(gs.Events, event.NewPlayerBetEvent(gs.GetPlayerID(gs.SB), model.BetActionBet, s.table.SmallBlind))
	gs.Events = append(gs.Events, event.NewPlayerBetEvent(gs.GetPlayerID(gs.BB), model.BetActionBet, s.table.BigBlind))
	gs.MaxBet = s.table.BigBlind
	for _, p := range gs.Players {
		if p == nil {
			continue
		}
		p.Cards = gs.Pool.Pop(2)
	}
	return PreFlop, consts.RoundInterval
}
