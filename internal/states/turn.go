package states

import (
	"hamster/internal/consts"
	"hamster/internal/model"
	"time"
)

type TurnState struct {
	*States
}

func (s *TurnState) Handle(gs *model.GameState) (State, time.Duration) {
	gs.Reset()
	gs.Pool.Pop(1)
	gs.Board.Push(gs.Pool.Pop(1))
	gs.Round = string(Turn)

	if gs.AllIn == gs.PN-1 || gs.Active == -1 {
		return River, consts.RoundInterval
	}
	return Bet, consts.RoundInterval
}
