package states

import (
	"hamster/internal/model"
	"hamster/pkg/poker"
	"hamster/pkg/util/mathx"
	"sort"
	"time"
)

type SettleState struct {
	*States
}

func (s *SettleState) Handle(gs *model.GameState) (State, time.Duration) {
	actives := make([]*model.GamePlayer, 0)
	for _, player := range gs.Players {
		if player.Valid() && !player.Folded {
			player.Hand = poker.AnalyseTexasHand(player.Cards, gs.Board)
			actives = append(actives, player)
		}
	}

	if len(actives) == 1 {
		s.UncontestedWin(gs, actives)
	} else if gs.AllIn == 0 {
		s.NonAllinFinish(gs, actives)
	} else {
		s.Allin<PERSON>inish(gs, actives)
	}

	s.table.TotalHands++
	s.table.TotalPot += gs.Pot
	return Announce, 0
}

func (s *SettleState) UncontestedWin(gs *model.GameState, actives []*model.GamePlayer) {
	for _, player := range actives {
		player.Balance += gs.Pot
		player.Winnings = gs.Pot
		break
	}
}

func (s *SettleState) NonAllinFinish(gs *model.GameState, actives []*model.GamePlayer) {
	var hand poker.TexasHand
	var winners []*model.GamePlayer
	for _, player := range actives {
		player.Showdown = true
		h := player.Hand
		if h.Level > hand.Level || (h.Level == hand.Level && h.Score > hand.Score) {
			hand = h
			winners = []*model.GamePlayer{player}
		} else if h.Level == hand.Level && h.Score == hand.Score {
			winners = append(winners, player)
		}
	}

	pot := mathx.Div(gs.Pot, uint64(len(winners)))
	for _, p := range winners {
		p.Balance += pot
		p.Winnings = gs.Pot
	}
}

func (s *SettleState) AllinFinish(gs *model.GameState, actives []*model.GamePlayer) {
	type Pot struct {
		amount  uint64
		players int
		winners []*model.GamePlayer
	}
	sort.Slice(actives, func(i, j int) bool {
		return actives[i].TotalBet < actives[j].TotalBet
	})

	var pots []*Pot
	previous := uint64(0)

	for i, player := range actives {
		player.Showdown = true
		current := player.TotalBet
		if current > previous {
			delta := current - previous
			players := len(actives) - i
			pots = append(pots, &Pot{
				amount:  delta * uint64(players),
				players: players,
			})
			previous = current
		}
	}

	for _, pot := range pots {
		var hand poker.TexasHand
		for _, player := range actives[len(actives)-pot.players:] {
			h := player.Hand
			if h.Level > hand.Level || (h.Level == hand.Level && h.Score > hand.Score) {
				hand = h
				pot.winners = []*model.GamePlayer{player}
			} else if h.Level == hand.Level && h.Score == hand.Score {
				pot.winners = append(pot.winners, player)
			}
		}
		share := mathx.Div(pot.amount, uint64(len(pot.winners)))
		for _, winner := range pot.winners {
			winner.Balance += share
			winner.Winnings += share
		}
	}
}
