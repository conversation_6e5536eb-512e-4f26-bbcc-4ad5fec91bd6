package states

import (
	"hamster/internal/consts"
	"hamster/internal/model"
	"time"
)

type RiverState struct {
	*States
}

func (s *RiverState) Handle(gs *model.GameState) (State, time.Duration) {
	gs.Reset()
	gs.Pool.Pop(1)
	gs.Board.Push(gs.Pool.Pop(1))
	gs.Round = string(River)

	if gs.AllIn == gs.PN-1 || gs.Active == -1 {
		return Settle, consts.RoundInterval
	}
	return Bet, consts.RoundInterval
}
