package states

import (
	"hamster/internal/consts"
	"hamster/internal/model"
	"time"
)

type FlopState struct {
	*States
}

func (s *FlopState) Handle(gs *model.GameState) (State, time.Duration) {
	gs.Reset()
	gs.Pool.Pop(1)
	gs.Board = gs.Pool.Pop(3)
	gs.Round = string(Flop)

	if gs.AllIn == gs.PN-1 || gs.Active == -1 {
		return Turn, consts.FlopRoundInterval
	}
	return Bet, consts.FlopRoundInterval
}
