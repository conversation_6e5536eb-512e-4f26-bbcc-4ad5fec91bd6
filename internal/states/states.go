package states

import (
	"hamster/internal/consts"
	"hamster/internal/event"
	"hamster/internal/handler"
	"hamster/internal/model"
	"hamster/internal/service"
	"hamster/pkg/log"
	"hamster/pkg/util/async"
	"time"
)

type State string

const (
	Init     State = "init"
	PreFlop  State = "pre_flop"
	Flop     State = "flop"
	Turn     State = "turn"
	River    State = "river"
	Bet      State = "bet"
	Settle   State = "settle"
	Announce State = "announce"
	End      State = "end"
)

type Handler interface {
	Handle(gs *model.GameState) (State, time.Duration)
}

type States struct {
	s             *service.Service
	h             *handler.Handler
	table         *model.Table
	lastGameState *model.GameState
	states        map[State]Handler

	stopped bool
}

func New(s *service.Service, h *handler.Handler, table *model.Table) *States {
	g := &States{
		s: s, h: h, table: table,
		states: map[State]Handler{},
	}
	g.states[Init] = &InitState{g}
	g.states[PreFlop] = &PreFlopState{g}
	g.states[Flop] = &FlopState{g}
	g.states[Turn] = &TurnState{g}
	g.states[River] = &RiverState{g}
	g.states[Bet] = &BettingState{g}
	g.states[Settle] = &SettleState{g}
	g.states[Announce] = &AnnounceState{g}
	g.states[End] = &EndState{g}
	return g
}

func (s *States) Run() {
	async.Go(func() error {
		for {
			if s.stopped {
				log.Infof("[Waiting] the game of table %d is stopped", s.table.ID)
				return nil
			}

			s.resetTableState()

			if g, ok := s.awaiting(s.lastGameState); ok {
				s.table.GameState = g
				s.run(g)
			} else {
				time.Sleep(2 * time.Second) // sleep for a while before checking again
			}
		}
	})
}

func (s *States) run(g *model.GameState) {
	var interval time.Duration
	for state := Init; state != ""; {
		st := s.states[state]
		g.State = string(state)
		state, interval = st.Handle(g)
		s.broadcastGameState(g)
		time.Sleep(interval)
	}
	s.h.Broadcast(s.table.ID, event.NewTableStateEvent(nil, s.table.TableState))
}

func (s *States) awaiting(lastState *model.GameState) (*model.GameState, bool) {
	if ok := s.checkTableStateWithLock(); !ok {
		return nil, false
	}

	if lastState == nil {
		s.h.Broadcast(s.table.ID, event.NewGameStartCountdownEvent(nil, int(consts.GameStartCountdown.Seconds())))
		time.Sleep(consts.GameStartCountdown)
	}

	return s.newGameState(lastState)
}

func (s *States) newGameState(lastState *model.GameState) (*model.GameState, bool) {
	s.table.Lock()
	defer s.table.Unlock()

	if ok := s.checkTableState(); !ok {
		return nil, false
	}

	ts := s.table.TableState

	players := make(map[string]*model.GamePlayer)
	for i, seat := range ts.Seats {
		if seat.Status == model.TableSeatStatusOccupied {
			seat.Playing = true
			players[seat.PlayerID] = &model.GamePlayer{
				TableSeat: seat,
				Index:     i,
				ID:        seat.PlayerID,
				Folded:    false,
				AllIn:     false,
			}
		}
	}
	return &model.GameState{
		TableState: ts,
		TableID:    ts.TableID,
		Stakes:     []uint64{s.table.SmallBlind, s.table.BigBlind},
		Players:    players,
		PN:         uint64(len(players)),
		LastState:  lastState,
		Leader:     -1,
		Active:     -1,
	}, true
}

func (s *States) broadcastGameState(g *model.GameState) {
	for playerId := range s.table.TableState.Players {
		s.h.Notify(playerId, event.NewGameStateEvent(nil, g, playerId))
	}
}

func (s *States) checkTableStateWithLock() bool {
	s.table.Lock()
	defer s.table.Unlock()
	return s.checkTableState()
}

func (s *States) checkTableState() bool {
	ts := s.table.TableState
	s.clearOfflinePlayers(ts)
	s.clearLeavedPlayers(ts)
	s.clearInsufficientAmountPlayers(ts)
	occupied := 0
	for _, seat := range ts.Seats {
		if seat.Status == model.TableSeatStatusOccupied {
			occupied++
		}
	}
	return occupied > 1
}

func (s *States) clearOfflinePlayers(ts *model.TableState) {
	for _, seat := range ts.Seats {
		if seat.PlayerID == "" {
			continue
		}
		player := s.h.Player.GetState(seat.PlayerID)
		if !player.Online {
			s.h.LeaveTable(nil, s.table, seat.PlayerID)
		}
	}
}

func (s *States) clearLeavedPlayers(ts *model.TableState) {
	for _, seat := range ts.Seats {
		if seat.PlayerID != "" && seat.Leaved {
			s.h.LeaveTable(nil, s.table, seat.PlayerID)
		}
	}
}

func (s *States) clearInsufficientAmountPlayers(ts *model.TableState) {
	var changed bool

	for _, seat := range ts.Seats {
		if seat.Status == model.TableSeatStatusOccupied && seat.Balance < s.table.BigBlind {
			seat.SetReserved(seat.PlayerID)
			async.Go(func() error { return s.h.RequestBuyIn(s.table, seat.PlayerID, seat.Seat) })
			changed = true
		}
	}
	if changed {
		s.h.Broadcast(ts.TableID, event.NewTableStateEvent(nil, ts))
	}
}

func (s *States) resetTableState() {
	s.table.Lock()
	defer s.table.Unlock()

	ts := s.table.TableState
	for _, seat := range ts.Seats {
		seat.Playing = false
	}
	s.lastGameState = s.table.GameState
	s.table.GameState = nil
}

func (s *States) Stop() {
	s.stopped = true
}

func (s *States) Stopped() bool {
	return s.stopped
}
