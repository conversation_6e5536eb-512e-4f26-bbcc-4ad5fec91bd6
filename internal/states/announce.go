package states

import (
	"hamster/internal/consts"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/pkg/util/async"
	"time"
)

type AnnounceState struct {
	*States
}

func (s *AnnounceState) Handle(gs *model.GameState) (State, time.Duration) {
	for _, player := range gs.Players {
		if player.Winnings > 0 && !player.Showdown {
			async.Go(func() error { s.requestShowdown(gs, player); return nil })
		}
	}
	return End, consts.AnnounceRoundInterval
}

func (s *AnnounceState) requestShowdown(gs *model.GameState, player *model.GamePlayer) {
	showdown := s.awaitingForPlayerShowdown(player.PlayerID)
	if showdown {
		player.Showdown = true
		s.broadcastGameState(gs)
	}
}

func (s *AnnounceState) awaitingForPlayerShowdown(playerId string) bool {
	state := s.h.Player.GetState(playerId)
	if !state.Online {
		return false
	}

	p, ok := s.h.<PERSON>eer(playerId)
	if !ok {
		return false
	}
	h := p.AddHook(event.TypeShowdown, 1)
	defer h.Close()

	s.h.Notify(playerId, event.NewShowdownPromptEvent(int(consts.ShowdownTimeout.Seconds())))

	select {
	case packet := <-h.OnPacket():
		if packet == nil {
			return false
		}
		return true
	case <-time.After(consts.ShowdownTimeout):
		return false
	}
}
