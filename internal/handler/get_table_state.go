package handler

import (
	"hamster/internal/event"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) GetTableStateEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.GetTableStateEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[GetTableState] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[GetTableState] table %d not found", e.TableID)
		return
	}

	h.Notify(peer.ID, event.NewTableStateEvent(e, t.TableState))
}
