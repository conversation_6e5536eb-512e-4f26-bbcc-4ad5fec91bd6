package handler

import (
	"hamster/internal/event"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) GetPlayerStateEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.GetPlayerStateEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[GetPlayerState] failed to unmarshal packet: %v", err)
		return
	}

	state := h.Player.GetState(peer.ID)
	h.Notify(peer.ID, event.NewPlayerStateEvent(e, state))
}
