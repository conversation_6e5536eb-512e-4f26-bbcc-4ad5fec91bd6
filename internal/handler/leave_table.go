package handler

import (
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) LeaveTableEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.LeaveTableEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[LeaveTable] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[LeaveTable] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.LeaveTable(e, t, peer.ID)
}

func (h *Handler) LeaveTable(src event.Event, t *model.Table, playerId string) {
	ps := h.Player.GetState(playerId)
	ts := t.TableState

	h.StandUp(src, t, playerId)
	h.LeaveWaitingList(src, t, playerId)

	if ps.TableID == t.ID {
		ps.TableID = 0
	}
	if _, ok := ts.Players[ps.ID]; ok {
		delete(ts.Players, ps.ID)
		e := event.NewLeftTableEvent(src, t.ID, playerId)
		h.Broadcast(ts.TableID, e)
		h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
		h.Notify(playerId, e)
	}
}
