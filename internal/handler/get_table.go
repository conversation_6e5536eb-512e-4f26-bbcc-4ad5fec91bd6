package handler

import (
	"hamster/internal/event"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) GetTableEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.GetTableEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[GetTable] failed to unmarshal packet: %v", err)
		return
	}

	table, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[GetTable] table %d not found", e.TableID)
		return
	}
	h.Notify(peer.ID, event.NewTableEvent(e, table))
}
