package handler

import (
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) LeaveWaitingListEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.LeaveWaitingListEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[LeaveWaitingList] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[LeaveWaitingList] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.LeaveWaitingList(e, t, peer.ID)
}

func (h *Handler) LeaveWaitingList(src event.Event, t *model.Table, playerId string) {
	ps := h.Player.GetState(playerId)
	ts := t.TableState

	if ts.WaitingList.Remove(ps.ID) < 0 {
		return
	}

	h.Broadcast(ts.TableID, event.NewLeftWaitingListEvent(src, t.ID, ps.ID))
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
}
