package handler

import (
	"github.com/samber/lo"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) GetTablesEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.GetTablesEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[GetTables] failed to unmarshal packet: %v", err)
		return
	}

	tables := h.Table.List()
	tables = lo.Filter(tables, func(item *model.Table, index int) bool {
		if e.Code != nil {
			return item.Code == *e.Code
		}
		return item.IsVisible
	})
	h.Notify(peer.ID, event.NewTablesEvent(e, tables))
}
