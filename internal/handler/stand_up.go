package handler

import (
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) StandUpEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.StandUpEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[StandUp] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[StandUp] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.StandUp(e, t, peer.ID)
}

func (h *Handler) StandUp(src event.Event, t *model.Table, playerId string) {
	ts := t.TableState
	ps := h.Player.GetState(playerId)

	seat, ok := ts.GetPlayerSeat(playerId)
	if !ok {
		return
	}

	if !seat.Leaved && seat.Playing {
		seat.Leaved = true
		return
	}

	ts.Seated--
	seat.SetEmpty()

	h.Broadcast(ts.TableID, event.NewStoodEvent(src, ts.TableID, ps.ID, seat.Seat))
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
	if gs := t.GameState; gs != nil {
		h.Broadcast(ts.TableID, event.NewGameStateEvent(src, gs, playerId))
	}
	h.dequeueWaitingList(src, t, seat.Seat)
}

func (h *Handler) dequeueWaitingList(src event.Event, t *model.Table, seat int) {
	ts := t.TableState
	for {
		playerId, ok := ts.WaitingList.Dequeue()
		if !ok {
			break
		}
		ps := h.Player.GetState(playerId)
		if ps == nil || !ps.Online {
			continue
		}
		h.SitDown(src, t, playerId, seat)
		break
	}
}
