package handler

import (
	"hamster/internal/event"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) GetGameStateEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.GetGameStateEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[GetGameState] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[GetGameState] table %d not found", e.TableID)
		return
	}

	if gs := t.GameState; gs != nil {
		h.Notify(peer.ID, event.NewGameStateEvent(e, gs, peer.ID))
	}
}
