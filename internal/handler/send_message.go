package handler

import (
	"hamster/internal/consts"
	"hamster/internal/event"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) SendMessageEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.SendMessageEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[SendMessage] failed to unmarshal packet: %v", err)
		return
	}

	ps := h.Player.GetState(peer.ID)
	if ps.TableID == 0 {
		return
	}

	message, ok := consts.MessageCodes[e.Code]
	if !ok {
		log.Errorf("[SendMessage] unknown message code: %v", e.Code)
		return
	}

	h.Broadcast(ps.TableID, event.NewChatEvent(e, peer.ID, message))
}
