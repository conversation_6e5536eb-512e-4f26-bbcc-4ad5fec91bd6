package handler

import (
	"context"
	"hamster/internal/errs"
	"hamster/internal/event"
	net2 "hamster/internal/net"
	"hamster/internal/peer"
	"hamster/internal/service"
	"hamster/pkg/log"
	"hamster/pkg/util/async"
	"sync"
	"time"
)

type eventHandler func(p *peer.Peer, packet *net2.Packet)

type Handler struct {
	sync.Mutex
	*service.Service

	peers map[string]*peer.Peer
}

func New(svc *service.Service) *Handler {
	return &Handler{
		Service: svc,
		peers:   make(map[string]*peer.Peer),
	}
}

func (h *Handler) Handle(ctx context.Context, c net2.ReadWriteCloser) {
	p, err := h.authorize(c)
	if err != nil {
		log.Errorf("authorize err %v", err)
		_ = c.Close()
		return
	}

	log.Infof("peer %s connected", p.ID)

	h.registerHandlers(p)
	h.connect(p)

	async.Go(func() error {
		err = p.Listening()
		if err != nil {
			log.Errorf("peer %s disconnected, err: %v", p.ID, err)
			h.disconnect(p)
		}
		return err
	})
}

func (h *Handler) connect(p *peer.Peer) {
	state := h.Player.GetState(p.ID)
	state.Online = true

	h.Notify(p.ID, event.NewPlayerStateEvent(nil, state))

	if t, ok := h.Table.Get(state.TableID); ok {
		h.Notify(p.ID, event.NewGoToTableEvent(nil, t))
		h.Notify(p.ID, event.NewTableStateEvent(nil, t.TableState))
		if gs := t.GameState; gs != nil {
			h.Notify(p.ID, event.NewGameStateEvent(nil, gs, p.ID))
		}
	}
}

func (h *Handler) disconnect(p *peer.Peer) {
	state := h.Player.GetState(p.ID)
	state.Online = false

	if t, ok := h.Table.Get(state.TableID); ok {
		t.Lock()
		defer t.Unlock()
		if seat, ok := t.TableState.GetPlayerSeat(p.ID); !ok || !seat.Playing {
			h.LeaveTable(nil, t, p.ID)
		}
	}
	h.removePeer(p.ID)
}

func (h *Handler) Broadcast(tableId uint64, event event.Event) {
	if t, ok := h.Table.Get(tableId); ok {
		for userId := range t.TableState.Players {
			if p, ok := h.Peer(userId); ok {
				p.Write(event)
			}
		}
	}
}

func (h *Handler) Notify(userId string, event event.Event) {
	if p, ok := h.Peer(userId); ok {
		p.Write(event)
	}
}

func (h *Handler) authorize(c net2.ReadWriteCloser) (*peer.Peer, error) {
	ch := make(chan *peer.Peer)
	defer close(ch)

	async.Go(func() error {
		packet, err := c.Read()
		if err != nil {
			log.Error(err)
			return err
		}

		e := event.AuthorizeEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			log.Error(err)
			return err
		}

		sessionData, err := h.Authorize.ValidateSession(e.SessionToken)
		if err != nil {
			log.Error(err)
			return errs.ErrUnauthorized
		}

		// Use email as the peer ID for consistency
		p := h.addPeer(sessionData.UserEmail, c)
		p.Write(event.NewAuthorizedEvent(e))
		ch <- p
		return nil
	})

	select {
	case p := <-ch:
		return p, nil
	case <-time.After(5 * time.Second):
		return nil, errs.ErrUnauthorized
	}
}

func (h *Handler) registerHandlers(p *peer.Peer) {
	hs := make(map[event.Type]eventHandler)
	hs[event.TypePing] = h.PingEventHandler
	hs[event.TypeGetTable] = h.GetTableEventHandler
	hs[event.TypeGetTables] = h.GetTablesEventHandler
	hs[event.TypeGetPlayerState] = h.GetPlayerStateEventHandler
	hs[event.TypeGetTableState] = h.GetTableStateEventHandler
	hs[event.TypeGetGameState] = h.GetGameStateEventHandler
	hs[event.TypeJoinTable] = h.JoinTableEventHandler
	hs[event.TypeJoinWaitingList] = h.JoinTableWaitingListEventHandler
	hs[event.TypeLeaveTable] = h.LeaveTableEventHandler
	hs[event.TypeLeaveWaitingList] = h.LeaveWaitingListEventHandler
	hs[event.TypeSitDown] = h.SitDownEventHandler
	hs[event.TypeStandUp] = h.StandUpEventHandler
	hs[event.TypeSendMessage] = h.SendMessageEventHandler

	for typ, handler := range hs {
		hook := p.AddHook(typ, 1)
		async.Go(func() error {
			for packet := range hook.OnPacket() {
				if packet == nil {
					return nil
				}
				handler(p, packet)
			}
			return nil
		})
	}
}

func (h *Handler) Peer(userId string) (*peer.Peer, bool) {
	h.Lock()
	defer h.Unlock()

	c, ok := h.peers[userId]
	return c, ok
}

func (h *Handler) addPeer(userId string, c net2.ReadWriteCloser) *peer.Peer {
	h.Lock()
	defer h.Unlock()

	p := peer.New(c, userId)
	h.peers[userId] = p
	return p
}

func (h *Handler) removePeer(userId string) {
	h.Lock()
	defer h.Unlock()

	if p, ok := h.peers[userId]; ok {
		p.Close()
	}
	delete(h.peers, userId)
}
