package handler

import (
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) JoinTableEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.JoinTableEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[JoinTable] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[JoinTable] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.JoinTable(e, t, peer.ID)
}

func (h *Handler) JoinTable(src event.Event, t *model.Table, playerId string) {
	ps := h.Player.GetState(playerId)
	ts := t.TableState

	if ps.TableID > 0 {
		log.Errorf("[JoinTable] player %s is already in table %d", ps.ID, ps.TableID)
		return
	}

	ps.TableID = ts.TableID
	ts.Players[ps.ID] = true

	h.Notify(playerId, event.NewGoToTableEvent(src, t))
	h.Broadcast(ts.TableID, event.NewJoinedTableEvent(src, ts.TableID, ps.ID))
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
	if gs := t.GameState; gs != nil {
		h.Notify(playerId, event.NewGameStateEvent(src, gs, playerId))
	}
}
