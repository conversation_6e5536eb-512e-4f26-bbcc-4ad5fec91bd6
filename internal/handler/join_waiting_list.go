package handler

import (
	"hamster/internal/consts"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
)

func (h *Handler) JoinTableWaitingListEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.JoinWaitingListEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[JoinWaitingList] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[JoinWaitingList] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.JoinTableWaitingList(e, t, peer.ID)
}

func (h *Handler) JoinTableWaitingList(src event.Event, t *model.Table, playerId string) {
	ps := h.Player.GetState(playerId)
	ts := t.TableState

	if ps.TableID != t.ID {
		return
	}
	if ts.Seated < uint64(t.MaxPlayers()) {
		log.Errorf("[JoinWaitingList] table %d is not full", ts.TableID)
		return
	}
	if ts.WaitingList.Size() >= consts.MaxWaitingListSize {
		log.Errorf("[JoinWaitingList] waiting list is full in table %d", ts.TableID)
		return
	}

	ts.WaitingList.Enqueue(ps.ID)

	h.Broadcast(ts.TableID, event.NewJoinedWaitingListEvent(src, t.ID, ps.ID))
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
}
