package handler

import (
	"hamster/internal/consts"
	"hamster/internal/errs"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/log"
	"hamster/pkg/util/async"
	"time"
)

func (h *Handler) SitDownEventHandler(peer *peer.Peer, packet *net.Packet) {
	e := event.SitDownEvent{}
	err := packet.Unmarshal(&e)
	if err != nil {
		log.Errorf("[SitDown] failed to unmarshal packet: %v", err)
		return
	}

	t, ok := h.Table.Get(e.TableID)
	if !ok {
		log.Errorf("[SitDown] table %d not found", e.TableID)
		return
	}

	t.Lock()
	defer t.Unlock()

	h.LeaveWaitingList(e, t, peer.ID)
	h.SitDown(e, t, peer.ID, e.Seat)
}

func (h *Handler) SitDown(src event.Event, t *model.Table, playerId string, seat int) {
	ps := h.Player.GetState(playerId)
	ts := t.TableState

	if ps.TableID != t.ID {
		log.Errorf("[SitDown] player %s is not in table %d", ps.ID, t.ID)
		return
	}
	if _, ok := ts.GetPlayerSeat(playerId); ok {
		log.Errorf("[SitDown] player %s is already seated in table %d", ps.ID, ts.TableID)
		return
	}
	if seat < 0 || seat >= len(ts.Seats) {
		log.Errorf("[SitDown] invalid seat %d, max players %d", seat, len(ts.Seats))
		return
	}
	if ts.Seats[seat].Status != model.TableSeatStatusEmpty {
		log.Errorf("[SitDown] seat %d is already occupied in table %d", seat, ts.TableID)
		return
	}

	ts.Seated++
	ts.Seats[seat].SetReserved(playerId)
	ts.Players[ps.ID] = true
	ts.WaitingList.Remove(ps.ID)

	h.Broadcast(ts.TableID, event.NewSeatedEvent(src, ts.TableID, ps.ID, ts.Seats[seat]))
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
	if gs := t.GameState; gs != nil {
		h.Broadcast(ts.TableID, event.NewGameStateEvent(src, gs, playerId))
	}
	async.Go(func() error { return h.RequestBuyIn(t, playerId, seat) })
}

func (h *Handler) BuyIn(src event.Event, t *model.Table, playerId string, seat int, amount uint64) {
	ts := t.TableState

	ts.Seats[seat].SetOccupied(playerId, amount)
	h.Broadcast(ts.TableID, event.NewTableStateEvent(src, ts))
	if gs := t.GameState; gs != nil {
		h.Broadcast(ts.TableID, event.NewGameStateEvent(src, gs, playerId))
	}
}

func (h *Handler) RequestBuyIn(t *model.Table, playerId string, seat int) (err error) {
	timeout := consts.BuyInTimeout
	for {
		begin := time.Now()
		e := h.awaitingForPlayerBuyIn(t, playerId, timeout)
		err = h.handlePlayerBuyIn(t, playerId, seat, e)
		if err == nil {
			break
		}
		h.Notify(playerId, event.NewErrorResponse(e, err))
		timeout -= time.Now().Sub(begin)
	}
	return err
}

func (h *Handler) handlePlayerBuyIn(t *model.Table, playerId string, seat int, e event.BuyInEvent) error {
	t.Lock()
	defer t.Unlock()

	s, ok := t.TableState.GetSeat(seat)
	if !ok || s.PlayerID != playerId || s.Status != model.TableSeatStatusReserved {
		return nil
	}

	switch e.Action {
	case event.BuyInActionBuyIn:
		if e.Amount < t.MinBuyIn || e.Amount > t.MaxBuyIn {
			return errs.ErrInvalidBuyInAmount
		}
		h.BuyIn(e, t, playerId, seat, e.Amount)
	case event.BuyInActionLeave:
		h.StandUp(e, t, playerId)
	default:
		return errs.ErrInvalidBuyInAction
	}
	return nil
}

func (h *Handler) awaitingForPlayerBuyIn(t *model.Table, playerId string, timeout time.Duration) event.BuyInEvent {
	fallback := event.NewBuyInEvent(event.BuyInActionLeave, 0)

	state := h.Player.GetState(playerId)
	if !state.Online {
		return fallback
	}

	p, ok := h.Peer(playerId)
	if !ok {
		return fallback
	}

	hook := p.AddHook(event.TypeBuyIn, 1)
	defer hook.Close()

	h.Notify(playerId, event.NewBuyInPromptEvent(t.MinBuyIn, t.MaxBuyIn, int(timeout.Seconds())))

	select {
	case packet := <-hook.OnPacket():
		if packet == nil {
			return fallback
		}
		e := event.BuyInEvent{}
		err := packet.Unmarshal(&e)
		if err != nil {
			log.Error(err)
			return fallback
		}
		return e
	case <-time.After(timeout):
		return fallback
	}
}
