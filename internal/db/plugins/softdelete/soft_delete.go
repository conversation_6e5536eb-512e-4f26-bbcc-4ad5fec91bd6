package softdelete

import (
	"context"
	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"fmt"
	gen "hamster/internal/db/ent"
	"hamster/internal/db/ent/intercept"
	"time"
)

type softDeleteKey struct{}

func Skip(ctx context.Context) context.Context {
	return context.WithValue(ctx, softDeleteKey{}, true)
}

func Interceptor() ent.Interceptor {
	return intercept.TraverseFunc(func(ctx context.Context, q intercept.Query) error {
		// Skip soft-delete, means include soft-deleted entities.
		if skip, _ := ctx.Value(softDeleteKey{}).(bool); skip {
			return nil
		}
		q.WhereP(sql.FieldIsNull("deleted_at"))
		return nil
	})
}

func Hook(next ent.Mutator) ent.Mutator {
	return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
		// Skip soft-delete, means delete the entity permanently.
		if skip, _ := ctx.Value(softDeleteKey{}).(bool); skip {
			return next.Mutate(ctx, m)
		}

		mx, ok := m.(interface {
			SetOp(ent.Op)
			Client() *gen.Client
			SetDeletedAt(time.Time) // That is the line that needs to be updated if you change column name to be deleted_at
			WhereP(...func(*sql.Selector))
		})
		if !ok {
			return nil, fmt.Errorf("unexpected mutation type %T %+v", m, m)
		}

		mx.WhereP(sql.FieldIsNull("deleted_at"))
		mx.SetOp(ent.OpUpdate)
		mx.SetDeletedAt(time.Now())
		return mx.Client().Mutate(ctx, m)
	})
}
