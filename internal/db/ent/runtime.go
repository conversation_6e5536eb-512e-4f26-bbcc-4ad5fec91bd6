// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/schema"
	"hamster/internal/db/ent/tables"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	gamesFields := schema.Games{}.Fields()
	_ = gamesFields
	// gamesDescStartedAt is the schema descriptor for started_at field.
	gamesDescStartedAt := gamesFields[13].Descriptor()
	// games.DefaultStartedAt holds the default value on creation for the started_at field.
	games.DefaultStartedAt = gamesDescStartedAt.Default.(func() time.Time)
	// gamesDescSettled is the schema descriptor for settled field.
	gamesDescSettled := gamesFields[15].Descriptor()
	// games.DefaultSettled holds the default value on creation for the settled field.
	games.DefaultSettled = gamesDescSettled.Default.(bool)
	// gamesDescCreatedAt is the schema descriptor for created_at field.
	gamesDescCreatedAt := gamesFields[16].Descriptor()
	// games.DefaultCreatedAt holds the default value on creation for the created_at field.
	games.DefaultCreatedAt = gamesDescCreatedAt.Default.(func() time.Time)
	// gamesDescUpdatedAt is the schema descriptor for updated_at field.
	gamesDescUpdatedAt := gamesFields[19].Descriptor()
	// games.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	games.DefaultUpdatedAt = gamesDescUpdatedAt.Default.(func() time.Time)
	// games.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	games.UpdateDefaultUpdatedAt = gamesDescUpdatedAt.UpdateDefault.(func() time.Time)
	tablesFields := schema.Tables{}.Fields()
	_ = tablesFields
	// tablesDescName is the schema descriptor for name field.
	tablesDescName := tablesFields[1].Descriptor()
	// tables.NameValidator is a validator for the "name" field. It is called by the builders before save.
	tables.NameValidator = tablesDescName.Validators[0].(func(string) error)
	// tablesDescCode is the schema descriptor for code field.
	tablesDescCode := tablesFields[2].Descriptor()
	// tables.CodeValidator is a validator for the "code" field. It is called by the builders before save.
	tables.CodeValidator = tablesDescCode.Validators[0].(func(string) error)
	// tablesDescIsVisible is the schema descriptor for is_visible field.
	tablesDescIsVisible := tablesFields[9].Descriptor()
	// tables.DefaultIsVisible holds the default value on creation for the is_visible field.
	tables.DefaultIsVisible = tablesDescIsVisible.Default.(bool)
	// tablesDescCreatedAt is the schema descriptor for created_at field.
	tablesDescCreatedAt := tablesFields[11].Descriptor()
	// tables.DefaultCreatedAt holds the default value on creation for the created_at field.
	tables.DefaultCreatedAt = tablesDescCreatedAt.Default.(func() time.Time)
	// tablesDescUpdatedAt is the schema descriptor for updated_at field.
	tablesDescUpdatedAt := tablesFields[14].Descriptor()
	// tables.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tables.DefaultUpdatedAt = tablesDescUpdatedAt.Default.(func() time.Time)
	// tables.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tables.UpdateDefaultUpdatedAt = tablesDescUpdatedAt.UpdateDefault.(func() time.Time)
}
