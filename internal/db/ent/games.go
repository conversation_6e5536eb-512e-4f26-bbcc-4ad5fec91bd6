// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"hamster/internal/db/ent/games"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Games is the model entity for the Games schema.
type Games struct {
	config `json:"-"`
	// ID of the ent.
	ID uint64 `json:"id,omitempty"`
	// TableID holds the value of the "table_id" field.
	TableID uint64 `json:"table_id,omitempty"`
	// Status holds the value of the "status" field.
	Status games.Status `json:"status,omitempty"`
	// Seats holds the value of the "seats" field.
	Seats []string `json:"seats,omitempty"`
	// Pot holds the value of the "pot" field.
	Pot uint64 `json:"pot,omitempty"`
	// Button holds the value of the "button" field.
	Button int `json:"button,omitempty"`
	// Sb holds the value of the "sb" field.
	Sb int `json:"sb,omitempty"`
	// Bb holds the value of the "bb" field.
	Bb int `json:"bb,omitempty"`
	// Stakes holds the value of the "stakes" field.
	Stakes []uint64 `json:"stakes,omitempty"`
	// Players holds the value of the "players" field.
	Players []model.PlayerState `json:"players,omitempty"`
	// Winners holds the value of the "winners" field.
	Winners []model.PlayerState `json:"winners,omitempty"`
	// Board holds the value of the "board" field.
	Board poker.Cards `json:"board,omitempty"`
	// Duration holds the value of the "duration" field.
	Duration int64 `json:"duration,omitempty"`
	// StartedAt holds the value of the "started_at" field.
	StartedAt time.Time `json:"started_at,omitempty"`
	// CompletedAt holds the value of the "completed_at" field.
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	// Settled holds the value of the "settled" field.
	Settled bool `json:"settled,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// CreatedBy holds the value of the "created_by" field.
	CreatedBy string `json:"created_by,omitempty"`
	// UpdatedBy holds the value of the "updated_by" field.
	UpdatedBy string `json:"updated_by,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    *time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Games) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case games.FieldSeats, games.FieldStakes, games.FieldPlayers, games.FieldWinners, games.FieldBoard:
			values[i] = new([]byte)
		case games.FieldSettled:
			values[i] = new(sql.NullBool)
		case games.FieldID, games.FieldTableID, games.FieldPot, games.FieldButton, games.FieldSb, games.FieldBb, games.FieldDuration:
			values[i] = new(sql.NullInt64)
		case games.FieldStatus, games.FieldCreatedBy, games.FieldUpdatedBy:
			values[i] = new(sql.NullString)
		case games.FieldStartedAt, games.FieldCompletedAt, games.FieldCreatedAt, games.FieldUpdatedAt, games.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Games fields.
func (ga *Games) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case games.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ga.ID = uint64(value.Int64)
		case games.FieldTableID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field table_id", values[i])
			} else if value.Valid {
				ga.TableID = uint64(value.Int64)
			}
		case games.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				ga.Status = games.Status(value.String)
			}
		case games.FieldSeats:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field seats", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ga.Seats); err != nil {
					return fmt.Errorf("unmarshal field seats: %w", err)
				}
			}
		case games.FieldPot:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pot", values[i])
			} else if value.Valid {
				ga.Pot = uint64(value.Int64)
			}
		case games.FieldButton:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field button", values[i])
			} else if value.Valid {
				ga.Button = int(value.Int64)
			}
		case games.FieldSb:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sb", values[i])
			} else if value.Valid {
				ga.Sb = int(value.Int64)
			}
		case games.FieldBb:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bb", values[i])
			} else if value.Valid {
				ga.Bb = int(value.Int64)
			}
		case games.FieldStakes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field stakes", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ga.Stakes); err != nil {
					return fmt.Errorf("unmarshal field stakes: %w", err)
				}
			}
		case games.FieldPlayers:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field players", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ga.Players); err != nil {
					return fmt.Errorf("unmarshal field players: %w", err)
				}
			}
		case games.FieldWinners:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field winners", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ga.Winners); err != nil {
					return fmt.Errorf("unmarshal field winners: %w", err)
				}
			}
		case games.FieldBoard:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field board", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ga.Board); err != nil {
					return fmt.Errorf("unmarshal field board: %w", err)
				}
			}
		case games.FieldDuration:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field duration", values[i])
			} else if value.Valid {
				ga.Duration = value.Int64
			}
		case games.FieldStartedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field started_at", values[i])
			} else if value.Valid {
				ga.StartedAt = value.Time
			}
		case games.FieldCompletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field completed_at", values[i])
			} else if value.Valid {
				ga.CompletedAt = new(time.Time)
				*ga.CompletedAt = value.Time
			}
		case games.FieldSettled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field settled", values[i])
			} else if value.Valid {
				ga.Settled = value.Bool
			}
		case games.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ga.CreatedAt = value.Time
			}
		case games.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				ga.CreatedBy = value.String
			}
		case games.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				ga.UpdatedBy = value.String
			}
		case games.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ga.UpdatedAt = value.Time
			}
		case games.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				ga.DeletedAt = new(time.Time)
				*ga.DeletedAt = value.Time
			}
		default:
			ga.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Games.
// This includes values selected through modifiers, order, etc.
func (ga *Games) Value(name string) (ent.Value, error) {
	return ga.selectValues.Get(name)
}

// Update returns a builder for updating this Games.
// Note that you need to call Games.Unwrap() before calling this method if this Games
// was returned from a transaction, and the transaction was committed or rolled back.
func (ga *Games) Update() *GamesUpdateOne {
	return NewGamesClient(ga.config).UpdateOne(ga)
}

// Unwrap unwraps the Games entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ga *Games) Unwrap() *Games {
	_tx, ok := ga.config.driver.(*txDriver)
	if !ok {
		panic("ent: Games is not a transactional entity")
	}
	ga.config.driver = _tx.drv
	return ga
}

// String implements the fmt.Stringer.
func (ga *Games) String() string {
	var builder strings.Builder
	builder.WriteString("Games(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ga.ID))
	builder.WriteString("table_id=")
	builder.WriteString(fmt.Sprintf("%v", ga.TableID))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", ga.Status))
	builder.WriteString(", ")
	builder.WriteString("seats=")
	builder.WriteString(fmt.Sprintf("%v", ga.Seats))
	builder.WriteString(", ")
	builder.WriteString("pot=")
	builder.WriteString(fmt.Sprintf("%v", ga.Pot))
	builder.WriteString(", ")
	builder.WriteString("button=")
	builder.WriteString(fmt.Sprintf("%v", ga.Button))
	builder.WriteString(", ")
	builder.WriteString("sb=")
	builder.WriteString(fmt.Sprintf("%v", ga.Sb))
	builder.WriteString(", ")
	builder.WriteString("bb=")
	builder.WriteString(fmt.Sprintf("%v", ga.Bb))
	builder.WriteString(", ")
	builder.WriteString("stakes=")
	builder.WriteString(fmt.Sprintf("%v", ga.Stakes))
	builder.WriteString(", ")
	builder.WriteString("players=")
	builder.WriteString(fmt.Sprintf("%v", ga.Players))
	builder.WriteString(", ")
	builder.WriteString("winners=")
	builder.WriteString(fmt.Sprintf("%v", ga.Winners))
	builder.WriteString(", ")
	builder.WriteString("board=")
	builder.WriteString(fmt.Sprintf("%v", ga.Board))
	builder.WriteString(", ")
	builder.WriteString("duration=")
	builder.WriteString(fmt.Sprintf("%v", ga.Duration))
	builder.WriteString(", ")
	builder.WriteString("started_at=")
	builder.WriteString(ga.StartedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ga.CompletedAt; v != nil {
		builder.WriteString("completed_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("settled=")
	builder.WriteString(fmt.Sprintf("%v", ga.Settled))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ga.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(ga.CreatedBy)
	builder.WriteString(", ")
	builder.WriteString("updated_by=")
	builder.WriteString(ga.UpdatedBy)
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ga.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ga.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// GamesSlice is a parsable slice of Games.
type GamesSlice []*Games
