// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/internal"
	"hamster/internal/db/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GamesQuery is the builder for querying Games entities.
type GamesQuery struct {
	config
	ctx        *QueryContext
	order      []games.OrderOption
	inters     []Interceptor
	predicates []predicate.Games
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the GamesQuery builder.
func (gq *GamesQuery) Where(ps ...predicate.Games) *GamesQuery {
	gq.predicates = append(gq.predicates, ps...)
	return gq
}

// Limit the number of records to be returned by this query.
func (gq *GamesQuery) Limit(limit int) *GamesQuery {
	gq.ctx.Limit = &limit
	return gq
}

// Offset to start from.
func (gq *GamesQuery) Offset(offset int) *GamesQuery {
	gq.ctx.Offset = &offset
	return gq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (gq *GamesQuery) Unique(unique bool) *GamesQuery {
	gq.ctx.Unique = &unique
	return gq
}

// Order specifies how the records should be ordered.
func (gq *GamesQuery) Order(o ...games.OrderOption) *GamesQuery {
	gq.order = append(gq.order, o...)
	return gq
}

// First returns the first Games entity from the query.
// Returns a *NotFoundError when no Games was found.
func (gq *GamesQuery) First(ctx context.Context) (*Games, error) {
	nodes, err := gq.Limit(1).All(setContextOp(ctx, gq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{games.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (gq *GamesQuery) FirstX(ctx context.Context) *Games {
	node, err := gq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Games ID from the query.
// Returns a *NotFoundError when no Games ID was found.
func (gq *GamesQuery) FirstID(ctx context.Context) (id uint64, err error) {
	var ids []uint64
	if ids, err = gq.Limit(1).IDs(setContextOp(ctx, gq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{games.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (gq *GamesQuery) FirstIDX(ctx context.Context) uint64 {
	id, err := gq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Games entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Games entity is found.
// Returns a *NotFoundError when no Games entities are found.
func (gq *GamesQuery) Only(ctx context.Context) (*Games, error) {
	nodes, err := gq.Limit(2).All(setContextOp(ctx, gq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{games.Label}
	default:
		return nil, &NotSingularError{games.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (gq *GamesQuery) OnlyX(ctx context.Context) *Games {
	node, err := gq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Games ID in the query.
// Returns a *NotSingularError when more than one Games ID is found.
// Returns a *NotFoundError when no entities are found.
func (gq *GamesQuery) OnlyID(ctx context.Context) (id uint64, err error) {
	var ids []uint64
	if ids, err = gq.Limit(2).IDs(setContextOp(ctx, gq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{games.Label}
	default:
		err = &NotSingularError{games.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (gq *GamesQuery) OnlyIDX(ctx context.Context) uint64 {
	id, err := gq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of GamesSlice.
func (gq *GamesQuery) All(ctx context.Context) ([]*Games, error) {
	ctx = setContextOp(ctx, gq.ctx, ent.OpQueryAll)
	if err := gq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Games, *GamesQuery]()
	return withInterceptors[[]*Games](ctx, gq, qr, gq.inters)
}

// AllX is like All, but panics if an error occurs.
func (gq *GamesQuery) AllX(ctx context.Context) []*Games {
	nodes, err := gq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Games IDs.
func (gq *GamesQuery) IDs(ctx context.Context) (ids []uint64, err error) {
	if gq.ctx.Unique == nil && gq.path != nil {
		gq.Unique(true)
	}
	ctx = setContextOp(ctx, gq.ctx, ent.OpQueryIDs)
	if err = gq.Select(games.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (gq *GamesQuery) IDsX(ctx context.Context) []uint64 {
	ids, err := gq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (gq *GamesQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, gq.ctx, ent.OpQueryCount)
	if err := gq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, gq, querierCount[*GamesQuery](), gq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (gq *GamesQuery) CountX(ctx context.Context) int {
	count, err := gq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (gq *GamesQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, gq.ctx, ent.OpQueryExist)
	switch _, err := gq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (gq *GamesQuery) ExistX(ctx context.Context) bool {
	exist, err := gq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the GamesQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (gq *GamesQuery) Clone() *GamesQuery {
	if gq == nil {
		return nil
	}
	return &GamesQuery{
		config:     gq.config,
		ctx:        gq.ctx.Clone(),
		order:      append([]games.OrderOption{}, gq.order...),
		inters:     append([]Interceptor{}, gq.inters...),
		predicates: append([]predicate.Games{}, gq.predicates...),
		// clone intermediate query.
		sql:  gq.sql.Clone(),
		path: gq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TableID uint64 `json:"table_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Games.Query().
//		GroupBy(games.FieldTableID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (gq *GamesQuery) GroupBy(field string, fields ...string) *GamesGroupBy {
	gq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &GamesGroupBy{build: gq}
	grbuild.flds = &gq.ctx.Fields
	grbuild.label = games.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TableID uint64 `json:"table_id,omitempty"`
//	}
//
//	client.Games.Query().
//		Select(games.FieldTableID).
//		Scan(ctx, &v)
func (gq *GamesQuery) Select(fields ...string) *GamesSelect {
	gq.ctx.Fields = append(gq.ctx.Fields, fields...)
	sbuild := &GamesSelect{GamesQuery: gq}
	sbuild.label = games.Label
	sbuild.flds, sbuild.scan = &gq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a GamesSelect configured with the given aggregations.
func (gq *GamesQuery) Aggregate(fns ...AggregateFunc) *GamesSelect {
	return gq.Select().Aggregate(fns...)
}

func (gq *GamesQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range gq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, gq); err != nil {
				return err
			}
		}
	}
	for _, f := range gq.ctx.Fields {
		if !games.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if gq.path != nil {
		prev, err := gq.path(ctx)
		if err != nil {
			return err
		}
		gq.sql = prev
	}
	return nil
}

func (gq *GamesQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Games, error) {
	var (
		nodes = []*Games{}
		_spec = gq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Games).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Games{config: gq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	_spec.Node.Schema = gq.schemaConfig.Games
	ctx = internal.NewSchemaConfigContext(ctx, gq.schemaConfig)
	if len(gq.modifiers) > 0 {
		_spec.Modifiers = gq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, gq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (gq *GamesQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := gq.querySpec()
	_spec.Node.Schema = gq.schemaConfig.Games
	ctx = internal.NewSchemaConfigContext(ctx, gq.schemaConfig)
	if len(gq.modifiers) > 0 {
		_spec.Modifiers = gq.modifiers
	}
	_spec.Node.Columns = gq.ctx.Fields
	if len(gq.ctx.Fields) > 0 {
		_spec.Unique = gq.ctx.Unique != nil && *gq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, gq.driver, _spec)
}

func (gq *GamesQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(games.Table, games.Columns, sqlgraph.NewFieldSpec(games.FieldID, field.TypeUint64))
	_spec.From = gq.sql
	if unique := gq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if gq.path != nil {
		_spec.Unique = true
	}
	if fields := gq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, games.FieldID)
		for i := range fields {
			if fields[i] != games.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := gq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := gq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := gq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := gq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (gq *GamesQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(gq.driver.Dialect())
	t1 := builder.Table(games.Table)
	columns := gq.ctx.Fields
	if len(columns) == 0 {
		columns = games.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if gq.sql != nil {
		selector = gq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if gq.ctx.Unique != nil && *gq.ctx.Unique {
		selector.Distinct()
	}
	t1.Schema(gq.schemaConfig.Games)
	ctx = internal.NewSchemaConfigContext(ctx, gq.schemaConfig)
	selector.WithContext(ctx)
	for _, m := range gq.modifiers {
		m(selector)
	}
	for _, p := range gq.predicates {
		p(selector)
	}
	for _, p := range gq.order {
		p(selector)
	}
	if offset := gq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := gq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (gq *GamesQuery) ForUpdate(opts ...sql.LockOption) *GamesQuery {
	if gq.driver.Dialect() == dialect.Postgres {
		gq.Unique(false)
	}
	gq.modifiers = append(gq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return gq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (gq *GamesQuery) ForShare(opts ...sql.LockOption) *GamesQuery {
	if gq.driver.Dialect() == dialect.Postgres {
		gq.Unique(false)
	}
	gq.modifiers = append(gq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return gq
}

// GamesGroupBy is the group-by builder for Games entities.
type GamesGroupBy struct {
	selector
	build *GamesQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ggb *GamesGroupBy) Aggregate(fns ...AggregateFunc) *GamesGroupBy {
	ggb.fns = append(ggb.fns, fns...)
	return ggb
}

// Scan applies the selector query and scans the result into the given value.
func (ggb *GamesGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ggb.build.ctx, ent.OpQueryGroupBy)
	if err := ggb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*GamesQuery, *GamesGroupBy](ctx, ggb.build, ggb, ggb.build.inters, v)
}

func (ggb *GamesGroupBy) sqlScan(ctx context.Context, root *GamesQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ggb.fns))
	for _, fn := range ggb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ggb.flds)+len(ggb.fns))
		for _, f := range *ggb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ggb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ggb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// GamesSelect is the builder for selecting fields of Games entities.
type GamesSelect struct {
	*GamesQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (gs *GamesSelect) Aggregate(fns ...AggregateFunc) *GamesSelect {
	gs.fns = append(gs.fns, fns...)
	return gs
}

// Scan applies the selector query and scans the result into the given value.
func (gs *GamesSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, gs.ctx, ent.OpQuerySelect)
	if err := gs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*GamesQuery, *GamesSelect](ctx, gs.GamesQuery, gs, gs.inters, v)
}

func (gs *GamesSelect) sqlScan(ctx context.Context, root *GamesQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(gs.fns))
	for _, fn := range gs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*gs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := gs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
