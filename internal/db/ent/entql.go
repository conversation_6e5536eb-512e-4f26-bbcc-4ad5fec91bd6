// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/tables"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/entql"
	"entgo.io/ent/schema/field"
)

// schemaGraph holds a representation of ent/schema at runtime.
var schemaGraph = func() *sqlgraph.Schema {
	graph := &sqlgraph.Schema{Nodes: make([]*sqlgraph.Node, 2)}
	graph.Nodes[0] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   games.Table,
			Columns: games.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint64,
				Column: games.FieldID,
			},
		},
		Type: "Games",
		Fields: map[string]*sqlgraph.FieldSpec{
			games.FieldTableID:     {Type: field.TypeUint64, Column: games.FieldTableID},
			games.FieldStatus:      {Type: field.TypeEnum, Column: games.FieldStatus},
			games.FieldSeats:       {Type: field.TypeJSON, Column: games.FieldSeats},
			games.FieldPot:         {Type: field.TypeUint64, Column: games.FieldPot},
			games.FieldButton:      {Type: field.TypeInt, Column: games.FieldButton},
			games.FieldSb:          {Type: field.TypeInt, Column: games.FieldSb},
			games.FieldBb:          {Type: field.TypeInt, Column: games.FieldBb},
			games.FieldStakes:      {Type: field.TypeJSON, Column: games.FieldStakes},
			games.FieldPlayers:     {Type: field.TypeJSON, Column: games.FieldPlayers},
			games.FieldWinners:     {Type: field.TypeJSON, Column: games.FieldWinners},
			games.FieldBoard:       {Type: field.TypeJSON, Column: games.FieldBoard},
			games.FieldDuration:    {Type: field.TypeInt64, Column: games.FieldDuration},
			games.FieldStartedAt:   {Type: field.TypeTime, Column: games.FieldStartedAt},
			games.FieldCompletedAt: {Type: field.TypeTime, Column: games.FieldCompletedAt},
			games.FieldSettled:     {Type: field.TypeBool, Column: games.FieldSettled},
			games.FieldCreatedAt:   {Type: field.TypeTime, Column: games.FieldCreatedAt},
			games.FieldCreatedBy:   {Type: field.TypeString, Column: games.FieldCreatedBy},
			games.FieldUpdatedBy:   {Type: field.TypeString, Column: games.FieldUpdatedBy},
			games.FieldUpdatedAt:   {Type: field.TypeTime, Column: games.FieldUpdatedAt},
			games.FieldDeletedAt:   {Type: field.TypeTime, Column: games.FieldDeletedAt},
		},
	}
	graph.Nodes[1] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   tables.Table,
			Columns: tables.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint64,
				Column: tables.FieldID,
			},
		},
		Type: "Tables",
		Fields: map[string]*sqlgraph.FieldSpec{
			tables.FieldName:       {Type: field.TypeString, Column: tables.FieldName},
			tables.FieldCode:       {Type: field.TypeString, Column: tables.FieldCode},
			tables.FieldType:       {Type: field.TypeEnum, Column: tables.FieldType},
			tables.FieldStatus:     {Type: field.TypeEnum, Column: tables.FieldStatus},
			tables.FieldBigBlind:   {Type: field.TypeUint64, Column: tables.FieldBigBlind},
			tables.FieldSmallBlind: {Type: field.TypeUint64, Column: tables.FieldSmallBlind},
			tables.FieldMinBuyIn:   {Type: field.TypeUint64, Column: tables.FieldMinBuyIn},
			tables.FieldMaxBuyIn:   {Type: field.TypeUint64, Column: tables.FieldMaxBuyIn},
			tables.FieldIsVisible:  {Type: field.TypeBool, Column: tables.FieldIsVisible},
			tables.FieldExpiredAt:  {Type: field.TypeTime, Column: tables.FieldExpiredAt},
			tables.FieldCreatedAt:  {Type: field.TypeTime, Column: tables.FieldCreatedAt},
			tables.FieldCreatedBy:  {Type: field.TypeString, Column: tables.FieldCreatedBy},
			tables.FieldUpdatedBy:  {Type: field.TypeString, Column: tables.FieldUpdatedBy},
			tables.FieldUpdatedAt:  {Type: field.TypeTime, Column: tables.FieldUpdatedAt},
			tables.FieldDeletedAt:  {Type: field.TypeTime, Column: tables.FieldDeletedAt},
		},
	}
	return graph
}()

// predicateAdder wraps the addPredicate method.
// All update, update-one and query builders implement this interface.
type predicateAdder interface {
	addPredicate(func(s *sql.Selector))
}

// addPredicate implements the predicateAdder interface.
func (gq *GamesQuery) addPredicate(pred func(s *sql.Selector)) {
	gq.predicates = append(gq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the GamesQuery builder.
func (gq *GamesQuery) Filter() *GamesFilter {
	return &GamesFilter{config: gq.config, predicateAdder: gq}
}

// addPredicate implements the predicateAdder interface.
func (m *GamesMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the GamesMutation builder.
func (m *GamesMutation) Filter() *GamesFilter {
	return &GamesFilter{config: m.config, predicateAdder: m}
}

// GamesFilter provides a generic filtering capability at runtime for GamesQuery.
type GamesFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *GamesFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[0].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint64 predicate on the id field.
func (f *GamesFilter) WhereID(p entql.Uint64P) {
	f.Where(p.Field(games.FieldID))
}

// WhereTableID applies the entql uint64 predicate on the table_id field.
func (f *GamesFilter) WhereTableID(p entql.Uint64P) {
	f.Where(p.Field(games.FieldTableID))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *GamesFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(games.FieldStatus))
}

// WhereSeats applies the entql json.RawMessage predicate on the seats field.
func (f *GamesFilter) WhereSeats(p entql.BytesP) {
	f.Where(p.Field(games.FieldSeats))
}

// WherePot applies the entql uint64 predicate on the pot field.
func (f *GamesFilter) WherePot(p entql.Uint64P) {
	f.Where(p.Field(games.FieldPot))
}

// WhereButton applies the entql int predicate on the button field.
func (f *GamesFilter) WhereButton(p entql.IntP) {
	f.Where(p.Field(games.FieldButton))
}

// WhereSb applies the entql int predicate on the sb field.
func (f *GamesFilter) WhereSb(p entql.IntP) {
	f.Where(p.Field(games.FieldSb))
}

// WhereBb applies the entql int predicate on the bb field.
func (f *GamesFilter) WhereBb(p entql.IntP) {
	f.Where(p.Field(games.FieldBb))
}

// WhereStakes applies the entql json.RawMessage predicate on the stakes field.
func (f *GamesFilter) WhereStakes(p entql.BytesP) {
	f.Where(p.Field(games.FieldStakes))
}

// WherePlayers applies the entql json.RawMessage predicate on the players field.
func (f *GamesFilter) WherePlayers(p entql.BytesP) {
	f.Where(p.Field(games.FieldPlayers))
}

// WhereWinners applies the entql json.RawMessage predicate on the winners field.
func (f *GamesFilter) WhereWinners(p entql.BytesP) {
	f.Where(p.Field(games.FieldWinners))
}

// WhereBoard applies the entql json.RawMessage predicate on the board field.
func (f *GamesFilter) WhereBoard(p entql.BytesP) {
	f.Where(p.Field(games.FieldBoard))
}

// WhereDuration applies the entql int64 predicate on the duration field.
func (f *GamesFilter) WhereDuration(p entql.Int64P) {
	f.Where(p.Field(games.FieldDuration))
}

// WhereStartedAt applies the entql time.Time predicate on the started_at field.
func (f *GamesFilter) WhereStartedAt(p entql.TimeP) {
	f.Where(p.Field(games.FieldStartedAt))
}

// WhereCompletedAt applies the entql time.Time predicate on the completed_at field.
func (f *GamesFilter) WhereCompletedAt(p entql.TimeP) {
	f.Where(p.Field(games.FieldCompletedAt))
}

// WhereSettled applies the entql bool predicate on the settled field.
func (f *GamesFilter) WhereSettled(p entql.BoolP) {
	f.Where(p.Field(games.FieldSettled))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *GamesFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(games.FieldCreatedAt))
}

// WhereCreatedBy applies the entql string predicate on the created_by field.
func (f *GamesFilter) WhereCreatedBy(p entql.StringP) {
	f.Where(p.Field(games.FieldCreatedBy))
}

// WhereUpdatedBy applies the entql string predicate on the updated_by field.
func (f *GamesFilter) WhereUpdatedBy(p entql.StringP) {
	f.Where(p.Field(games.FieldUpdatedBy))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *GamesFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(games.FieldUpdatedAt))
}

// WhereDeletedAt applies the entql time.Time predicate on the deleted_at field.
func (f *GamesFilter) WhereDeletedAt(p entql.TimeP) {
	f.Where(p.Field(games.FieldDeletedAt))
}

// addPredicate implements the predicateAdder interface.
func (tq *TablesQuery) addPredicate(pred func(s *sql.Selector)) {
	tq.predicates = append(tq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the TablesQuery builder.
func (tq *TablesQuery) Filter() *TablesFilter {
	return &TablesFilter{config: tq.config, predicateAdder: tq}
}

// addPredicate implements the predicateAdder interface.
func (m *TablesMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the TablesMutation builder.
func (m *TablesMutation) Filter() *TablesFilter {
	return &TablesFilter{config: m.config, predicateAdder: m}
}

// TablesFilter provides a generic filtering capability at runtime for TablesQuery.
type TablesFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *TablesFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[1].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint64 predicate on the id field.
func (f *TablesFilter) WhereID(p entql.Uint64P) {
	f.Where(p.Field(tables.FieldID))
}

// WhereName applies the entql string predicate on the name field.
func (f *TablesFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(tables.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *TablesFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(tables.FieldCode))
}

// WhereType applies the entql string predicate on the type field.
func (f *TablesFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(tables.FieldType))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *TablesFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(tables.FieldStatus))
}

// WhereBigBlind applies the entql uint64 predicate on the big_blind field.
func (f *TablesFilter) WhereBigBlind(p entql.Uint64P) {
	f.Where(p.Field(tables.FieldBigBlind))
}

// WhereSmallBlind applies the entql uint64 predicate on the small_blind field.
func (f *TablesFilter) WhereSmallBlind(p entql.Uint64P) {
	f.Where(p.Field(tables.FieldSmallBlind))
}

// WhereMinBuyIn applies the entql uint64 predicate on the min_buy_in field.
func (f *TablesFilter) WhereMinBuyIn(p entql.Uint64P) {
	f.Where(p.Field(tables.FieldMinBuyIn))
}

// WhereMaxBuyIn applies the entql uint64 predicate on the max_buy_in field.
func (f *TablesFilter) WhereMaxBuyIn(p entql.Uint64P) {
	f.Where(p.Field(tables.FieldMaxBuyIn))
}

// WhereIsVisible applies the entql bool predicate on the is_visible field.
func (f *TablesFilter) WhereIsVisible(p entql.BoolP) {
	f.Where(p.Field(tables.FieldIsVisible))
}

// WhereExpiredAt applies the entql time.Time predicate on the expired_at field.
func (f *TablesFilter) WhereExpiredAt(p entql.TimeP) {
	f.Where(p.Field(tables.FieldExpiredAt))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *TablesFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(tables.FieldCreatedAt))
}

// WhereCreatedBy applies the entql string predicate on the created_by field.
func (f *TablesFilter) WhereCreatedBy(p entql.StringP) {
	f.Where(p.Field(tables.FieldCreatedBy))
}

// WhereUpdatedBy applies the entql string predicate on the updated_by field.
func (f *TablesFilter) WhereUpdatedBy(p entql.StringP) {
	f.Where(p.Field(tables.FieldUpdatedBy))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *TablesFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(tables.FieldUpdatedAt))
}

// WhereDeletedAt applies the entql time.Time predicate on the deleted_at field.
func (f *TablesFilter) WhereDeletedAt(p entql.TimeP) {
	f.Where(p.Field(tables.FieldDeletedAt))
}
