// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"fmt"
	"hamster/internal/db/ent/tables"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Tables is the model entity for the Tables schema.
type Tables struct {
	config `json:"-"`
	// ID of the ent.
	ID uint64 `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Code holds the value of the "code" field.
	Code string `json:"code,omitempty"`
	// Type holds the value of the "type" field.
	Type tables.Type `json:"type,omitempty"`
	// Status holds the value of the "status" field.
	Status tables.Status `json:"status,omitempty"`
	// BigBlind holds the value of the "big_blind" field.
	BigBlind uint64 `json:"big_blind,omitempty"`
	// SmallBlind holds the value of the "small_blind" field.
	SmallBlind uint64 `json:"small_blind,omitempty"`
	// MinBuyIn holds the value of the "min_buy_in" field.
	MinBuyIn uint64 `json:"min_buy_in,omitempty"`
	// MaxBuyIn holds the value of the "max_buy_in" field.
	MaxBuyIn uint64 `json:"max_buy_in,omitempty"`
	// IsVisible holds the value of the "is_visible" field.
	IsVisible bool `json:"is_visible,omitempty"`
	// ExpiredAt holds the value of the "expired_at" field.
	ExpiredAt *time.Time `json:"expired_at,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// CreatedBy holds the value of the "created_by" field.
	CreatedBy string `json:"created_by,omitempty"`
	// UpdatedBy holds the value of the "updated_by" field.
	UpdatedBy string `json:"updated_by,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    *time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Tables) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case tables.FieldIsVisible:
			values[i] = new(sql.NullBool)
		case tables.FieldID, tables.FieldBigBlind, tables.FieldSmallBlind, tables.FieldMinBuyIn, tables.FieldMaxBuyIn:
			values[i] = new(sql.NullInt64)
		case tables.FieldName, tables.FieldCode, tables.FieldType, tables.FieldStatus, tables.FieldCreatedBy, tables.FieldUpdatedBy:
			values[i] = new(sql.NullString)
		case tables.FieldExpiredAt, tables.FieldCreatedAt, tables.FieldUpdatedAt, tables.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Tables fields.
func (t *Tables) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case tables.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			t.ID = uint64(value.Int64)
		case tables.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				t.Name = value.String
			}
		case tables.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				t.Code = value.String
			}
		case tables.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				t.Type = tables.Type(value.String)
			}
		case tables.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				t.Status = tables.Status(value.String)
			}
		case tables.FieldBigBlind:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field big_blind", values[i])
			} else if value.Valid {
				t.BigBlind = uint64(value.Int64)
			}
		case tables.FieldSmallBlind:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field small_blind", values[i])
			} else if value.Valid {
				t.SmallBlind = uint64(value.Int64)
			}
		case tables.FieldMinBuyIn:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field min_buy_in", values[i])
			} else if value.Valid {
				t.MinBuyIn = uint64(value.Int64)
			}
		case tables.FieldMaxBuyIn:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_buy_in", values[i])
			} else if value.Valid {
				t.MaxBuyIn = uint64(value.Int64)
			}
		case tables.FieldIsVisible:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_visible", values[i])
			} else if value.Valid {
				t.IsVisible = value.Bool
			}
		case tables.FieldExpiredAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expired_at", values[i])
			} else if value.Valid {
				t.ExpiredAt = new(time.Time)
				*t.ExpiredAt = value.Time
			}
		case tables.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				t.CreatedAt = value.Time
			}
		case tables.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				t.CreatedBy = value.String
			}
		case tables.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				t.UpdatedBy = value.String
			}
		case tables.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				t.UpdatedAt = value.Time
			}
		case tables.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				t.DeletedAt = new(time.Time)
				*t.DeletedAt = value.Time
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Tables.
// This includes values selected through modifiers, order, etc.
func (t *Tables) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Tables.
// Note that you need to call Tables.Unwrap() before calling this method if this Tables
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Tables) Update() *TablesUpdateOne {
	return NewTablesClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Tables entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Tables) Unwrap() *Tables {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Tables is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Tables) String() string {
	var builder strings.Builder
	builder.WriteString("Tables(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("name=")
	builder.WriteString(t.Name)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(t.Code)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", t.Type))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", t.Status))
	builder.WriteString(", ")
	builder.WriteString("big_blind=")
	builder.WriteString(fmt.Sprintf("%v", t.BigBlind))
	builder.WriteString(", ")
	builder.WriteString("small_blind=")
	builder.WriteString(fmt.Sprintf("%v", t.SmallBlind))
	builder.WriteString(", ")
	builder.WriteString("min_buy_in=")
	builder.WriteString(fmt.Sprintf("%v", t.MinBuyIn))
	builder.WriteString(", ")
	builder.WriteString("max_buy_in=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxBuyIn))
	builder.WriteString(", ")
	builder.WriteString("is_visible=")
	builder.WriteString(fmt.Sprintf("%v", t.IsVisible))
	builder.WriteString(", ")
	if v := t.ExpiredAt; v != nil {
		builder.WriteString("expired_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(t.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(t.CreatedBy)
	builder.WriteString(", ")
	builder.WriteString("updated_by=")
	builder.WriteString(t.UpdatedBy)
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(t.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := t.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// TablesSlice is a parsable slice of Tables.
type TablesSlice []*Tables
