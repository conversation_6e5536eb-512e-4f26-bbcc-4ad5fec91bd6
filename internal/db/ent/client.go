// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"hamster/internal/db/ent/migrate"

	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/tables"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	stdsql "database/sql"
	"hamster/internal/db/ent/internal"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Games is the client for interacting with the Games builders.
	Games *GamesClient
	// Tables is the client for interacting with the Tables builders.
	Tables *TablesClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Games = NewGamesClient(c.config)
	c.Tables = NewTablesClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
		// schemaConfig contains alternative names for all tables.
		schemaConfig SchemaConfig
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.schemaConfig = DefaultSchemaConfig
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:    ctx,
		config: cfg,
		Games:  NewGamesClient(cfg),
		Tables: NewTablesClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:    ctx,
		config: cfg,
		Games:  NewGamesClient(cfg),
		Tables: NewTablesClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Games.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Games.Use(hooks...)
	c.Tables.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Games.Intercept(interceptors...)
	c.Tables.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *GamesMutation:
		return c.Games.mutate(ctx, m)
	case *TablesMutation:
		return c.Tables.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// GamesClient is a client for the Games schema.
type GamesClient struct {
	config
}

// NewGamesClient returns a client for the Games from the given config.
func NewGamesClient(c config) *GamesClient {
	return &GamesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `games.Hooks(f(g(h())))`.
func (c *GamesClient) Use(hooks ...Hook) {
	c.hooks.Games = append(c.hooks.Games, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `games.Intercept(f(g(h())))`.
func (c *GamesClient) Intercept(interceptors ...Interceptor) {
	c.inters.Games = append(c.inters.Games, interceptors...)
}

// Create returns a builder for creating a Games entity.
func (c *GamesClient) Create() *GamesCreate {
	mutation := newGamesMutation(c.config, OpCreate)
	return &GamesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Games entities.
func (c *GamesClient) CreateBulk(builders ...*GamesCreate) *GamesCreateBulk {
	return &GamesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GamesClient) MapCreateBulk(slice any, setFunc func(*GamesCreate, int)) *GamesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GamesCreateBulk{err: fmt.Errorf("calling to GamesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GamesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GamesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Games.
func (c *GamesClient) Update() *GamesUpdate {
	mutation := newGamesMutation(c.config, OpUpdate)
	return &GamesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GamesClient) UpdateOne(ga *Games) *GamesUpdateOne {
	mutation := newGamesMutation(c.config, OpUpdateOne, withGames(ga))
	return &GamesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GamesClient) UpdateOneID(id uint64) *GamesUpdateOne {
	mutation := newGamesMutation(c.config, OpUpdateOne, withGamesID(id))
	return &GamesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Games.
func (c *GamesClient) Delete() *GamesDelete {
	mutation := newGamesMutation(c.config, OpDelete)
	return &GamesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GamesClient) DeleteOne(ga *Games) *GamesDeleteOne {
	return c.DeleteOneID(ga.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GamesClient) DeleteOneID(id uint64) *GamesDeleteOne {
	builder := c.Delete().Where(games.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GamesDeleteOne{builder}
}

// Query returns a query builder for Games.
func (c *GamesClient) Query() *GamesQuery {
	return &GamesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGames},
		inters: c.Interceptors(),
	}
}

// Get returns a Games entity by its id.
func (c *GamesClient) Get(ctx context.Context, id uint64) (*Games, error) {
	return c.Query().Where(games.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GamesClient) GetX(ctx context.Context, id uint64) *Games {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *GamesClient) Hooks() []Hook {
	return c.hooks.Games
}

// Interceptors returns the client interceptors.
func (c *GamesClient) Interceptors() []Interceptor {
	return c.inters.Games
}

func (c *GamesClient) mutate(ctx context.Context, m *GamesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GamesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GamesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GamesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GamesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Games mutation op: %q", m.Op())
	}
}

// TablesClient is a client for the Tables schema.
type TablesClient struct {
	config
}

// NewTablesClient returns a client for the Tables from the given config.
func NewTablesClient(c config) *TablesClient {
	return &TablesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tables.Hooks(f(g(h())))`.
func (c *TablesClient) Use(hooks ...Hook) {
	c.hooks.Tables = append(c.hooks.Tables, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tables.Intercept(f(g(h())))`.
func (c *TablesClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tables = append(c.inters.Tables, interceptors...)
}

// Create returns a builder for creating a Tables entity.
func (c *TablesClient) Create() *TablesCreate {
	mutation := newTablesMutation(c.config, OpCreate)
	return &TablesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tables entities.
func (c *TablesClient) CreateBulk(builders ...*TablesCreate) *TablesCreateBulk {
	return &TablesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TablesClient) MapCreateBulk(slice any, setFunc func(*TablesCreate, int)) *TablesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TablesCreateBulk{err: fmt.Errorf("calling to TablesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TablesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TablesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tables.
func (c *TablesClient) Update() *TablesUpdate {
	mutation := newTablesMutation(c.config, OpUpdate)
	return &TablesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TablesClient) UpdateOne(t *Tables) *TablesUpdateOne {
	mutation := newTablesMutation(c.config, OpUpdateOne, withTables(t))
	return &TablesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TablesClient) UpdateOneID(id uint64) *TablesUpdateOne {
	mutation := newTablesMutation(c.config, OpUpdateOne, withTablesID(id))
	return &TablesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tables.
func (c *TablesClient) Delete() *TablesDelete {
	mutation := newTablesMutation(c.config, OpDelete)
	return &TablesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TablesClient) DeleteOne(t *Tables) *TablesDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TablesClient) DeleteOneID(id uint64) *TablesDeleteOne {
	builder := c.Delete().Where(tables.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TablesDeleteOne{builder}
}

// Query returns a query builder for Tables.
func (c *TablesClient) Query() *TablesQuery {
	return &TablesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTables},
		inters: c.Interceptors(),
	}
}

// Get returns a Tables entity by its id.
func (c *TablesClient) Get(ctx context.Context, id uint64) (*Tables, error) {
	return c.Query().Where(tables.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TablesClient) GetX(ctx context.Context, id uint64) *Tables {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TablesClient) Hooks() []Hook {
	return c.hooks.Tables
}

// Interceptors returns the client interceptors.
func (c *TablesClient) Interceptors() []Interceptor {
	return c.inters.Tables
}

func (c *TablesClient) mutate(ctx context.Context, m *TablesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TablesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TablesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TablesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TablesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Tables mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Games, Tables []ent.Hook
	}
	inters struct {
		Games, Tables []ent.Interceptor
	}
)

var (
	// DefaultSchemaConfig represents the default schema names for all tables as defined in ent/schema.
	DefaultSchemaConfig = SchemaConfig{
		Games:  tableSchemas[0],
		Tables: tableSchemas[0],
	}
	tableSchemas = [...]string{"public"}
)

// SchemaConfig represents alternative schema names for all tables
// that can be passed at runtime.
type SchemaConfig = internal.SchemaConfig

// AlternateSchemas allows alternate schema names to be
// passed into ent operations.
func AlternateSchema(schemaConfig SchemaConfig) Option {
	return func(c *config) {
		c.schemaConfig = schemaConfig
	}
}

// ExecContext allows calling the underlying ExecContext method of the driver if it is supported by it.
// See, database/sql#DB.ExecContext for more information.
func (c *config) ExecContext(ctx context.Context, query string, args ...any) (stdsql.Result, error) {
	ex, ok := c.driver.(interface {
		ExecContext(context.Context, string, ...any) (stdsql.Result, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.ExecContext is not supported")
	}
	return ex.ExecContext(ctx, query, args...)
}

// QueryContext allows calling the underlying QueryContext method of the driver if it is supported by it.
// See, database/sql#DB.QueryContext for more information.
func (c *config) QueryContext(ctx context.Context, query string, args ...any) (*stdsql.Rows, error) {
	q, ok := c.driver.(interface {
		QueryContext(context.Context, string, ...any) (*stdsql.Rows, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.QueryContext is not supported")
	}
	return q.QueryContext(ctx, query, args...)
}
