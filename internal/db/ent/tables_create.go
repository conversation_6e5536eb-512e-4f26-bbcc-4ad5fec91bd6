// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"hamster/internal/db/ent/tables"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TablesCreate is the builder for creating a Tables entity.
type TablesCreate struct {
	config
	mutation *TablesMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetN<PERSON> sets the "name" field.
func (tc *TablesCreate) SetName(s string) *TablesCreate {
	tc.mutation.SetName(s)
	return tc
}

// SetCode sets the "code" field.
func (tc *TablesCreate) SetCode(s string) *TablesCreate {
	tc.mutation.SetCode(s)
	return tc
}

// SetType sets the "type" field.
func (tc *TablesCreate) SetType(t tables.Type) *TablesCreate {
	tc.mutation.SetType(t)
	return tc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tc *TablesCreate) SetNillableType(t *tables.Type) *TablesCreate {
	if t != nil {
		tc.SetType(*t)
	}
	return tc
}

// SetStatus sets the "status" field.
func (tc *TablesCreate) SetStatus(t tables.Status) *TablesCreate {
	tc.mutation.SetStatus(t)
	return tc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tc *TablesCreate) SetNillableStatus(t *tables.Status) *TablesCreate {
	if t != nil {
		tc.SetStatus(*t)
	}
	return tc
}

// SetBigBlind sets the "big_blind" field.
func (tc *TablesCreate) SetBigBlind(u uint64) *TablesCreate {
	tc.mutation.SetBigBlind(u)
	return tc
}

// SetSmallBlind sets the "small_blind" field.
func (tc *TablesCreate) SetSmallBlind(u uint64) *TablesCreate {
	tc.mutation.SetSmallBlind(u)
	return tc
}

// SetMinBuyIn sets the "min_buy_in" field.
func (tc *TablesCreate) SetMinBuyIn(u uint64) *TablesCreate {
	tc.mutation.SetMinBuyIn(u)
	return tc
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (tc *TablesCreate) SetMaxBuyIn(u uint64) *TablesCreate {
	tc.mutation.SetMaxBuyIn(u)
	return tc
}

// SetIsVisible sets the "is_visible" field.
func (tc *TablesCreate) SetIsVisible(b bool) *TablesCreate {
	tc.mutation.SetIsVisible(b)
	return tc
}

// SetNillableIsVisible sets the "is_visible" field if the given value is not nil.
func (tc *TablesCreate) SetNillableIsVisible(b *bool) *TablesCreate {
	if b != nil {
		tc.SetIsVisible(*b)
	}
	return tc
}

// SetExpiredAt sets the "expired_at" field.
func (tc *TablesCreate) SetExpiredAt(t time.Time) *TablesCreate {
	tc.mutation.SetExpiredAt(t)
	return tc
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tc *TablesCreate) SetNillableExpiredAt(t *time.Time) *TablesCreate {
	if t != nil {
		tc.SetExpiredAt(*t)
	}
	return tc
}

// SetCreatedAt sets the "created_at" field.
func (tc *TablesCreate) SetCreatedAt(t time.Time) *TablesCreate {
	tc.mutation.SetCreatedAt(t)
	return tc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tc *TablesCreate) SetNillableCreatedAt(t *time.Time) *TablesCreate {
	if t != nil {
		tc.SetCreatedAt(*t)
	}
	return tc
}

// SetCreatedBy sets the "created_by" field.
func (tc *TablesCreate) SetCreatedBy(s string) *TablesCreate {
	tc.mutation.SetCreatedBy(s)
	return tc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (tc *TablesCreate) SetNillableCreatedBy(s *string) *TablesCreate {
	if s != nil {
		tc.SetCreatedBy(*s)
	}
	return tc
}

// SetUpdatedBy sets the "updated_by" field.
func (tc *TablesCreate) SetUpdatedBy(s string) *TablesCreate {
	tc.mutation.SetUpdatedBy(s)
	return tc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (tc *TablesCreate) SetNillableUpdatedBy(s *string) *TablesCreate {
	if s != nil {
		tc.SetUpdatedBy(*s)
	}
	return tc
}

// SetUpdatedAt sets the "updated_at" field.
func (tc *TablesCreate) SetUpdatedAt(t time.Time) *TablesCreate {
	tc.mutation.SetUpdatedAt(t)
	return tc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tc *TablesCreate) SetNillableUpdatedAt(t *time.Time) *TablesCreate {
	if t != nil {
		tc.SetUpdatedAt(*t)
	}
	return tc
}

// SetDeletedAt sets the "deleted_at" field.
func (tc *TablesCreate) SetDeletedAt(t time.Time) *TablesCreate {
	tc.mutation.SetDeletedAt(t)
	return tc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tc *TablesCreate) SetNillableDeletedAt(t *time.Time) *TablesCreate {
	if t != nil {
		tc.SetDeletedAt(*t)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TablesCreate) SetID(u uint64) *TablesCreate {
	tc.mutation.SetID(u)
	return tc
}

// Mutation returns the TablesMutation object of the builder.
func (tc *TablesCreate) Mutation() *TablesMutation {
	return tc.mutation
}

// Save creates the Tables in the database.
func (tc *TablesCreate) Save(ctx context.Context) (*Tables, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TablesCreate) SaveX(ctx context.Context) *Tables {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TablesCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TablesCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TablesCreate) defaults() {
	if _, ok := tc.mutation.GetType(); !ok {
		v := tables.DefaultType
		tc.mutation.SetType(v)
	}
	if _, ok := tc.mutation.Status(); !ok {
		v := tables.DefaultStatus
		tc.mutation.SetStatus(v)
	}
	if _, ok := tc.mutation.IsVisible(); !ok {
		v := tables.DefaultIsVisible
		tc.mutation.SetIsVisible(v)
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		v := tables.DefaultCreatedAt()
		tc.mutation.SetCreatedAt(v)
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		v := tables.DefaultUpdatedAt()
		tc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TablesCreate) check() error {
	if _, ok := tc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Tables.name"`)}
	}
	if v, ok := tc.mutation.Name(); ok {
		if err := tables.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tables.name": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Tables.code"`)}
	}
	if v, ok := tc.mutation.Code(); ok {
		if err := tables.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tables.code": %w`, err)}
		}
	}
	if _, ok := tc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Tables.type"`)}
	}
	if v, ok := tc.mutation.GetType(); ok {
		if err := tables.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Tables.type": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Tables.status"`)}
	}
	if v, ok := tc.mutation.Status(); ok {
		if err := tables.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tables.status": %w`, err)}
		}
	}
	if _, ok := tc.mutation.BigBlind(); !ok {
		return &ValidationError{Name: "big_blind", err: errors.New(`ent: missing required field "Tables.big_blind"`)}
	}
	if _, ok := tc.mutation.SmallBlind(); !ok {
		return &ValidationError{Name: "small_blind", err: errors.New(`ent: missing required field "Tables.small_blind"`)}
	}
	if _, ok := tc.mutation.MinBuyIn(); !ok {
		return &ValidationError{Name: "min_buy_in", err: errors.New(`ent: missing required field "Tables.min_buy_in"`)}
	}
	if _, ok := tc.mutation.MaxBuyIn(); !ok {
		return &ValidationError{Name: "max_buy_in", err: errors.New(`ent: missing required field "Tables.max_buy_in"`)}
	}
	if _, ok := tc.mutation.IsVisible(); !ok {
		return &ValidationError{Name: "is_visible", err: errors.New(`ent: missing required field "Tables.is_visible"`)}
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Tables.created_at"`)}
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Tables.updated_at"`)}
	}
	return nil
}

func (tc *TablesCreate) sqlSave(ctx context.Context) (*Tables, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint64(id)
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TablesCreate) createSpec() (*Tables, *sqlgraph.CreateSpec) {
	var (
		_node = &Tables{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(tables.Table, sqlgraph.NewFieldSpec(tables.FieldID, field.TypeUint64))
	)
	_spec.Schema = tc.schemaConfig.Tables
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.Name(); ok {
		_spec.SetField(tables.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := tc.mutation.Code(); ok {
		_spec.SetField(tables.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := tc.mutation.GetType(); ok {
		_spec.SetField(tables.FieldType, field.TypeEnum, value)
		_node.Type = value
	}
	if value, ok := tc.mutation.Status(); ok {
		_spec.SetField(tables.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := tc.mutation.BigBlind(); ok {
		_spec.SetField(tables.FieldBigBlind, field.TypeUint64, value)
		_node.BigBlind = value
	}
	if value, ok := tc.mutation.SmallBlind(); ok {
		_spec.SetField(tables.FieldSmallBlind, field.TypeUint64, value)
		_node.SmallBlind = value
	}
	if value, ok := tc.mutation.MinBuyIn(); ok {
		_spec.SetField(tables.FieldMinBuyIn, field.TypeUint64, value)
		_node.MinBuyIn = value
	}
	if value, ok := tc.mutation.MaxBuyIn(); ok {
		_spec.SetField(tables.FieldMaxBuyIn, field.TypeUint64, value)
		_node.MaxBuyIn = value
	}
	if value, ok := tc.mutation.IsVisible(); ok {
		_spec.SetField(tables.FieldIsVisible, field.TypeBool, value)
		_node.IsVisible = value
	}
	if value, ok := tc.mutation.ExpiredAt(); ok {
		_spec.SetField(tables.FieldExpiredAt, field.TypeTime, value)
		_node.ExpiredAt = &value
	}
	if value, ok := tc.mutation.CreatedAt(); ok {
		_spec.SetField(tables.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tc.mutation.CreatedBy(); ok {
		_spec.SetField(tables.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := tc.mutation.UpdatedBy(); ok {
		_spec.SetField(tables.FieldUpdatedBy, field.TypeString, value)
		_node.UpdatedBy = value
	}
	if value, ok := tc.mutation.UpdatedAt(); ok {
		_spec.SetField(tables.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tc.mutation.DeletedAt(); ok {
		_spec.SetField(tables.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tables.Create().
//		SetName(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TablesUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (tc *TablesCreate) OnConflict(opts ...sql.ConflictOption) *TablesUpsertOne {
	tc.conflict = opts
	return &TablesUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tables.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TablesCreate) OnConflictColumns(columns ...string) *TablesUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TablesUpsertOne{
		create: tc,
	}
}

type (
	// TablesUpsertOne is the builder for "upsert"-ing
	//  one Tables node.
	TablesUpsertOne struct {
		create *TablesCreate
	}

	// TablesUpsert is the "OnConflict" setter.
	TablesUpsert struct {
		*sql.UpdateSet
	}
)

// SetName sets the "name" field.
func (u *TablesUpsert) SetName(v string) *TablesUpsert {
	u.Set(tables.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TablesUpsert) UpdateName() *TablesUpsert {
	u.SetExcluded(tables.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *TablesUpsert) SetCode(v string) *TablesUpsert {
	u.Set(tables.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TablesUpsert) UpdateCode() *TablesUpsert {
	u.SetExcluded(tables.FieldCode)
	return u
}

// SetType sets the "type" field.
func (u *TablesUpsert) SetType(v tables.Type) *TablesUpsert {
	u.Set(tables.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TablesUpsert) UpdateType() *TablesUpsert {
	u.SetExcluded(tables.FieldType)
	return u
}

// SetStatus sets the "status" field.
func (u *TablesUpsert) SetStatus(v tables.Status) *TablesUpsert {
	u.Set(tables.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TablesUpsert) UpdateStatus() *TablesUpsert {
	u.SetExcluded(tables.FieldStatus)
	return u
}

// SetBigBlind sets the "big_blind" field.
func (u *TablesUpsert) SetBigBlind(v uint64) *TablesUpsert {
	u.Set(tables.FieldBigBlind, v)
	return u
}

// UpdateBigBlind sets the "big_blind" field to the value that was provided on create.
func (u *TablesUpsert) UpdateBigBlind() *TablesUpsert {
	u.SetExcluded(tables.FieldBigBlind)
	return u
}

// AddBigBlind adds v to the "big_blind" field.
func (u *TablesUpsert) AddBigBlind(v uint64) *TablesUpsert {
	u.Add(tables.FieldBigBlind, v)
	return u
}

// SetSmallBlind sets the "small_blind" field.
func (u *TablesUpsert) SetSmallBlind(v uint64) *TablesUpsert {
	u.Set(tables.FieldSmallBlind, v)
	return u
}

// UpdateSmallBlind sets the "small_blind" field to the value that was provided on create.
func (u *TablesUpsert) UpdateSmallBlind() *TablesUpsert {
	u.SetExcluded(tables.FieldSmallBlind)
	return u
}

// AddSmallBlind adds v to the "small_blind" field.
func (u *TablesUpsert) AddSmallBlind(v uint64) *TablesUpsert {
	u.Add(tables.FieldSmallBlind, v)
	return u
}

// SetMinBuyIn sets the "min_buy_in" field.
func (u *TablesUpsert) SetMinBuyIn(v uint64) *TablesUpsert {
	u.Set(tables.FieldMinBuyIn, v)
	return u
}

// UpdateMinBuyIn sets the "min_buy_in" field to the value that was provided on create.
func (u *TablesUpsert) UpdateMinBuyIn() *TablesUpsert {
	u.SetExcluded(tables.FieldMinBuyIn)
	return u
}

// AddMinBuyIn adds v to the "min_buy_in" field.
func (u *TablesUpsert) AddMinBuyIn(v uint64) *TablesUpsert {
	u.Add(tables.FieldMinBuyIn, v)
	return u
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (u *TablesUpsert) SetMaxBuyIn(v uint64) *TablesUpsert {
	u.Set(tables.FieldMaxBuyIn, v)
	return u
}

// UpdateMaxBuyIn sets the "max_buy_in" field to the value that was provided on create.
func (u *TablesUpsert) UpdateMaxBuyIn() *TablesUpsert {
	u.SetExcluded(tables.FieldMaxBuyIn)
	return u
}

// AddMaxBuyIn adds v to the "max_buy_in" field.
func (u *TablesUpsert) AddMaxBuyIn(v uint64) *TablesUpsert {
	u.Add(tables.FieldMaxBuyIn, v)
	return u
}

// SetIsVisible sets the "is_visible" field.
func (u *TablesUpsert) SetIsVisible(v bool) *TablesUpsert {
	u.Set(tables.FieldIsVisible, v)
	return u
}

// UpdateIsVisible sets the "is_visible" field to the value that was provided on create.
func (u *TablesUpsert) UpdateIsVisible() *TablesUpsert {
	u.SetExcluded(tables.FieldIsVisible)
	return u
}

// SetExpiredAt sets the "expired_at" field.
func (u *TablesUpsert) SetExpiredAt(v time.Time) *TablesUpsert {
	u.Set(tables.FieldExpiredAt, v)
	return u
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TablesUpsert) UpdateExpiredAt() *TablesUpsert {
	u.SetExcluded(tables.FieldExpiredAt)
	return u
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (u *TablesUpsert) ClearExpiredAt() *TablesUpsert {
	u.SetNull(tables.FieldExpiredAt)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TablesUpsert) SetUpdatedAt(v time.Time) *TablesUpsert {
	u.Set(tables.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TablesUpsert) UpdateUpdatedAt() *TablesUpsert {
	u.SetExcluded(tables.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TablesUpsert) SetDeletedAt(v time.Time) *TablesUpsert {
	u.Set(tables.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TablesUpsert) UpdateDeletedAt() *TablesUpsert {
	u.SetExcluded(tables.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TablesUpsert) ClearDeletedAt() *TablesUpsert {
	u.SetNull(tables.FieldDeletedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Tables.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tables.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TablesUpsertOne) UpdateNewValues() *TablesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tables.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(tables.FieldCreatedAt)
		}
		if _, exists := u.create.mutation.CreatedBy(); exists {
			s.SetIgnore(tables.FieldCreatedBy)
		}
		if _, exists := u.create.mutation.UpdatedBy(); exists {
			s.SetIgnore(tables.FieldUpdatedBy)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tables.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TablesUpsertOne) Ignore() *TablesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TablesUpsertOne) DoNothing() *TablesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TablesCreate.OnConflict
// documentation for more info.
func (u *TablesUpsertOne) Update(set func(*TablesUpsert)) *TablesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TablesUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *TablesUpsertOne) SetName(v string) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateName() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *TablesUpsertOne) SetCode(v string) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateCode() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateCode()
	})
}

// SetType sets the "type" field.
func (u *TablesUpsertOne) SetType(v tables.Type) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateType() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateType()
	})
}

// SetStatus sets the "status" field.
func (u *TablesUpsertOne) SetStatus(v tables.Status) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateStatus() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateStatus()
	})
}

// SetBigBlind sets the "big_blind" field.
func (u *TablesUpsertOne) SetBigBlind(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetBigBlind(v)
	})
}

// AddBigBlind adds v to the "big_blind" field.
func (u *TablesUpsertOne) AddBigBlind(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.AddBigBlind(v)
	})
}

// UpdateBigBlind sets the "big_blind" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateBigBlind() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateBigBlind()
	})
}

// SetSmallBlind sets the "small_blind" field.
func (u *TablesUpsertOne) SetSmallBlind(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetSmallBlind(v)
	})
}

// AddSmallBlind adds v to the "small_blind" field.
func (u *TablesUpsertOne) AddSmallBlind(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.AddSmallBlind(v)
	})
}

// UpdateSmallBlind sets the "small_blind" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateSmallBlind() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateSmallBlind()
	})
}

// SetMinBuyIn sets the "min_buy_in" field.
func (u *TablesUpsertOne) SetMinBuyIn(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetMinBuyIn(v)
	})
}

// AddMinBuyIn adds v to the "min_buy_in" field.
func (u *TablesUpsertOne) AddMinBuyIn(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.AddMinBuyIn(v)
	})
}

// UpdateMinBuyIn sets the "min_buy_in" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateMinBuyIn() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateMinBuyIn()
	})
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (u *TablesUpsertOne) SetMaxBuyIn(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetMaxBuyIn(v)
	})
}

// AddMaxBuyIn adds v to the "max_buy_in" field.
func (u *TablesUpsertOne) AddMaxBuyIn(v uint64) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.AddMaxBuyIn(v)
	})
}

// UpdateMaxBuyIn sets the "max_buy_in" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateMaxBuyIn() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateMaxBuyIn()
	})
}

// SetIsVisible sets the "is_visible" field.
func (u *TablesUpsertOne) SetIsVisible(v bool) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetIsVisible(v)
	})
}

// UpdateIsVisible sets the "is_visible" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateIsVisible() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateIsVisible()
	})
}

// SetExpiredAt sets the "expired_at" field.
func (u *TablesUpsertOne) SetExpiredAt(v time.Time) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetExpiredAt(v)
	})
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateExpiredAt() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateExpiredAt()
	})
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (u *TablesUpsertOne) ClearExpiredAt() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.ClearExpiredAt()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TablesUpsertOne) SetUpdatedAt(v time.Time) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateUpdatedAt() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TablesUpsertOne) SetDeletedAt(v time.Time) *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TablesUpsertOne) UpdateDeletedAt() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TablesUpsertOne) ClearDeletedAt() *TablesUpsertOne {
	return u.Update(func(s *TablesUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *TablesUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TablesCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TablesUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TablesUpsertOne) ID(ctx context.Context) (id uint64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TablesUpsertOne) IDX(ctx context.Context) uint64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TablesCreateBulk is the builder for creating many Tables entities in bulk.
type TablesCreateBulk struct {
	config
	err      error
	builders []*TablesCreate
	conflict []sql.ConflictOption
}

// Save creates the Tables entities in the database.
func (tcb *TablesCreateBulk) Save(ctx context.Context) ([]*Tables, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Tables, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TablesMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TablesCreateBulk) SaveX(ctx context.Context) []*Tables {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TablesCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TablesCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tables.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TablesUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (tcb *TablesCreateBulk) OnConflict(opts ...sql.ConflictOption) *TablesUpsertBulk {
	tcb.conflict = opts
	return &TablesUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tables.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TablesCreateBulk) OnConflictColumns(columns ...string) *TablesUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TablesUpsertBulk{
		create: tcb,
	}
}

// TablesUpsertBulk is the builder for "upsert"-ing
// a bulk of Tables nodes.
type TablesUpsertBulk struct {
	create *TablesCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Tables.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tables.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TablesUpsertBulk) UpdateNewValues() *TablesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tables.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(tables.FieldCreatedAt)
			}
			if _, exists := b.mutation.CreatedBy(); exists {
				s.SetIgnore(tables.FieldCreatedBy)
			}
			if _, exists := b.mutation.UpdatedBy(); exists {
				s.SetIgnore(tables.FieldUpdatedBy)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tables.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TablesUpsertBulk) Ignore() *TablesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TablesUpsertBulk) DoNothing() *TablesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TablesCreateBulk.OnConflict
// documentation for more info.
func (u *TablesUpsertBulk) Update(set func(*TablesUpsert)) *TablesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TablesUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *TablesUpsertBulk) SetName(v string) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateName() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *TablesUpsertBulk) SetCode(v string) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateCode() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateCode()
	})
}

// SetType sets the "type" field.
func (u *TablesUpsertBulk) SetType(v tables.Type) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateType() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateType()
	})
}

// SetStatus sets the "status" field.
func (u *TablesUpsertBulk) SetStatus(v tables.Status) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateStatus() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateStatus()
	})
}

// SetBigBlind sets the "big_blind" field.
func (u *TablesUpsertBulk) SetBigBlind(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetBigBlind(v)
	})
}

// AddBigBlind adds v to the "big_blind" field.
func (u *TablesUpsertBulk) AddBigBlind(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.AddBigBlind(v)
	})
}

// UpdateBigBlind sets the "big_blind" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateBigBlind() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateBigBlind()
	})
}

// SetSmallBlind sets the "small_blind" field.
func (u *TablesUpsertBulk) SetSmallBlind(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetSmallBlind(v)
	})
}

// AddSmallBlind adds v to the "small_blind" field.
func (u *TablesUpsertBulk) AddSmallBlind(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.AddSmallBlind(v)
	})
}

// UpdateSmallBlind sets the "small_blind" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateSmallBlind() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateSmallBlind()
	})
}

// SetMinBuyIn sets the "min_buy_in" field.
func (u *TablesUpsertBulk) SetMinBuyIn(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetMinBuyIn(v)
	})
}

// AddMinBuyIn adds v to the "min_buy_in" field.
func (u *TablesUpsertBulk) AddMinBuyIn(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.AddMinBuyIn(v)
	})
}

// UpdateMinBuyIn sets the "min_buy_in" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateMinBuyIn() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateMinBuyIn()
	})
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (u *TablesUpsertBulk) SetMaxBuyIn(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetMaxBuyIn(v)
	})
}

// AddMaxBuyIn adds v to the "max_buy_in" field.
func (u *TablesUpsertBulk) AddMaxBuyIn(v uint64) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.AddMaxBuyIn(v)
	})
}

// UpdateMaxBuyIn sets the "max_buy_in" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateMaxBuyIn() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateMaxBuyIn()
	})
}

// SetIsVisible sets the "is_visible" field.
func (u *TablesUpsertBulk) SetIsVisible(v bool) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetIsVisible(v)
	})
}

// UpdateIsVisible sets the "is_visible" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateIsVisible() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateIsVisible()
	})
}

// SetExpiredAt sets the "expired_at" field.
func (u *TablesUpsertBulk) SetExpiredAt(v time.Time) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetExpiredAt(v)
	})
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateExpiredAt() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateExpiredAt()
	})
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (u *TablesUpsertBulk) ClearExpiredAt() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.ClearExpiredAt()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TablesUpsertBulk) SetUpdatedAt(v time.Time) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateUpdatedAt() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TablesUpsertBulk) SetDeletedAt(v time.Time) *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TablesUpsertBulk) UpdateDeletedAt() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TablesUpsertBulk) ClearDeletedAt() *TablesUpsertBulk {
	return u.Update(func(s *TablesUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *TablesUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TablesCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TablesCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TablesUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
