// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package games

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the games type in the database.
	Label = "games"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTableID holds the string denoting the table_id field in the database.
	FieldTableID = "table_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldSeats holds the string denoting the seats field in the database.
	FieldSeats = "seats"
	// FieldPot holds the string denoting the pot field in the database.
	FieldPot = "pot"
	// FieldButton holds the string denoting the button field in the database.
	FieldButton = "button"
	// FieldSb holds the string denoting the sb field in the database.
	FieldSb = "sb"
	// FieldBb holds the string denoting the bb field in the database.
	FieldBb = "bb"
	// FieldStakes holds the string denoting the stakes field in the database.
	FieldStakes = "stakes"
	// FieldPlayers holds the string denoting the players field in the database.
	FieldPlayers = "players"
	// FieldWinners holds the string denoting the winners field in the database.
	FieldWinners = "winners"
	// FieldBoard holds the string denoting the board field in the database.
	FieldBoard = "board"
	// FieldDuration holds the string denoting the duration field in the database.
	FieldDuration = "duration"
	// FieldStartedAt holds the string denoting the started_at field in the database.
	FieldStartedAt = "started_at"
	// FieldCompletedAt holds the string denoting the completed_at field in the database.
	FieldCompletedAt = "completed_at"
	// FieldSettled holds the string denoting the settled field in the database.
	FieldSettled = "settled"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the games in the database.
	Table = "games"
)

// Columns holds all SQL columns for games fields.
var Columns = []string{
	FieldID,
	FieldTableID,
	FieldStatus,
	FieldSeats,
	FieldPot,
	FieldButton,
	FieldSb,
	FieldBb,
	FieldStakes,
	FieldPlayers,
	FieldWinners,
	FieldBoard,
	FieldDuration,
	FieldStartedAt,
	FieldCompletedAt,
	FieldSettled,
	FieldCreatedAt,
	FieldCreatedBy,
	FieldUpdatedBy,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultStartedAt holds the default value on creation for the "started_at" field.
	DefaultStartedAt func() time.Time
	// DefaultSettled holds the default value on creation for the "settled" field.
	DefaultSettled bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// Status defines the type for the "status" enum field.
type Status string

// StatusRunning is the default value of the Status enum.
const DefaultStatus = StatusRunning

// Status values.
const (
	StatusRunning   Status = "running"
	StatusCompleted Status = "completed"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusRunning, StatusCompleted:
		return nil
	default:
		return fmt.Errorf("games: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Games queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTableID orders the results by the table_id field.
func ByTableID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTableID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByPot orders the results by the pot field.
func ByPot(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPot, opts...).ToFunc()
}

// ByButton orders the results by the button field.
func ByButton(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldButton, opts...).ToFunc()
}

// BySb orders the results by the sb field.
func BySb(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSb, opts...).ToFunc()
}

// ByBb orders the results by the bb field.
func ByBb(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBb, opts...).ToFunc()
}

// ByDuration orders the results by the duration field.
func ByDuration(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDuration, opts...).ToFunc()
}

// ByStartedAt orders the results by the started_at field.
func ByStartedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartedAt, opts...).ToFunc()
}

// ByCompletedAt orders the results by the completed_at field.
func ByCompletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCompletedAt, opts...).ToFunc()
}

// BySettled orders the results by the settled field.
func BySettled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSettled, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}
