// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package games

import (
	"hamster/internal/db/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint64) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint64) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint64) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint64) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint64) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint64) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint64) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldID, id))
}

// TableID applies equality check predicate on the "table_id" field. It's identical to TableIDEQ.
func TableID(v uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldTableID, v))
}

// Pot applies equality check predicate on the "pot" field. It's identical to PotEQ.
func Pot(v uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldPot, v))
}

// Button applies equality check predicate on the "button" field. It's identical to ButtonEQ.
func Button(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldButton, v))
}

// Sb applies equality check predicate on the "sb" field. It's identical to SbEQ.
func Sb(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldSb, v))
}

// Bb applies equality check predicate on the "bb" field. It's identical to BbEQ.
func Bb(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldBb, v))
}

// Duration applies equality check predicate on the "duration" field. It's identical to DurationEQ.
func Duration(v int64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldDuration, v))
}

// StartedAt applies equality check predicate on the "started_at" field. It's identical to StartedAtEQ.
func StartedAt(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldStartedAt, v))
}

// CompletedAt applies equality check predicate on the "completed_at" field. It's identical to CompletedAtEQ.
func CompletedAt(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCompletedAt, v))
}

// Settled applies equality check predicate on the "settled" field. It's identical to SettledEQ.
func Settled(v bool) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldSettled, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldDeletedAt, v))
}

// TableIDEQ applies the EQ predicate on the "table_id" field.
func TableIDEQ(v uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldTableID, v))
}

// TableIDNEQ applies the NEQ predicate on the "table_id" field.
func TableIDNEQ(v uint64) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldTableID, v))
}

// TableIDIn applies the In predicate on the "table_id" field.
func TableIDIn(vs ...uint64) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldTableID, vs...))
}

// TableIDNotIn applies the NotIn predicate on the "table_id" field.
func TableIDNotIn(vs ...uint64) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldTableID, vs...))
}

// TableIDGT applies the GT predicate on the "table_id" field.
func TableIDGT(v uint64) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldTableID, v))
}

// TableIDGTE applies the GTE predicate on the "table_id" field.
func TableIDGTE(v uint64) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldTableID, v))
}

// TableIDLT applies the LT predicate on the "table_id" field.
func TableIDLT(v uint64) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldTableID, v))
}

// TableIDLTE applies the LTE predicate on the "table_id" field.
func TableIDLTE(v uint64) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldTableID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldStatus, vs...))
}

// PotEQ applies the EQ predicate on the "pot" field.
func PotEQ(v uint64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldPot, v))
}

// PotNEQ applies the NEQ predicate on the "pot" field.
func PotNEQ(v uint64) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldPot, v))
}

// PotIn applies the In predicate on the "pot" field.
func PotIn(vs ...uint64) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldPot, vs...))
}

// PotNotIn applies the NotIn predicate on the "pot" field.
func PotNotIn(vs ...uint64) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldPot, vs...))
}

// PotGT applies the GT predicate on the "pot" field.
func PotGT(v uint64) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldPot, v))
}

// PotGTE applies the GTE predicate on the "pot" field.
func PotGTE(v uint64) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldPot, v))
}

// PotLT applies the LT predicate on the "pot" field.
func PotLT(v uint64) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldPot, v))
}

// PotLTE applies the LTE predicate on the "pot" field.
func PotLTE(v uint64) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldPot, v))
}

// ButtonEQ applies the EQ predicate on the "button" field.
func ButtonEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldButton, v))
}

// ButtonNEQ applies the NEQ predicate on the "button" field.
func ButtonNEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldButton, v))
}

// ButtonIn applies the In predicate on the "button" field.
func ButtonIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldButton, vs...))
}

// ButtonNotIn applies the NotIn predicate on the "button" field.
func ButtonNotIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldButton, vs...))
}

// ButtonGT applies the GT predicate on the "button" field.
func ButtonGT(v int) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldButton, v))
}

// ButtonGTE applies the GTE predicate on the "button" field.
func ButtonGTE(v int) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldButton, v))
}

// ButtonLT applies the LT predicate on the "button" field.
func ButtonLT(v int) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldButton, v))
}

// ButtonLTE applies the LTE predicate on the "button" field.
func ButtonLTE(v int) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldButton, v))
}

// SbEQ applies the EQ predicate on the "sb" field.
func SbEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldSb, v))
}

// SbNEQ applies the NEQ predicate on the "sb" field.
func SbNEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldSb, v))
}

// SbIn applies the In predicate on the "sb" field.
func SbIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldSb, vs...))
}

// SbNotIn applies the NotIn predicate on the "sb" field.
func SbNotIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldSb, vs...))
}

// SbGT applies the GT predicate on the "sb" field.
func SbGT(v int) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldSb, v))
}

// SbGTE applies the GTE predicate on the "sb" field.
func SbGTE(v int) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldSb, v))
}

// SbLT applies the LT predicate on the "sb" field.
func SbLT(v int) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldSb, v))
}

// SbLTE applies the LTE predicate on the "sb" field.
func SbLTE(v int) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldSb, v))
}

// BbEQ applies the EQ predicate on the "bb" field.
func BbEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldBb, v))
}

// BbNEQ applies the NEQ predicate on the "bb" field.
func BbNEQ(v int) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldBb, v))
}

// BbIn applies the In predicate on the "bb" field.
func BbIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldBb, vs...))
}

// BbNotIn applies the NotIn predicate on the "bb" field.
func BbNotIn(vs ...int) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldBb, vs...))
}

// BbGT applies the GT predicate on the "bb" field.
func BbGT(v int) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldBb, v))
}

// BbGTE applies the GTE predicate on the "bb" field.
func BbGTE(v int) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldBb, v))
}

// BbLT applies the LT predicate on the "bb" field.
func BbLT(v int) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldBb, v))
}

// BbLTE applies the LTE predicate on the "bb" field.
func BbLTE(v int) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldBb, v))
}

// DurationEQ applies the EQ predicate on the "duration" field.
func DurationEQ(v int64) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldDuration, v))
}

// DurationNEQ applies the NEQ predicate on the "duration" field.
func DurationNEQ(v int64) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldDuration, v))
}

// DurationIn applies the In predicate on the "duration" field.
func DurationIn(vs ...int64) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldDuration, vs...))
}

// DurationNotIn applies the NotIn predicate on the "duration" field.
func DurationNotIn(vs ...int64) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldDuration, vs...))
}

// DurationGT applies the GT predicate on the "duration" field.
func DurationGT(v int64) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldDuration, v))
}

// DurationGTE applies the GTE predicate on the "duration" field.
func DurationGTE(v int64) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldDuration, v))
}

// DurationLT applies the LT predicate on the "duration" field.
func DurationLT(v int64) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldDuration, v))
}

// DurationLTE applies the LTE predicate on the "duration" field.
func DurationLTE(v int64) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldDuration, v))
}

// StartedAtEQ applies the EQ predicate on the "started_at" field.
func StartedAtEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldStartedAt, v))
}

// StartedAtNEQ applies the NEQ predicate on the "started_at" field.
func StartedAtNEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldStartedAt, v))
}

// StartedAtIn applies the In predicate on the "started_at" field.
func StartedAtIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldStartedAt, vs...))
}

// StartedAtNotIn applies the NotIn predicate on the "started_at" field.
func StartedAtNotIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldStartedAt, vs...))
}

// StartedAtGT applies the GT predicate on the "started_at" field.
func StartedAtGT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldStartedAt, v))
}

// StartedAtGTE applies the GTE predicate on the "started_at" field.
func StartedAtGTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldStartedAt, v))
}

// StartedAtLT applies the LT predicate on the "started_at" field.
func StartedAtLT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldStartedAt, v))
}

// StartedAtLTE applies the LTE predicate on the "started_at" field.
func StartedAtLTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldStartedAt, v))
}

// CompletedAtEQ applies the EQ predicate on the "completed_at" field.
func CompletedAtEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCompletedAt, v))
}

// CompletedAtNEQ applies the NEQ predicate on the "completed_at" field.
func CompletedAtNEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldCompletedAt, v))
}

// CompletedAtIn applies the In predicate on the "completed_at" field.
func CompletedAtIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldCompletedAt, vs...))
}

// CompletedAtNotIn applies the NotIn predicate on the "completed_at" field.
func CompletedAtNotIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldCompletedAt, vs...))
}

// CompletedAtGT applies the GT predicate on the "completed_at" field.
func CompletedAtGT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldCompletedAt, v))
}

// CompletedAtGTE applies the GTE predicate on the "completed_at" field.
func CompletedAtGTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldCompletedAt, v))
}

// CompletedAtLT applies the LT predicate on the "completed_at" field.
func CompletedAtLT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldCompletedAt, v))
}

// CompletedAtLTE applies the LTE predicate on the "completed_at" field.
func CompletedAtLTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldCompletedAt, v))
}

// CompletedAtIsNil applies the IsNil predicate on the "completed_at" field.
func CompletedAtIsNil() predicate.Games {
	return predicate.Games(sql.FieldIsNull(FieldCompletedAt))
}

// CompletedAtNotNil applies the NotNil predicate on the "completed_at" field.
func CompletedAtNotNil() predicate.Games {
	return predicate.Games(sql.FieldNotNull(FieldCompletedAt))
}

// SettledEQ applies the EQ predicate on the "settled" field.
func SettledEQ(v bool) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldSettled, v))
}

// SettledNEQ applies the NEQ predicate on the "settled" field.
func SettledNEQ(v bool) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldSettled, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldCreatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.Games {
	return predicate.Games(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.Games {
	return predicate.Games(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.Games {
	return predicate.Games(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.Games {
	return predicate.Games(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.Games {
	return predicate.Games(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.Games {
	return predicate.Games(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.Games {
	return predicate.Games(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.Games {
	return predicate.Games(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.Games {
	return predicate.Games(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.Games {
	return predicate.Games(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.Games {
	return predicate.Games(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.Games {
	return predicate.Games(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.Games {
	return predicate.Games(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.Games {
	return predicate.Games(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Games {
	return predicate.Games(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Games {
	return predicate.Games(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Games {
	return predicate.Games(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Games {
	return predicate.Games(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Games) predicate.Games {
	return predicate.Games(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Games) predicate.Games {
	return predicate.Games(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Games) predicate.Games {
	return predicate.Games(sql.NotPredicates(p))
}
