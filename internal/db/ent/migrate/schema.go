// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// GamesColumns holds the columns for the "games" table.
	GamesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true, SchemaType: map[string]string{"postgres": "serial"}},
		{Name: "table_id", Type: field.TypeUint64},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"running", "completed"}, Default: "running"},
		{Name: "seats", Type: field.TypeJSON},
		{Name: "pot", Type: field.TypeUint64},
		{Name: "button", Type: field.TypeInt},
		{Name: "sb", Type: field.TypeInt},
		{Name: "bb", Type: field.TypeInt},
		{Name: "stakes", Type: field.TypeJSON},
		{Name: "players", Type: field.TypeJSON},
		{Name: "winners", Type: field.TypeJSON},
		{Name: "board", Type: field.TypeJSON},
		{Name: "duration", Type: field.TypeInt64},
		{Name: "started_at", Type: field.TypeTime},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "settled", Type: field.TypeBool, Default: false},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// GamesTable holds the schema information for the "games" table.
	GamesTable = &schema.Table{
		Name:       "games",
		Columns:    GamesColumns,
		PrimaryKey: []*schema.Column{GamesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "games_table_id",
				Unique:  false,
				Columns: []*schema.Column{GamesColumns[1]},
			},
		},
	}
	// TablesColumns holds the columns for the "tables" table.
	TablesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true, SchemaType: map[string]string{"postgres": "serial"}},
		{Name: "name", Type: field.TypeString},
		{Name: "code", Type: field.TypeString},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"6p", "9p"}, Default: "6p"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"active", "inactive"}, Default: "active"},
		{Name: "big_blind", Type: field.TypeUint64},
		{Name: "small_blind", Type: field.TypeUint64},
		{Name: "min_buy_in", Type: field.TypeUint64},
		{Name: "max_buy_in", Type: field.TypeUint64},
		{Name: "is_visible", Type: field.TypeBool, Default: true},
		{Name: "expired_at", Type: field.TypeTime, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "created_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_by", Type: field.TypeString, Nullable: true},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// TablesTable holds the schema information for the "tables" table.
	TablesTable = &schema.Table{
		Name:       "tables",
		Columns:    TablesColumns,
		PrimaryKey: []*schema.Column{TablesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "tables_code",
				Unique:  true,
				Columns: []*schema.Column{TablesColumns[2]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		GamesTable,
		TablesTable,
	}
)

func init() {
	GamesTable.Annotation = &entsql.Annotation{
		Table: "games",
	}
	TablesTable.Annotation = &entsql.Annotation{
		Table: "tables",
	}
}
