// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/predicate"
	"hamster/internal/db/ent/tables"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeGames  = "Games"
	TypeTables = "Tables"
)

// GamesMutation represents an operation that mutates the Games nodes in the graph.
type GamesMutation struct {
	config
	op            Op
	typ           string
	id            *uint64
	table_id      *uint64
	addtable_id   *int64
	status        *games.Status
	seats         *[]string
	appendseats   []string
	pot           *uint64
	addpot        *int64
	button        *int
	addbutton     *int
	sb            *int
	addsb         *int
	bb            *int
	addbb         *int
	stakes        *[]uint64
	appendstakes  []uint64
	players       *[]model.PlayerState
	appendplayers []model.PlayerState
	winners       *[]model.PlayerState
	appendwinners []model.PlayerState
	board         *poker.Cards
	appendboard   poker.Cards
	duration      *int64
	addduration   *int64
	started_at    *time.Time
	completed_at  *time.Time
	settled       *bool
	created_at    *time.Time
	created_by    *string
	updated_by    *string
	updated_at    *time.Time
	deleted_at    *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Games, error)
	predicates    []predicate.Games
}

var _ ent.Mutation = (*GamesMutation)(nil)

// gamesOption allows management of the mutation configuration using functional options.
type gamesOption func(*GamesMutation)

// newGamesMutation creates new mutation for the Games entity.
func newGamesMutation(c config, op Op, opts ...gamesOption) *GamesMutation {
	m := &GamesMutation{
		config:        c,
		op:            op,
		typ:           TypeGames,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withGamesID sets the ID field of the mutation.
func withGamesID(id uint64) gamesOption {
	return func(m *GamesMutation) {
		var (
			err   error
			once  sync.Once
			value *Games
		)
		m.oldValue = func(ctx context.Context) (*Games, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Games.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withGames sets the old Games of the mutation.
func withGames(node *Games) gamesOption {
	return func(m *GamesMutation) {
		m.oldValue = func(context.Context) (*Games, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m GamesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m GamesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Games entities.
func (m *GamesMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *GamesMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *GamesMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Games.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetTableID sets the "table_id" field.
func (m *GamesMutation) SetTableID(u uint64) {
	m.table_id = &u
	m.addtable_id = nil
}

// TableID returns the value of the "table_id" field in the mutation.
func (m *GamesMutation) TableID() (r uint64, exists bool) {
	v := m.table_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTableID returns the old "table_id" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldTableID(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTableID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTableID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTableID: %w", err)
	}
	return oldValue.TableID, nil
}

// AddTableID adds u to the "table_id" field.
func (m *GamesMutation) AddTableID(u int64) {
	if m.addtable_id != nil {
		*m.addtable_id += u
	} else {
		m.addtable_id = &u
	}
}

// AddedTableID returns the value that was added to the "table_id" field in this mutation.
func (m *GamesMutation) AddedTableID() (r int64, exists bool) {
	v := m.addtable_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetTableID resets all changes to the "table_id" field.
func (m *GamesMutation) ResetTableID() {
	m.table_id = nil
	m.addtable_id = nil
}

// SetStatus sets the "status" field.
func (m *GamesMutation) SetStatus(ga games.Status) {
	m.status = &ga
}

// Status returns the value of the "status" field in the mutation.
func (m *GamesMutation) Status() (r games.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldStatus(ctx context.Context) (v games.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *GamesMutation) ResetStatus() {
	m.status = nil
}

// SetSeats sets the "seats" field.
func (m *GamesMutation) SetSeats(s []string) {
	m.seats = &s
	m.appendseats = nil
}

// Seats returns the value of the "seats" field in the mutation.
func (m *GamesMutation) Seats() (r []string, exists bool) {
	v := m.seats
	if v == nil {
		return
	}
	return *v, true
}

// OldSeats returns the old "seats" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldSeats(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSeats is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSeats requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSeats: %w", err)
	}
	return oldValue.Seats, nil
}

// AppendSeats adds s to the "seats" field.
func (m *GamesMutation) AppendSeats(s []string) {
	m.appendseats = append(m.appendseats, s...)
}

// AppendedSeats returns the list of values that were appended to the "seats" field in this mutation.
func (m *GamesMutation) AppendedSeats() ([]string, bool) {
	if len(m.appendseats) == 0 {
		return nil, false
	}
	return m.appendseats, true
}

// ResetSeats resets all changes to the "seats" field.
func (m *GamesMutation) ResetSeats() {
	m.seats = nil
	m.appendseats = nil
}

// SetPot sets the "pot" field.
func (m *GamesMutation) SetPot(u uint64) {
	m.pot = &u
	m.addpot = nil
}

// Pot returns the value of the "pot" field in the mutation.
func (m *GamesMutation) Pot() (r uint64, exists bool) {
	v := m.pot
	if v == nil {
		return
	}
	return *v, true
}

// OldPot returns the old "pot" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldPot(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPot is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPot requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPot: %w", err)
	}
	return oldValue.Pot, nil
}

// AddPot adds u to the "pot" field.
func (m *GamesMutation) AddPot(u int64) {
	if m.addpot != nil {
		*m.addpot += u
	} else {
		m.addpot = &u
	}
}

// AddedPot returns the value that was added to the "pot" field in this mutation.
func (m *GamesMutation) AddedPot() (r int64, exists bool) {
	v := m.addpot
	if v == nil {
		return
	}
	return *v, true
}

// ResetPot resets all changes to the "pot" field.
func (m *GamesMutation) ResetPot() {
	m.pot = nil
	m.addpot = nil
}

// SetButton sets the "button" field.
func (m *GamesMutation) SetButton(i int) {
	m.button = &i
	m.addbutton = nil
}

// Button returns the value of the "button" field in the mutation.
func (m *GamesMutation) Button() (r int, exists bool) {
	v := m.button
	if v == nil {
		return
	}
	return *v, true
}

// OldButton returns the old "button" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldButton(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldButton is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldButton requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldButton: %w", err)
	}
	return oldValue.Button, nil
}

// AddButton adds i to the "button" field.
func (m *GamesMutation) AddButton(i int) {
	if m.addbutton != nil {
		*m.addbutton += i
	} else {
		m.addbutton = &i
	}
}

// AddedButton returns the value that was added to the "button" field in this mutation.
func (m *GamesMutation) AddedButton() (r int, exists bool) {
	v := m.addbutton
	if v == nil {
		return
	}
	return *v, true
}

// ResetButton resets all changes to the "button" field.
func (m *GamesMutation) ResetButton() {
	m.button = nil
	m.addbutton = nil
}

// SetSb sets the "sb" field.
func (m *GamesMutation) SetSb(i int) {
	m.sb = &i
	m.addsb = nil
}

// Sb returns the value of the "sb" field in the mutation.
func (m *GamesMutation) Sb() (r int, exists bool) {
	v := m.sb
	if v == nil {
		return
	}
	return *v, true
}

// OldSb returns the old "sb" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldSb(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSb is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSb requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSb: %w", err)
	}
	return oldValue.Sb, nil
}

// AddSb adds i to the "sb" field.
func (m *GamesMutation) AddSb(i int) {
	if m.addsb != nil {
		*m.addsb += i
	} else {
		m.addsb = &i
	}
}

// AddedSb returns the value that was added to the "sb" field in this mutation.
func (m *GamesMutation) AddedSb() (r int, exists bool) {
	v := m.addsb
	if v == nil {
		return
	}
	return *v, true
}

// ResetSb resets all changes to the "sb" field.
func (m *GamesMutation) ResetSb() {
	m.sb = nil
	m.addsb = nil
}

// SetBb sets the "bb" field.
func (m *GamesMutation) SetBb(i int) {
	m.bb = &i
	m.addbb = nil
}

// Bb returns the value of the "bb" field in the mutation.
func (m *GamesMutation) Bb() (r int, exists bool) {
	v := m.bb
	if v == nil {
		return
	}
	return *v, true
}

// OldBb returns the old "bb" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldBb(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBb is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBb requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBb: %w", err)
	}
	return oldValue.Bb, nil
}

// AddBb adds i to the "bb" field.
func (m *GamesMutation) AddBb(i int) {
	if m.addbb != nil {
		*m.addbb += i
	} else {
		m.addbb = &i
	}
}

// AddedBb returns the value that was added to the "bb" field in this mutation.
func (m *GamesMutation) AddedBb() (r int, exists bool) {
	v := m.addbb
	if v == nil {
		return
	}
	return *v, true
}

// ResetBb resets all changes to the "bb" field.
func (m *GamesMutation) ResetBb() {
	m.bb = nil
	m.addbb = nil
}

// SetStakes sets the "stakes" field.
func (m *GamesMutation) SetStakes(u []uint64) {
	m.stakes = &u
	m.appendstakes = nil
}

// Stakes returns the value of the "stakes" field in the mutation.
func (m *GamesMutation) Stakes() (r []uint64, exists bool) {
	v := m.stakes
	if v == nil {
		return
	}
	return *v, true
}

// OldStakes returns the old "stakes" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldStakes(ctx context.Context) (v []uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStakes is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStakes requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStakes: %w", err)
	}
	return oldValue.Stakes, nil
}

// AppendStakes adds u to the "stakes" field.
func (m *GamesMutation) AppendStakes(u []uint64) {
	m.appendstakes = append(m.appendstakes, u...)
}

// AppendedStakes returns the list of values that were appended to the "stakes" field in this mutation.
func (m *GamesMutation) AppendedStakes() ([]uint64, bool) {
	if len(m.appendstakes) == 0 {
		return nil, false
	}
	return m.appendstakes, true
}

// ResetStakes resets all changes to the "stakes" field.
func (m *GamesMutation) ResetStakes() {
	m.stakes = nil
	m.appendstakes = nil
}

// SetPlayers sets the "players" field.
func (m *GamesMutation) SetPlayers(ms []model.PlayerState) {
	m.players = &ms
	m.appendplayers = nil
}

// Players returns the value of the "players" field in the mutation.
func (m *GamesMutation) Players() (r []model.PlayerState, exists bool) {
	v := m.players
	if v == nil {
		return
	}
	return *v, true
}

// OldPlayers returns the old "players" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldPlayers(ctx context.Context) (v []model.PlayerState, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlayers is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlayers requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlayers: %w", err)
	}
	return oldValue.Players, nil
}

// AppendPlayers adds ms to the "players" field.
func (m *GamesMutation) AppendPlayers(ms []model.PlayerState) {
	m.appendplayers = append(m.appendplayers, ms...)
}

// AppendedPlayers returns the list of values that were appended to the "players" field in this mutation.
func (m *GamesMutation) AppendedPlayers() ([]model.PlayerState, bool) {
	if len(m.appendplayers) == 0 {
		return nil, false
	}
	return m.appendplayers, true
}

// ResetPlayers resets all changes to the "players" field.
func (m *GamesMutation) ResetPlayers() {
	m.players = nil
	m.appendplayers = nil
}

// SetWinners sets the "winners" field.
func (m *GamesMutation) SetWinners(ms []model.PlayerState) {
	m.winners = &ms
	m.appendwinners = nil
}

// Winners returns the value of the "winners" field in the mutation.
func (m *GamesMutation) Winners() (r []model.PlayerState, exists bool) {
	v := m.winners
	if v == nil {
		return
	}
	return *v, true
}

// OldWinners returns the old "winners" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldWinners(ctx context.Context) (v []model.PlayerState, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWinners is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWinners requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWinners: %w", err)
	}
	return oldValue.Winners, nil
}

// AppendWinners adds ms to the "winners" field.
func (m *GamesMutation) AppendWinners(ms []model.PlayerState) {
	m.appendwinners = append(m.appendwinners, ms...)
}

// AppendedWinners returns the list of values that were appended to the "winners" field in this mutation.
func (m *GamesMutation) AppendedWinners() ([]model.PlayerState, bool) {
	if len(m.appendwinners) == 0 {
		return nil, false
	}
	return m.appendwinners, true
}

// ResetWinners resets all changes to the "winners" field.
func (m *GamesMutation) ResetWinners() {
	m.winners = nil
	m.appendwinners = nil
}

// SetBoard sets the "board" field.
func (m *GamesMutation) SetBoard(po poker.Cards) {
	m.board = &po
	m.appendboard = nil
}

// Board returns the value of the "board" field in the mutation.
func (m *GamesMutation) Board() (r poker.Cards, exists bool) {
	v := m.board
	if v == nil {
		return
	}
	return *v, true
}

// OldBoard returns the old "board" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldBoard(ctx context.Context) (v poker.Cards, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBoard is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBoard requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBoard: %w", err)
	}
	return oldValue.Board, nil
}

// AppendBoard adds po to the "board" field.
func (m *GamesMutation) AppendBoard(po poker.Cards) {
	m.appendboard = append(m.appendboard, po...)
}

// AppendedBoard returns the list of values that were appended to the "board" field in this mutation.
func (m *GamesMutation) AppendedBoard() (poker.Cards, bool) {
	if len(m.appendboard) == 0 {
		return nil, false
	}
	return m.appendboard, true
}

// ResetBoard resets all changes to the "board" field.
func (m *GamesMutation) ResetBoard() {
	m.board = nil
	m.appendboard = nil
}

// SetDuration sets the "duration" field.
func (m *GamesMutation) SetDuration(i int64) {
	m.duration = &i
	m.addduration = nil
}

// Duration returns the value of the "duration" field in the mutation.
func (m *GamesMutation) Duration() (r int64, exists bool) {
	v := m.duration
	if v == nil {
		return
	}
	return *v, true
}

// OldDuration returns the old "duration" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldDuration(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDuration is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDuration requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDuration: %w", err)
	}
	return oldValue.Duration, nil
}

// AddDuration adds i to the "duration" field.
func (m *GamesMutation) AddDuration(i int64) {
	if m.addduration != nil {
		*m.addduration += i
	} else {
		m.addduration = &i
	}
}

// AddedDuration returns the value that was added to the "duration" field in this mutation.
func (m *GamesMutation) AddedDuration() (r int64, exists bool) {
	v := m.addduration
	if v == nil {
		return
	}
	return *v, true
}

// ResetDuration resets all changes to the "duration" field.
func (m *GamesMutation) ResetDuration() {
	m.duration = nil
	m.addduration = nil
}

// SetStartedAt sets the "started_at" field.
func (m *GamesMutation) SetStartedAt(t time.Time) {
	m.started_at = &t
}

// StartedAt returns the value of the "started_at" field in the mutation.
func (m *GamesMutation) StartedAt() (r time.Time, exists bool) {
	v := m.started_at
	if v == nil {
		return
	}
	return *v, true
}

// OldStartedAt returns the old "started_at" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldStartedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartedAt: %w", err)
	}
	return oldValue.StartedAt, nil
}

// ResetStartedAt resets all changes to the "started_at" field.
func (m *GamesMutation) ResetStartedAt() {
	m.started_at = nil
}

// SetCompletedAt sets the "completed_at" field.
func (m *GamesMutation) SetCompletedAt(t time.Time) {
	m.completed_at = &t
}

// CompletedAt returns the value of the "completed_at" field in the mutation.
func (m *GamesMutation) CompletedAt() (r time.Time, exists bool) {
	v := m.completed_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCompletedAt returns the old "completed_at" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldCompletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCompletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCompletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCompletedAt: %w", err)
	}
	return oldValue.CompletedAt, nil
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (m *GamesMutation) ClearCompletedAt() {
	m.completed_at = nil
	m.clearedFields[games.FieldCompletedAt] = struct{}{}
}

// CompletedAtCleared returns if the "completed_at" field was cleared in this mutation.
func (m *GamesMutation) CompletedAtCleared() bool {
	_, ok := m.clearedFields[games.FieldCompletedAt]
	return ok
}

// ResetCompletedAt resets all changes to the "completed_at" field.
func (m *GamesMutation) ResetCompletedAt() {
	m.completed_at = nil
	delete(m.clearedFields, games.FieldCompletedAt)
}

// SetSettled sets the "settled" field.
func (m *GamesMutation) SetSettled(b bool) {
	m.settled = &b
}

// Settled returns the value of the "settled" field in the mutation.
func (m *GamesMutation) Settled() (r bool, exists bool) {
	v := m.settled
	if v == nil {
		return
	}
	return *v, true
}

// OldSettled returns the old "settled" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldSettled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSettled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSettled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSettled: %w", err)
	}
	return oldValue.Settled, nil
}

// ResetSettled resets all changes to the "settled" field.
func (m *GamesMutation) ResetSettled() {
	m.settled = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *GamesMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *GamesMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *GamesMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetCreatedBy sets the "created_by" field.
func (m *GamesMutation) SetCreatedBy(s string) {
	m.created_by = &s
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *GamesMutation) CreatedBy() (r string, exists bool) {
	v := m.created_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldCreatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ClearCreatedBy clears the value of the "created_by" field.
func (m *GamesMutation) ClearCreatedBy() {
	m.created_by = nil
	m.clearedFields[games.FieldCreatedBy] = struct{}{}
}

// CreatedByCleared returns if the "created_by" field was cleared in this mutation.
func (m *GamesMutation) CreatedByCleared() bool {
	_, ok := m.clearedFields[games.FieldCreatedBy]
	return ok
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *GamesMutation) ResetCreatedBy() {
	m.created_by = nil
	delete(m.clearedFields, games.FieldCreatedBy)
}

// SetUpdatedBy sets the "updated_by" field.
func (m *GamesMutation) SetUpdatedBy(s string) {
	m.updated_by = &s
}

// UpdatedBy returns the value of the "updated_by" field in the mutation.
func (m *GamesMutation) UpdatedBy() (r string, exists bool) {
	v := m.updated_by
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedBy returns the old "updated_by" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldUpdatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedBy: %w", err)
	}
	return oldValue.UpdatedBy, nil
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (m *GamesMutation) ClearUpdatedBy() {
	m.updated_by = nil
	m.clearedFields[games.FieldUpdatedBy] = struct{}{}
}

// UpdatedByCleared returns if the "updated_by" field was cleared in this mutation.
func (m *GamesMutation) UpdatedByCleared() bool {
	_, ok := m.clearedFields[games.FieldUpdatedBy]
	return ok
}

// ResetUpdatedBy resets all changes to the "updated_by" field.
func (m *GamesMutation) ResetUpdatedBy() {
	m.updated_by = nil
	delete(m.clearedFields, games.FieldUpdatedBy)
}

// SetUpdatedAt sets the "updated_at" field.
func (m *GamesMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *GamesMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *GamesMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *GamesMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *GamesMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Games entity.
// If the Games object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GamesMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *GamesMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[games.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *GamesMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[games.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *GamesMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, games.FieldDeletedAt)
}

// Where appends a list predicates to the GamesMutation builder.
func (m *GamesMutation) Where(ps ...predicate.Games) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the GamesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *GamesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Games, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *GamesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *GamesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Games).
func (m *GamesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *GamesMutation) Fields() []string {
	fields := make([]string, 0, 20)
	if m.table_id != nil {
		fields = append(fields, games.FieldTableID)
	}
	if m.status != nil {
		fields = append(fields, games.FieldStatus)
	}
	if m.seats != nil {
		fields = append(fields, games.FieldSeats)
	}
	if m.pot != nil {
		fields = append(fields, games.FieldPot)
	}
	if m.button != nil {
		fields = append(fields, games.FieldButton)
	}
	if m.sb != nil {
		fields = append(fields, games.FieldSb)
	}
	if m.bb != nil {
		fields = append(fields, games.FieldBb)
	}
	if m.stakes != nil {
		fields = append(fields, games.FieldStakes)
	}
	if m.players != nil {
		fields = append(fields, games.FieldPlayers)
	}
	if m.winners != nil {
		fields = append(fields, games.FieldWinners)
	}
	if m.board != nil {
		fields = append(fields, games.FieldBoard)
	}
	if m.duration != nil {
		fields = append(fields, games.FieldDuration)
	}
	if m.started_at != nil {
		fields = append(fields, games.FieldStartedAt)
	}
	if m.completed_at != nil {
		fields = append(fields, games.FieldCompletedAt)
	}
	if m.settled != nil {
		fields = append(fields, games.FieldSettled)
	}
	if m.created_at != nil {
		fields = append(fields, games.FieldCreatedAt)
	}
	if m.created_by != nil {
		fields = append(fields, games.FieldCreatedBy)
	}
	if m.updated_by != nil {
		fields = append(fields, games.FieldUpdatedBy)
	}
	if m.updated_at != nil {
		fields = append(fields, games.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, games.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *GamesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case games.FieldTableID:
		return m.TableID()
	case games.FieldStatus:
		return m.Status()
	case games.FieldSeats:
		return m.Seats()
	case games.FieldPot:
		return m.Pot()
	case games.FieldButton:
		return m.Button()
	case games.FieldSb:
		return m.Sb()
	case games.FieldBb:
		return m.Bb()
	case games.FieldStakes:
		return m.Stakes()
	case games.FieldPlayers:
		return m.Players()
	case games.FieldWinners:
		return m.Winners()
	case games.FieldBoard:
		return m.Board()
	case games.FieldDuration:
		return m.Duration()
	case games.FieldStartedAt:
		return m.StartedAt()
	case games.FieldCompletedAt:
		return m.CompletedAt()
	case games.FieldSettled:
		return m.Settled()
	case games.FieldCreatedAt:
		return m.CreatedAt()
	case games.FieldCreatedBy:
		return m.CreatedBy()
	case games.FieldUpdatedBy:
		return m.UpdatedBy()
	case games.FieldUpdatedAt:
		return m.UpdatedAt()
	case games.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *GamesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case games.FieldTableID:
		return m.OldTableID(ctx)
	case games.FieldStatus:
		return m.OldStatus(ctx)
	case games.FieldSeats:
		return m.OldSeats(ctx)
	case games.FieldPot:
		return m.OldPot(ctx)
	case games.FieldButton:
		return m.OldButton(ctx)
	case games.FieldSb:
		return m.OldSb(ctx)
	case games.FieldBb:
		return m.OldBb(ctx)
	case games.FieldStakes:
		return m.OldStakes(ctx)
	case games.FieldPlayers:
		return m.OldPlayers(ctx)
	case games.FieldWinners:
		return m.OldWinners(ctx)
	case games.FieldBoard:
		return m.OldBoard(ctx)
	case games.FieldDuration:
		return m.OldDuration(ctx)
	case games.FieldStartedAt:
		return m.OldStartedAt(ctx)
	case games.FieldCompletedAt:
		return m.OldCompletedAt(ctx)
	case games.FieldSettled:
		return m.OldSettled(ctx)
	case games.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case games.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case games.FieldUpdatedBy:
		return m.OldUpdatedBy(ctx)
	case games.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case games.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Games field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *GamesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case games.FieldTableID:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTableID(v)
		return nil
	case games.FieldStatus:
		v, ok := value.(games.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case games.FieldSeats:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSeats(v)
		return nil
	case games.FieldPot:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPot(v)
		return nil
	case games.FieldButton:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetButton(v)
		return nil
	case games.FieldSb:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSb(v)
		return nil
	case games.FieldBb:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBb(v)
		return nil
	case games.FieldStakes:
		v, ok := value.([]uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStakes(v)
		return nil
	case games.FieldPlayers:
		v, ok := value.([]model.PlayerState)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlayers(v)
		return nil
	case games.FieldWinners:
		v, ok := value.([]model.PlayerState)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWinners(v)
		return nil
	case games.FieldBoard:
		v, ok := value.(poker.Cards)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBoard(v)
		return nil
	case games.FieldDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDuration(v)
		return nil
	case games.FieldStartedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartedAt(v)
		return nil
	case games.FieldCompletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCompletedAt(v)
		return nil
	case games.FieldSettled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSettled(v)
		return nil
	case games.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case games.FieldCreatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case games.FieldUpdatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedBy(v)
		return nil
	case games.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case games.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Games field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *GamesMutation) AddedFields() []string {
	var fields []string
	if m.addtable_id != nil {
		fields = append(fields, games.FieldTableID)
	}
	if m.addpot != nil {
		fields = append(fields, games.FieldPot)
	}
	if m.addbutton != nil {
		fields = append(fields, games.FieldButton)
	}
	if m.addsb != nil {
		fields = append(fields, games.FieldSb)
	}
	if m.addbb != nil {
		fields = append(fields, games.FieldBb)
	}
	if m.addduration != nil {
		fields = append(fields, games.FieldDuration)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *GamesMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case games.FieldTableID:
		return m.AddedTableID()
	case games.FieldPot:
		return m.AddedPot()
	case games.FieldButton:
		return m.AddedButton()
	case games.FieldSb:
		return m.AddedSb()
	case games.FieldBb:
		return m.AddedBb()
	case games.FieldDuration:
		return m.AddedDuration()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *GamesMutation) AddField(name string, value ent.Value) error {
	switch name {
	case games.FieldTableID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTableID(v)
		return nil
	case games.FieldPot:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPot(v)
		return nil
	case games.FieldButton:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddButton(v)
		return nil
	case games.FieldSb:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddSb(v)
		return nil
	case games.FieldBb:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBb(v)
		return nil
	case games.FieldDuration:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDuration(v)
		return nil
	}
	return fmt.Errorf("unknown Games numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *GamesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(games.FieldCompletedAt) {
		fields = append(fields, games.FieldCompletedAt)
	}
	if m.FieldCleared(games.FieldCreatedBy) {
		fields = append(fields, games.FieldCreatedBy)
	}
	if m.FieldCleared(games.FieldUpdatedBy) {
		fields = append(fields, games.FieldUpdatedBy)
	}
	if m.FieldCleared(games.FieldDeletedAt) {
		fields = append(fields, games.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *GamesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *GamesMutation) ClearField(name string) error {
	switch name {
	case games.FieldCompletedAt:
		m.ClearCompletedAt()
		return nil
	case games.FieldCreatedBy:
		m.ClearCreatedBy()
		return nil
	case games.FieldUpdatedBy:
		m.ClearUpdatedBy()
		return nil
	case games.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Games nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *GamesMutation) ResetField(name string) error {
	switch name {
	case games.FieldTableID:
		m.ResetTableID()
		return nil
	case games.FieldStatus:
		m.ResetStatus()
		return nil
	case games.FieldSeats:
		m.ResetSeats()
		return nil
	case games.FieldPot:
		m.ResetPot()
		return nil
	case games.FieldButton:
		m.ResetButton()
		return nil
	case games.FieldSb:
		m.ResetSb()
		return nil
	case games.FieldBb:
		m.ResetBb()
		return nil
	case games.FieldStakes:
		m.ResetStakes()
		return nil
	case games.FieldPlayers:
		m.ResetPlayers()
		return nil
	case games.FieldWinners:
		m.ResetWinners()
		return nil
	case games.FieldBoard:
		m.ResetBoard()
		return nil
	case games.FieldDuration:
		m.ResetDuration()
		return nil
	case games.FieldStartedAt:
		m.ResetStartedAt()
		return nil
	case games.FieldCompletedAt:
		m.ResetCompletedAt()
		return nil
	case games.FieldSettled:
		m.ResetSettled()
		return nil
	case games.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case games.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case games.FieldUpdatedBy:
		m.ResetUpdatedBy()
		return nil
	case games.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case games.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Games field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *GamesMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *GamesMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *GamesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *GamesMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *GamesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *GamesMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *GamesMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Games unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *GamesMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Games edge %s", name)
}

// TablesMutation represents an operation that mutates the Tables nodes in the graph.
type TablesMutation struct {
	config
	op             Op
	typ            string
	id             *uint64
	name           *string
	code           *string
	_type          *tables.Type
	status         *tables.Status
	big_blind      *uint64
	addbig_blind   *int64
	small_blind    *uint64
	addsmall_blind *int64
	min_buy_in     *uint64
	addmin_buy_in  *int64
	max_buy_in     *uint64
	addmax_buy_in  *int64
	is_visible     *bool
	expired_at     *time.Time
	created_at     *time.Time
	created_by     *string
	updated_by     *string
	updated_at     *time.Time
	deleted_at     *time.Time
	clearedFields  map[string]struct{}
	done           bool
	oldValue       func(context.Context) (*Tables, error)
	predicates     []predicate.Tables
}

var _ ent.Mutation = (*TablesMutation)(nil)

// tablesOption allows management of the mutation configuration using functional options.
type tablesOption func(*TablesMutation)

// newTablesMutation creates new mutation for the Tables entity.
func newTablesMutation(c config, op Op, opts ...tablesOption) *TablesMutation {
	m := &TablesMutation{
		config:        c,
		op:            op,
		typ:           TypeTables,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTablesID sets the ID field of the mutation.
func withTablesID(id uint64) tablesOption {
	return func(m *TablesMutation) {
		var (
			err   error
			once  sync.Once
			value *Tables
		)
		m.oldValue = func(ctx context.Context) (*Tables, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Tables.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTables sets the old Tables of the mutation.
func withTables(node *Tables) tablesOption {
	return func(m *TablesMutation) {
		m.oldValue = func(context.Context) (*Tables, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TablesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TablesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Tables entities.
func (m *TablesMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TablesMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TablesMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Tables.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *TablesMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *TablesMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *TablesMutation) ResetName() {
	m.name = nil
}

// SetCode sets the "code" field.
func (m *TablesMutation) SetCode(s string) {
	m.code = &s
}

// Code returns the value of the "code" field in the mutation.
func (m *TablesMutation) Code() (r string, exists bool) {
	v := m.code
	if v == nil {
		return
	}
	return *v, true
}

// OldCode returns the old "code" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCode: %w", err)
	}
	return oldValue.Code, nil
}

// ResetCode resets all changes to the "code" field.
func (m *TablesMutation) ResetCode() {
	m.code = nil
}

// SetType sets the "type" field.
func (m *TablesMutation) SetType(t tables.Type) {
	m._type = &t
}

// GetType returns the value of the "type" field in the mutation.
func (m *TablesMutation) GetType() (r tables.Type, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldType(ctx context.Context) (v tables.Type, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *TablesMutation) ResetType() {
	m._type = nil
}

// SetStatus sets the "status" field.
func (m *TablesMutation) SetStatus(t tables.Status) {
	m.status = &t
}

// Status returns the value of the "status" field in the mutation.
func (m *TablesMutation) Status() (r tables.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldStatus(ctx context.Context) (v tables.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *TablesMutation) ResetStatus() {
	m.status = nil
}

// SetBigBlind sets the "big_blind" field.
func (m *TablesMutation) SetBigBlind(u uint64) {
	m.big_blind = &u
	m.addbig_blind = nil
}

// BigBlind returns the value of the "big_blind" field in the mutation.
func (m *TablesMutation) BigBlind() (r uint64, exists bool) {
	v := m.big_blind
	if v == nil {
		return
	}
	return *v, true
}

// OldBigBlind returns the old "big_blind" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldBigBlind(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBigBlind is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBigBlind requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBigBlind: %w", err)
	}
	return oldValue.BigBlind, nil
}

// AddBigBlind adds u to the "big_blind" field.
func (m *TablesMutation) AddBigBlind(u int64) {
	if m.addbig_blind != nil {
		*m.addbig_blind += u
	} else {
		m.addbig_blind = &u
	}
}

// AddedBigBlind returns the value that was added to the "big_blind" field in this mutation.
func (m *TablesMutation) AddedBigBlind() (r int64, exists bool) {
	v := m.addbig_blind
	if v == nil {
		return
	}
	return *v, true
}

// ResetBigBlind resets all changes to the "big_blind" field.
func (m *TablesMutation) ResetBigBlind() {
	m.big_blind = nil
	m.addbig_blind = nil
}

// SetSmallBlind sets the "small_blind" field.
func (m *TablesMutation) SetSmallBlind(u uint64) {
	m.small_blind = &u
	m.addsmall_blind = nil
}

// SmallBlind returns the value of the "small_blind" field in the mutation.
func (m *TablesMutation) SmallBlind() (r uint64, exists bool) {
	v := m.small_blind
	if v == nil {
		return
	}
	return *v, true
}

// OldSmallBlind returns the old "small_blind" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldSmallBlind(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSmallBlind is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSmallBlind requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSmallBlind: %w", err)
	}
	return oldValue.SmallBlind, nil
}

// AddSmallBlind adds u to the "small_blind" field.
func (m *TablesMutation) AddSmallBlind(u int64) {
	if m.addsmall_blind != nil {
		*m.addsmall_blind += u
	} else {
		m.addsmall_blind = &u
	}
}

// AddedSmallBlind returns the value that was added to the "small_blind" field in this mutation.
func (m *TablesMutation) AddedSmallBlind() (r int64, exists bool) {
	v := m.addsmall_blind
	if v == nil {
		return
	}
	return *v, true
}

// ResetSmallBlind resets all changes to the "small_blind" field.
func (m *TablesMutation) ResetSmallBlind() {
	m.small_blind = nil
	m.addsmall_blind = nil
}

// SetMinBuyIn sets the "min_buy_in" field.
func (m *TablesMutation) SetMinBuyIn(u uint64) {
	m.min_buy_in = &u
	m.addmin_buy_in = nil
}

// MinBuyIn returns the value of the "min_buy_in" field in the mutation.
func (m *TablesMutation) MinBuyIn() (r uint64, exists bool) {
	v := m.min_buy_in
	if v == nil {
		return
	}
	return *v, true
}

// OldMinBuyIn returns the old "min_buy_in" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldMinBuyIn(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMinBuyIn is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMinBuyIn requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMinBuyIn: %w", err)
	}
	return oldValue.MinBuyIn, nil
}

// AddMinBuyIn adds u to the "min_buy_in" field.
func (m *TablesMutation) AddMinBuyIn(u int64) {
	if m.addmin_buy_in != nil {
		*m.addmin_buy_in += u
	} else {
		m.addmin_buy_in = &u
	}
}

// AddedMinBuyIn returns the value that was added to the "min_buy_in" field in this mutation.
func (m *TablesMutation) AddedMinBuyIn() (r int64, exists bool) {
	v := m.addmin_buy_in
	if v == nil {
		return
	}
	return *v, true
}

// ResetMinBuyIn resets all changes to the "min_buy_in" field.
func (m *TablesMutation) ResetMinBuyIn() {
	m.min_buy_in = nil
	m.addmin_buy_in = nil
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (m *TablesMutation) SetMaxBuyIn(u uint64) {
	m.max_buy_in = &u
	m.addmax_buy_in = nil
}

// MaxBuyIn returns the value of the "max_buy_in" field in the mutation.
func (m *TablesMutation) MaxBuyIn() (r uint64, exists bool) {
	v := m.max_buy_in
	if v == nil {
		return
	}
	return *v, true
}

// OldMaxBuyIn returns the old "max_buy_in" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldMaxBuyIn(ctx context.Context) (v uint64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaxBuyIn is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaxBuyIn requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaxBuyIn: %w", err)
	}
	return oldValue.MaxBuyIn, nil
}

// AddMaxBuyIn adds u to the "max_buy_in" field.
func (m *TablesMutation) AddMaxBuyIn(u int64) {
	if m.addmax_buy_in != nil {
		*m.addmax_buy_in += u
	} else {
		m.addmax_buy_in = &u
	}
}

// AddedMaxBuyIn returns the value that was added to the "max_buy_in" field in this mutation.
func (m *TablesMutation) AddedMaxBuyIn() (r int64, exists bool) {
	v := m.addmax_buy_in
	if v == nil {
		return
	}
	return *v, true
}

// ResetMaxBuyIn resets all changes to the "max_buy_in" field.
func (m *TablesMutation) ResetMaxBuyIn() {
	m.max_buy_in = nil
	m.addmax_buy_in = nil
}

// SetIsVisible sets the "is_visible" field.
func (m *TablesMutation) SetIsVisible(b bool) {
	m.is_visible = &b
}

// IsVisible returns the value of the "is_visible" field in the mutation.
func (m *TablesMutation) IsVisible() (r bool, exists bool) {
	v := m.is_visible
	if v == nil {
		return
	}
	return *v, true
}

// OldIsVisible returns the old "is_visible" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldIsVisible(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsVisible is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsVisible requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsVisible: %w", err)
	}
	return oldValue.IsVisible, nil
}

// ResetIsVisible resets all changes to the "is_visible" field.
func (m *TablesMutation) ResetIsVisible() {
	m.is_visible = nil
}

// SetExpiredAt sets the "expired_at" field.
func (m *TablesMutation) SetExpiredAt(t time.Time) {
	m.expired_at = &t
}

// ExpiredAt returns the value of the "expired_at" field in the mutation.
func (m *TablesMutation) ExpiredAt() (r time.Time, exists bool) {
	v := m.expired_at
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiredAt returns the old "expired_at" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldExpiredAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiredAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiredAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiredAt: %w", err)
	}
	return oldValue.ExpiredAt, nil
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (m *TablesMutation) ClearExpiredAt() {
	m.expired_at = nil
	m.clearedFields[tables.FieldExpiredAt] = struct{}{}
}

// ExpiredAtCleared returns if the "expired_at" field was cleared in this mutation.
func (m *TablesMutation) ExpiredAtCleared() bool {
	_, ok := m.clearedFields[tables.FieldExpiredAt]
	return ok
}

// ResetExpiredAt resets all changes to the "expired_at" field.
func (m *TablesMutation) ResetExpiredAt() {
	m.expired_at = nil
	delete(m.clearedFields, tables.FieldExpiredAt)
}

// SetCreatedAt sets the "created_at" field.
func (m *TablesMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *TablesMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *TablesMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetCreatedBy sets the "created_by" field.
func (m *TablesMutation) SetCreatedBy(s string) {
	m.created_by = &s
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *TablesMutation) CreatedBy() (r string, exists bool) {
	v := m.created_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldCreatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ClearCreatedBy clears the value of the "created_by" field.
func (m *TablesMutation) ClearCreatedBy() {
	m.created_by = nil
	m.clearedFields[tables.FieldCreatedBy] = struct{}{}
}

// CreatedByCleared returns if the "created_by" field was cleared in this mutation.
func (m *TablesMutation) CreatedByCleared() bool {
	_, ok := m.clearedFields[tables.FieldCreatedBy]
	return ok
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *TablesMutation) ResetCreatedBy() {
	m.created_by = nil
	delete(m.clearedFields, tables.FieldCreatedBy)
}

// SetUpdatedBy sets the "updated_by" field.
func (m *TablesMutation) SetUpdatedBy(s string) {
	m.updated_by = &s
}

// UpdatedBy returns the value of the "updated_by" field in the mutation.
func (m *TablesMutation) UpdatedBy() (r string, exists bool) {
	v := m.updated_by
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedBy returns the old "updated_by" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldUpdatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedBy: %w", err)
	}
	return oldValue.UpdatedBy, nil
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (m *TablesMutation) ClearUpdatedBy() {
	m.updated_by = nil
	m.clearedFields[tables.FieldUpdatedBy] = struct{}{}
}

// UpdatedByCleared returns if the "updated_by" field was cleared in this mutation.
func (m *TablesMutation) UpdatedByCleared() bool {
	_, ok := m.clearedFields[tables.FieldUpdatedBy]
	return ok
}

// ResetUpdatedBy resets all changes to the "updated_by" field.
func (m *TablesMutation) ResetUpdatedBy() {
	m.updated_by = nil
	delete(m.clearedFields, tables.FieldUpdatedBy)
}

// SetUpdatedAt sets the "updated_at" field.
func (m *TablesMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *TablesMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *TablesMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *TablesMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *TablesMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Tables entity.
// If the Tables object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TablesMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *TablesMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[tables.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *TablesMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[tables.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *TablesMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, tables.FieldDeletedAt)
}

// Where appends a list predicates to the TablesMutation builder.
func (m *TablesMutation) Where(ps ...predicate.Tables) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TablesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TablesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Tables, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TablesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TablesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Tables).
func (m *TablesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TablesMutation) Fields() []string {
	fields := make([]string, 0, 15)
	if m.name != nil {
		fields = append(fields, tables.FieldName)
	}
	if m.code != nil {
		fields = append(fields, tables.FieldCode)
	}
	if m._type != nil {
		fields = append(fields, tables.FieldType)
	}
	if m.status != nil {
		fields = append(fields, tables.FieldStatus)
	}
	if m.big_blind != nil {
		fields = append(fields, tables.FieldBigBlind)
	}
	if m.small_blind != nil {
		fields = append(fields, tables.FieldSmallBlind)
	}
	if m.min_buy_in != nil {
		fields = append(fields, tables.FieldMinBuyIn)
	}
	if m.max_buy_in != nil {
		fields = append(fields, tables.FieldMaxBuyIn)
	}
	if m.is_visible != nil {
		fields = append(fields, tables.FieldIsVisible)
	}
	if m.expired_at != nil {
		fields = append(fields, tables.FieldExpiredAt)
	}
	if m.created_at != nil {
		fields = append(fields, tables.FieldCreatedAt)
	}
	if m.created_by != nil {
		fields = append(fields, tables.FieldCreatedBy)
	}
	if m.updated_by != nil {
		fields = append(fields, tables.FieldUpdatedBy)
	}
	if m.updated_at != nil {
		fields = append(fields, tables.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, tables.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TablesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case tables.FieldName:
		return m.Name()
	case tables.FieldCode:
		return m.Code()
	case tables.FieldType:
		return m.GetType()
	case tables.FieldStatus:
		return m.Status()
	case tables.FieldBigBlind:
		return m.BigBlind()
	case tables.FieldSmallBlind:
		return m.SmallBlind()
	case tables.FieldMinBuyIn:
		return m.MinBuyIn()
	case tables.FieldMaxBuyIn:
		return m.MaxBuyIn()
	case tables.FieldIsVisible:
		return m.IsVisible()
	case tables.FieldExpiredAt:
		return m.ExpiredAt()
	case tables.FieldCreatedAt:
		return m.CreatedAt()
	case tables.FieldCreatedBy:
		return m.CreatedBy()
	case tables.FieldUpdatedBy:
		return m.UpdatedBy()
	case tables.FieldUpdatedAt:
		return m.UpdatedAt()
	case tables.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TablesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case tables.FieldName:
		return m.OldName(ctx)
	case tables.FieldCode:
		return m.OldCode(ctx)
	case tables.FieldType:
		return m.OldType(ctx)
	case tables.FieldStatus:
		return m.OldStatus(ctx)
	case tables.FieldBigBlind:
		return m.OldBigBlind(ctx)
	case tables.FieldSmallBlind:
		return m.OldSmallBlind(ctx)
	case tables.FieldMinBuyIn:
		return m.OldMinBuyIn(ctx)
	case tables.FieldMaxBuyIn:
		return m.OldMaxBuyIn(ctx)
	case tables.FieldIsVisible:
		return m.OldIsVisible(ctx)
	case tables.FieldExpiredAt:
		return m.OldExpiredAt(ctx)
	case tables.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case tables.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case tables.FieldUpdatedBy:
		return m.OldUpdatedBy(ctx)
	case tables.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case tables.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Tables field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TablesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case tables.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case tables.FieldCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCode(v)
		return nil
	case tables.FieldType:
		v, ok := value.(tables.Type)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case tables.FieldStatus:
		v, ok := value.(tables.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case tables.FieldBigBlind:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBigBlind(v)
		return nil
	case tables.FieldSmallBlind:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSmallBlind(v)
		return nil
	case tables.FieldMinBuyIn:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMinBuyIn(v)
		return nil
	case tables.FieldMaxBuyIn:
		v, ok := value.(uint64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaxBuyIn(v)
		return nil
	case tables.FieldIsVisible:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsVisible(v)
		return nil
	case tables.FieldExpiredAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiredAt(v)
		return nil
	case tables.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case tables.FieldCreatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case tables.FieldUpdatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedBy(v)
		return nil
	case tables.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case tables.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Tables field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TablesMutation) AddedFields() []string {
	var fields []string
	if m.addbig_blind != nil {
		fields = append(fields, tables.FieldBigBlind)
	}
	if m.addsmall_blind != nil {
		fields = append(fields, tables.FieldSmallBlind)
	}
	if m.addmin_buy_in != nil {
		fields = append(fields, tables.FieldMinBuyIn)
	}
	if m.addmax_buy_in != nil {
		fields = append(fields, tables.FieldMaxBuyIn)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TablesMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case tables.FieldBigBlind:
		return m.AddedBigBlind()
	case tables.FieldSmallBlind:
		return m.AddedSmallBlind()
	case tables.FieldMinBuyIn:
		return m.AddedMinBuyIn()
	case tables.FieldMaxBuyIn:
		return m.AddedMaxBuyIn()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TablesMutation) AddField(name string, value ent.Value) error {
	switch name {
	case tables.FieldBigBlind:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBigBlind(v)
		return nil
	case tables.FieldSmallBlind:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddSmallBlind(v)
		return nil
	case tables.FieldMinBuyIn:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMinBuyIn(v)
		return nil
	case tables.FieldMaxBuyIn:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMaxBuyIn(v)
		return nil
	}
	return fmt.Errorf("unknown Tables numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TablesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(tables.FieldExpiredAt) {
		fields = append(fields, tables.FieldExpiredAt)
	}
	if m.FieldCleared(tables.FieldCreatedBy) {
		fields = append(fields, tables.FieldCreatedBy)
	}
	if m.FieldCleared(tables.FieldUpdatedBy) {
		fields = append(fields, tables.FieldUpdatedBy)
	}
	if m.FieldCleared(tables.FieldDeletedAt) {
		fields = append(fields, tables.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TablesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TablesMutation) ClearField(name string) error {
	switch name {
	case tables.FieldExpiredAt:
		m.ClearExpiredAt()
		return nil
	case tables.FieldCreatedBy:
		m.ClearCreatedBy()
		return nil
	case tables.FieldUpdatedBy:
		m.ClearUpdatedBy()
		return nil
	case tables.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Tables nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TablesMutation) ResetField(name string) error {
	switch name {
	case tables.FieldName:
		m.ResetName()
		return nil
	case tables.FieldCode:
		m.ResetCode()
		return nil
	case tables.FieldType:
		m.ResetType()
		return nil
	case tables.FieldStatus:
		m.ResetStatus()
		return nil
	case tables.FieldBigBlind:
		m.ResetBigBlind()
		return nil
	case tables.FieldSmallBlind:
		m.ResetSmallBlind()
		return nil
	case tables.FieldMinBuyIn:
		m.ResetMinBuyIn()
		return nil
	case tables.FieldMaxBuyIn:
		m.ResetMaxBuyIn()
		return nil
	case tables.FieldIsVisible:
		m.ResetIsVisible()
		return nil
	case tables.FieldExpiredAt:
		m.ResetExpiredAt()
		return nil
	case tables.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case tables.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case tables.FieldUpdatedBy:
		m.ResetUpdatedBy()
		return nil
	case tables.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case tables.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Tables field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TablesMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TablesMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TablesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TablesMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TablesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TablesMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TablesMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Tables unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TablesMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Tables edge %s", name)
}
