// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"hamster/internal/db/ent/internal"
	"hamster/internal/db/ent/predicate"
	"hamster/internal/db/ent/tables"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TablesUpdate is the builder for updating Tables entities.
type TablesUpdate struct {
	config
	hooks    []Hook
	mutation *TablesMutation
}

// Where appends a list predicates to the TablesUpdate builder.
func (tu *TablesUpdate) Where(ps ...predicate.Tables) *TablesUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetName sets the "name" field.
func (tu *TablesUpdate) SetName(s string) *TablesUpdate {
	tu.mutation.SetName(s)
	return tu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableName(s *string) *TablesUpdate {
	if s != nil {
		tu.SetName(*s)
	}
	return tu
}

// SetCode sets the "code" field.
func (tu *TablesUpdate) SetCode(s string) *TablesUpdate {
	tu.mutation.SetCode(s)
	return tu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableCode(s *string) *TablesUpdate {
	if s != nil {
		tu.SetCode(*s)
	}
	return tu
}

// SetType sets the "type" field.
func (tu *TablesUpdate) SetType(t tables.Type) *TablesUpdate {
	tu.mutation.SetType(t)
	return tu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableType(t *tables.Type) *TablesUpdate {
	if t != nil {
		tu.SetType(*t)
	}
	return tu
}

// SetStatus sets the "status" field.
func (tu *TablesUpdate) SetStatus(t tables.Status) *TablesUpdate {
	tu.mutation.SetStatus(t)
	return tu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableStatus(t *tables.Status) *TablesUpdate {
	if t != nil {
		tu.SetStatus(*t)
	}
	return tu
}

// SetBigBlind sets the "big_blind" field.
func (tu *TablesUpdate) SetBigBlind(u uint64) *TablesUpdate {
	tu.mutation.ResetBigBlind()
	tu.mutation.SetBigBlind(u)
	return tu
}

// SetNillableBigBlind sets the "big_blind" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableBigBlind(u *uint64) *TablesUpdate {
	if u != nil {
		tu.SetBigBlind(*u)
	}
	return tu
}

// AddBigBlind adds u to the "big_blind" field.
func (tu *TablesUpdate) AddBigBlind(u int64) *TablesUpdate {
	tu.mutation.AddBigBlind(u)
	return tu
}

// SetSmallBlind sets the "small_blind" field.
func (tu *TablesUpdate) SetSmallBlind(u uint64) *TablesUpdate {
	tu.mutation.ResetSmallBlind()
	tu.mutation.SetSmallBlind(u)
	return tu
}

// SetNillableSmallBlind sets the "small_blind" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableSmallBlind(u *uint64) *TablesUpdate {
	if u != nil {
		tu.SetSmallBlind(*u)
	}
	return tu
}

// AddSmallBlind adds u to the "small_blind" field.
func (tu *TablesUpdate) AddSmallBlind(u int64) *TablesUpdate {
	tu.mutation.AddSmallBlind(u)
	return tu
}

// SetMinBuyIn sets the "min_buy_in" field.
func (tu *TablesUpdate) SetMinBuyIn(u uint64) *TablesUpdate {
	tu.mutation.ResetMinBuyIn()
	tu.mutation.SetMinBuyIn(u)
	return tu
}

// SetNillableMinBuyIn sets the "min_buy_in" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableMinBuyIn(u *uint64) *TablesUpdate {
	if u != nil {
		tu.SetMinBuyIn(*u)
	}
	return tu
}

// AddMinBuyIn adds u to the "min_buy_in" field.
func (tu *TablesUpdate) AddMinBuyIn(u int64) *TablesUpdate {
	tu.mutation.AddMinBuyIn(u)
	return tu
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (tu *TablesUpdate) SetMaxBuyIn(u uint64) *TablesUpdate {
	tu.mutation.ResetMaxBuyIn()
	tu.mutation.SetMaxBuyIn(u)
	return tu
}

// SetNillableMaxBuyIn sets the "max_buy_in" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableMaxBuyIn(u *uint64) *TablesUpdate {
	if u != nil {
		tu.SetMaxBuyIn(*u)
	}
	return tu
}

// AddMaxBuyIn adds u to the "max_buy_in" field.
func (tu *TablesUpdate) AddMaxBuyIn(u int64) *TablesUpdate {
	tu.mutation.AddMaxBuyIn(u)
	return tu
}

// SetIsVisible sets the "is_visible" field.
func (tu *TablesUpdate) SetIsVisible(b bool) *TablesUpdate {
	tu.mutation.SetIsVisible(b)
	return tu
}

// SetNillableIsVisible sets the "is_visible" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableIsVisible(b *bool) *TablesUpdate {
	if b != nil {
		tu.SetIsVisible(*b)
	}
	return tu
}

// SetExpiredAt sets the "expired_at" field.
func (tu *TablesUpdate) SetExpiredAt(t time.Time) *TablesUpdate {
	tu.mutation.SetExpiredAt(t)
	return tu
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableExpiredAt(t *time.Time) *TablesUpdate {
	if t != nil {
		tu.SetExpiredAt(*t)
	}
	return tu
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (tu *TablesUpdate) ClearExpiredAt() *TablesUpdate {
	tu.mutation.ClearExpiredAt()
	return tu
}

// SetUpdatedAt sets the "updated_at" field.
func (tu *TablesUpdate) SetUpdatedAt(t time.Time) *TablesUpdate {
	tu.mutation.SetUpdatedAt(t)
	return tu
}

// SetDeletedAt sets the "deleted_at" field.
func (tu *TablesUpdate) SetDeletedAt(t time.Time) *TablesUpdate {
	tu.mutation.SetDeletedAt(t)
	return tu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tu *TablesUpdate) SetNillableDeletedAt(t *time.Time) *TablesUpdate {
	if t != nil {
		tu.SetDeletedAt(*t)
	}
	return tu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tu *TablesUpdate) ClearDeletedAt() *TablesUpdate {
	tu.mutation.ClearDeletedAt()
	return tu
}

// Mutation returns the TablesMutation object of the builder.
func (tu *TablesUpdate) Mutation() *TablesMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TablesUpdate) Save(ctx context.Context) (int, error) {
	tu.defaults()
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TablesUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TablesUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TablesUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tu *TablesUpdate) defaults() {
	if _, ok := tu.mutation.UpdatedAt(); !ok {
		v := tables.UpdateDefaultUpdatedAt()
		tu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TablesUpdate) check() error {
	if v, ok := tu.mutation.Name(); ok {
		if err := tables.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tables.name": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Code(); ok {
		if err := tables.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tables.code": %w`, err)}
		}
	}
	if v, ok := tu.mutation.GetType(); ok {
		if err := tables.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Tables.type": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Status(); ok {
		if err := tables.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tables.status": %w`, err)}
		}
	}
	return nil
}

func (tu *TablesUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(tables.Table, tables.Columns, sqlgraph.NewFieldSpec(tables.FieldID, field.TypeUint64))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.Name(); ok {
		_spec.SetField(tables.FieldName, field.TypeString, value)
	}
	if value, ok := tu.mutation.Code(); ok {
		_spec.SetField(tables.FieldCode, field.TypeString, value)
	}
	if value, ok := tu.mutation.GetType(); ok {
		_spec.SetField(tables.FieldType, field.TypeEnum, value)
	}
	if value, ok := tu.mutation.Status(); ok {
		_spec.SetField(tables.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := tu.mutation.BigBlind(); ok {
		_spec.SetField(tables.FieldBigBlind, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.AddedBigBlind(); ok {
		_spec.AddField(tables.FieldBigBlind, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.SmallBlind(); ok {
		_spec.SetField(tables.FieldSmallBlind, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.AddedSmallBlind(); ok {
		_spec.AddField(tables.FieldSmallBlind, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.MinBuyIn(); ok {
		_spec.SetField(tables.FieldMinBuyIn, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.AddedMinBuyIn(); ok {
		_spec.AddField(tables.FieldMinBuyIn, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.MaxBuyIn(); ok {
		_spec.SetField(tables.FieldMaxBuyIn, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.AddedMaxBuyIn(); ok {
		_spec.AddField(tables.FieldMaxBuyIn, field.TypeUint64, value)
	}
	if value, ok := tu.mutation.IsVisible(); ok {
		_spec.SetField(tables.FieldIsVisible, field.TypeBool, value)
	}
	if value, ok := tu.mutation.ExpiredAt(); ok {
		_spec.SetField(tables.FieldExpiredAt, field.TypeTime, value)
	}
	if tu.mutation.ExpiredAtCleared() {
		_spec.ClearField(tables.FieldExpiredAt, field.TypeTime)
	}
	if tu.mutation.CreatedByCleared() {
		_spec.ClearField(tables.FieldCreatedBy, field.TypeString)
	}
	if tu.mutation.UpdatedByCleared() {
		_spec.ClearField(tables.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := tu.mutation.UpdatedAt(); ok {
		_spec.SetField(tables.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tu.mutation.DeletedAt(); ok {
		_spec.SetField(tables.FieldDeletedAt, field.TypeTime, value)
	}
	if tu.mutation.DeletedAtCleared() {
		_spec.ClearField(tables.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = tu.schemaConfig.Tables
	ctx = internal.NewSchemaConfigContext(ctx, tu.schemaConfig)
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tables.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TablesUpdateOne is the builder for updating a single Tables entity.
type TablesUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TablesMutation
}

// SetName sets the "name" field.
func (tuo *TablesUpdateOne) SetName(s string) *TablesUpdateOne {
	tuo.mutation.SetName(s)
	return tuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableName(s *string) *TablesUpdateOne {
	if s != nil {
		tuo.SetName(*s)
	}
	return tuo
}

// SetCode sets the "code" field.
func (tuo *TablesUpdateOne) SetCode(s string) *TablesUpdateOne {
	tuo.mutation.SetCode(s)
	return tuo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableCode(s *string) *TablesUpdateOne {
	if s != nil {
		tuo.SetCode(*s)
	}
	return tuo
}

// SetType sets the "type" field.
func (tuo *TablesUpdateOne) SetType(t tables.Type) *TablesUpdateOne {
	tuo.mutation.SetType(t)
	return tuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableType(t *tables.Type) *TablesUpdateOne {
	if t != nil {
		tuo.SetType(*t)
	}
	return tuo
}

// SetStatus sets the "status" field.
func (tuo *TablesUpdateOne) SetStatus(t tables.Status) *TablesUpdateOne {
	tuo.mutation.SetStatus(t)
	return tuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableStatus(t *tables.Status) *TablesUpdateOne {
	if t != nil {
		tuo.SetStatus(*t)
	}
	return tuo
}

// SetBigBlind sets the "big_blind" field.
func (tuo *TablesUpdateOne) SetBigBlind(u uint64) *TablesUpdateOne {
	tuo.mutation.ResetBigBlind()
	tuo.mutation.SetBigBlind(u)
	return tuo
}

// SetNillableBigBlind sets the "big_blind" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableBigBlind(u *uint64) *TablesUpdateOne {
	if u != nil {
		tuo.SetBigBlind(*u)
	}
	return tuo
}

// AddBigBlind adds u to the "big_blind" field.
func (tuo *TablesUpdateOne) AddBigBlind(u int64) *TablesUpdateOne {
	tuo.mutation.AddBigBlind(u)
	return tuo
}

// SetSmallBlind sets the "small_blind" field.
func (tuo *TablesUpdateOne) SetSmallBlind(u uint64) *TablesUpdateOne {
	tuo.mutation.ResetSmallBlind()
	tuo.mutation.SetSmallBlind(u)
	return tuo
}

// SetNillableSmallBlind sets the "small_blind" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableSmallBlind(u *uint64) *TablesUpdateOne {
	if u != nil {
		tuo.SetSmallBlind(*u)
	}
	return tuo
}

// AddSmallBlind adds u to the "small_blind" field.
func (tuo *TablesUpdateOne) AddSmallBlind(u int64) *TablesUpdateOne {
	tuo.mutation.AddSmallBlind(u)
	return tuo
}

// SetMinBuyIn sets the "min_buy_in" field.
func (tuo *TablesUpdateOne) SetMinBuyIn(u uint64) *TablesUpdateOne {
	tuo.mutation.ResetMinBuyIn()
	tuo.mutation.SetMinBuyIn(u)
	return tuo
}

// SetNillableMinBuyIn sets the "min_buy_in" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableMinBuyIn(u *uint64) *TablesUpdateOne {
	if u != nil {
		tuo.SetMinBuyIn(*u)
	}
	return tuo
}

// AddMinBuyIn adds u to the "min_buy_in" field.
func (tuo *TablesUpdateOne) AddMinBuyIn(u int64) *TablesUpdateOne {
	tuo.mutation.AddMinBuyIn(u)
	return tuo
}

// SetMaxBuyIn sets the "max_buy_in" field.
func (tuo *TablesUpdateOne) SetMaxBuyIn(u uint64) *TablesUpdateOne {
	tuo.mutation.ResetMaxBuyIn()
	tuo.mutation.SetMaxBuyIn(u)
	return tuo
}

// SetNillableMaxBuyIn sets the "max_buy_in" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableMaxBuyIn(u *uint64) *TablesUpdateOne {
	if u != nil {
		tuo.SetMaxBuyIn(*u)
	}
	return tuo
}

// AddMaxBuyIn adds u to the "max_buy_in" field.
func (tuo *TablesUpdateOne) AddMaxBuyIn(u int64) *TablesUpdateOne {
	tuo.mutation.AddMaxBuyIn(u)
	return tuo
}

// SetIsVisible sets the "is_visible" field.
func (tuo *TablesUpdateOne) SetIsVisible(b bool) *TablesUpdateOne {
	tuo.mutation.SetIsVisible(b)
	return tuo
}

// SetNillableIsVisible sets the "is_visible" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableIsVisible(b *bool) *TablesUpdateOne {
	if b != nil {
		tuo.SetIsVisible(*b)
	}
	return tuo
}

// SetExpiredAt sets the "expired_at" field.
func (tuo *TablesUpdateOne) SetExpiredAt(t time.Time) *TablesUpdateOne {
	tuo.mutation.SetExpiredAt(t)
	return tuo
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableExpiredAt(t *time.Time) *TablesUpdateOne {
	if t != nil {
		tuo.SetExpiredAt(*t)
	}
	return tuo
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (tuo *TablesUpdateOne) ClearExpiredAt() *TablesUpdateOne {
	tuo.mutation.ClearExpiredAt()
	return tuo
}

// SetUpdatedAt sets the "updated_at" field.
func (tuo *TablesUpdateOne) SetUpdatedAt(t time.Time) *TablesUpdateOne {
	tuo.mutation.SetUpdatedAt(t)
	return tuo
}

// SetDeletedAt sets the "deleted_at" field.
func (tuo *TablesUpdateOne) SetDeletedAt(t time.Time) *TablesUpdateOne {
	tuo.mutation.SetDeletedAt(t)
	return tuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuo *TablesUpdateOne) SetNillableDeletedAt(t *time.Time) *TablesUpdateOne {
	if t != nil {
		tuo.SetDeletedAt(*t)
	}
	return tuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuo *TablesUpdateOne) ClearDeletedAt() *TablesUpdateOne {
	tuo.mutation.ClearDeletedAt()
	return tuo
}

// Mutation returns the TablesMutation object of the builder.
func (tuo *TablesUpdateOne) Mutation() *TablesMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TablesUpdate builder.
func (tuo *TablesUpdateOne) Where(ps ...predicate.Tables) *TablesUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TablesUpdateOne) Select(field string, fields ...string) *TablesUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Tables entity.
func (tuo *TablesUpdateOne) Save(ctx context.Context) (*Tables, error) {
	tuo.defaults()
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TablesUpdateOne) SaveX(ctx context.Context) *Tables {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TablesUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TablesUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuo *TablesUpdateOne) defaults() {
	if _, ok := tuo.mutation.UpdatedAt(); !ok {
		v := tables.UpdateDefaultUpdatedAt()
		tuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TablesUpdateOne) check() error {
	if v, ok := tuo.mutation.Name(); ok {
		if err := tables.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tables.name": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Code(); ok {
		if err := tables.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tables.code": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.GetType(); ok {
		if err := tables.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Tables.type": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Status(); ok {
		if err := tables.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tables.status": %w`, err)}
		}
	}
	return nil
}

func (tuo *TablesUpdateOne) sqlSave(ctx context.Context) (_node *Tables, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(tables.Table, tables.Columns, sqlgraph.NewFieldSpec(tables.FieldID, field.TypeUint64))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Tables.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tables.FieldID)
		for _, f := range fields {
			if !tables.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != tables.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.Name(); ok {
		_spec.SetField(tables.FieldName, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Code(); ok {
		_spec.SetField(tables.FieldCode, field.TypeString, value)
	}
	if value, ok := tuo.mutation.GetType(); ok {
		_spec.SetField(tables.FieldType, field.TypeEnum, value)
	}
	if value, ok := tuo.mutation.Status(); ok {
		_spec.SetField(tables.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := tuo.mutation.BigBlind(); ok {
		_spec.SetField(tables.FieldBigBlind, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.AddedBigBlind(); ok {
		_spec.AddField(tables.FieldBigBlind, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.SmallBlind(); ok {
		_spec.SetField(tables.FieldSmallBlind, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.AddedSmallBlind(); ok {
		_spec.AddField(tables.FieldSmallBlind, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.MinBuyIn(); ok {
		_spec.SetField(tables.FieldMinBuyIn, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.AddedMinBuyIn(); ok {
		_spec.AddField(tables.FieldMinBuyIn, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.MaxBuyIn(); ok {
		_spec.SetField(tables.FieldMaxBuyIn, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.AddedMaxBuyIn(); ok {
		_spec.AddField(tables.FieldMaxBuyIn, field.TypeUint64, value)
	}
	if value, ok := tuo.mutation.IsVisible(); ok {
		_spec.SetField(tables.FieldIsVisible, field.TypeBool, value)
	}
	if value, ok := tuo.mutation.ExpiredAt(); ok {
		_spec.SetField(tables.FieldExpiredAt, field.TypeTime, value)
	}
	if tuo.mutation.ExpiredAtCleared() {
		_spec.ClearField(tables.FieldExpiredAt, field.TypeTime)
	}
	if tuo.mutation.CreatedByCleared() {
		_spec.ClearField(tables.FieldCreatedBy, field.TypeString)
	}
	if tuo.mutation.UpdatedByCleared() {
		_spec.ClearField(tables.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := tuo.mutation.UpdatedAt(); ok {
		_spec.SetField(tables.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuo.mutation.DeletedAt(); ok {
		_spec.SetField(tables.FieldDeletedAt, field.TypeTime, value)
	}
	if tuo.mutation.DeletedAtCleared() {
		_spec.ClearField(tables.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = tuo.schemaConfig.Tables
	ctx = internal.NewSchemaConfigContext(ctx, tuo.schemaConfig)
	_node = &Tables{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tables.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
