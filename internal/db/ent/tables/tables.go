// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package tables

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the tables type in the database.
	Label = "tables"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldCode holds the string denoting the code field in the database.
	FieldCode = "code"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldBigBlind holds the string denoting the big_blind field in the database.
	FieldBigBlind = "big_blind"
	// FieldSmallBlind holds the string denoting the small_blind field in the database.
	FieldSmallBlind = "small_blind"
	// FieldMinBuyIn holds the string denoting the min_buy_in field in the database.
	FieldMinBuyIn = "min_buy_in"
	// FieldMaxBuyIn holds the string denoting the max_buy_in field in the database.
	FieldMaxBuyIn = "max_buy_in"
	// FieldIsVisible holds the string denoting the is_visible field in the database.
	FieldIsVisible = "is_visible"
	// FieldExpiredAt holds the string denoting the expired_at field in the database.
	FieldExpiredAt = "expired_at"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the tables in the database.
	Table = "tables"
)

// Columns holds all SQL columns for tables fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldCode,
	FieldType,
	FieldStatus,
	FieldBigBlind,
	FieldSmallBlind,
	FieldMinBuyIn,
	FieldMaxBuyIn,
	FieldIsVisible,
	FieldExpiredAt,
	FieldCreatedAt,
	FieldCreatedBy,
	FieldUpdatedBy,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// CodeValidator is a validator for the "code" field. It is called by the builders before save.
	CodeValidator func(string) error
	// DefaultIsVisible holds the default value on creation for the "is_visible" field.
	DefaultIsVisible bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// Type defines the type for the "type" enum field.
type Type string

// Type6p is the default value of the Type enum.
const DefaultType = Type6p

// Type values.
const (
	Type6p Type = "6p"
	Type9p Type = "9p"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case Type6p, Type9p:
		return nil
	default:
		return fmt.Errorf("tables: invalid enum value for type field: %q", _type)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusActive is the default value of the Status enum.
const DefaultStatus = StatusActive

// Status values.
const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusActive, StatusInactive:
		return nil
	default:
		return fmt.Errorf("tables: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Tables queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByCode orders the results by the code field.
func ByCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCode, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByBigBlind orders the results by the big_blind field.
func ByBigBlind(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBigBlind, opts...).ToFunc()
}

// BySmallBlind orders the results by the small_blind field.
func BySmallBlind(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSmallBlind, opts...).ToFunc()
}

// ByMinBuyIn orders the results by the min_buy_in field.
func ByMinBuyIn(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMinBuyIn, opts...).ToFunc()
}

// ByMaxBuyIn orders the results by the max_buy_in field.
func ByMaxBuyIn(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxBuyIn, opts...).ToFunc()
}

// ByIsVisible orders the results by the is_visible field.
func ByIsVisible(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsVisible, opts...).ToFunc()
}

// ByExpiredAt orders the results by the expired_at field.
func ByExpiredAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiredAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}
