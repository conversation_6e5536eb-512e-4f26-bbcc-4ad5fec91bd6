// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package tables

import (
	"hamster/internal/db/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCode, v))
}

// BigBlind applies equality check predicate on the "big_blind" field. It's identical to BigBlindEQ.
func BigBlind(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldBigBlind, v))
}

// SmallBlind applies equality check predicate on the "small_blind" field. It's identical to SmallBlindEQ.
func SmallBlind(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldSmallBlind, v))
}

// MinBuyIn applies equality check predicate on the "min_buy_in" field. It's identical to MinBuyInEQ.
func MinBuyIn(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldMinBuyIn, v))
}

// MaxBuyIn applies equality check predicate on the "max_buy_in" field. It's identical to MaxBuyInEQ.
func MaxBuyIn(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldMaxBuyIn, v))
}

// IsVisible applies equality check predicate on the "is_visible" field. It's identical to IsVisibleEQ.
func IsVisible(v bool) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldIsVisible, v))
}

// ExpiredAt applies equality check predicate on the "expired_at" field. It's identical to ExpiredAtEQ.
func ExpiredAt(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldExpiredAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCreatedBy, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldDeletedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContainsFold(FieldName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContainsFold(FieldCode, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldType, vs...))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldStatus, vs...))
}

// BigBlindEQ applies the EQ predicate on the "big_blind" field.
func BigBlindEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldBigBlind, v))
}

// BigBlindNEQ applies the NEQ predicate on the "big_blind" field.
func BigBlindNEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldBigBlind, v))
}

// BigBlindIn applies the In predicate on the "big_blind" field.
func BigBlindIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldBigBlind, vs...))
}

// BigBlindNotIn applies the NotIn predicate on the "big_blind" field.
func BigBlindNotIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldBigBlind, vs...))
}

// BigBlindGT applies the GT predicate on the "big_blind" field.
func BigBlindGT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldBigBlind, v))
}

// BigBlindGTE applies the GTE predicate on the "big_blind" field.
func BigBlindGTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldBigBlind, v))
}

// BigBlindLT applies the LT predicate on the "big_blind" field.
func BigBlindLT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldBigBlind, v))
}

// BigBlindLTE applies the LTE predicate on the "big_blind" field.
func BigBlindLTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldBigBlind, v))
}

// SmallBlindEQ applies the EQ predicate on the "small_blind" field.
func SmallBlindEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldSmallBlind, v))
}

// SmallBlindNEQ applies the NEQ predicate on the "small_blind" field.
func SmallBlindNEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldSmallBlind, v))
}

// SmallBlindIn applies the In predicate on the "small_blind" field.
func SmallBlindIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldSmallBlind, vs...))
}

// SmallBlindNotIn applies the NotIn predicate on the "small_blind" field.
func SmallBlindNotIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldSmallBlind, vs...))
}

// SmallBlindGT applies the GT predicate on the "small_blind" field.
func SmallBlindGT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldSmallBlind, v))
}

// SmallBlindGTE applies the GTE predicate on the "small_blind" field.
func SmallBlindGTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldSmallBlind, v))
}

// SmallBlindLT applies the LT predicate on the "small_blind" field.
func SmallBlindLT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldSmallBlind, v))
}

// SmallBlindLTE applies the LTE predicate on the "small_blind" field.
func SmallBlindLTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldSmallBlind, v))
}

// MinBuyInEQ applies the EQ predicate on the "min_buy_in" field.
func MinBuyInEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldMinBuyIn, v))
}

// MinBuyInNEQ applies the NEQ predicate on the "min_buy_in" field.
func MinBuyInNEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldMinBuyIn, v))
}

// MinBuyInIn applies the In predicate on the "min_buy_in" field.
func MinBuyInIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldMinBuyIn, vs...))
}

// MinBuyInNotIn applies the NotIn predicate on the "min_buy_in" field.
func MinBuyInNotIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldMinBuyIn, vs...))
}

// MinBuyInGT applies the GT predicate on the "min_buy_in" field.
func MinBuyInGT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldMinBuyIn, v))
}

// MinBuyInGTE applies the GTE predicate on the "min_buy_in" field.
func MinBuyInGTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldMinBuyIn, v))
}

// MinBuyInLT applies the LT predicate on the "min_buy_in" field.
func MinBuyInLT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldMinBuyIn, v))
}

// MinBuyInLTE applies the LTE predicate on the "min_buy_in" field.
func MinBuyInLTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldMinBuyIn, v))
}

// MaxBuyInEQ applies the EQ predicate on the "max_buy_in" field.
func MaxBuyInEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldMaxBuyIn, v))
}

// MaxBuyInNEQ applies the NEQ predicate on the "max_buy_in" field.
func MaxBuyInNEQ(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldMaxBuyIn, v))
}

// MaxBuyInIn applies the In predicate on the "max_buy_in" field.
func MaxBuyInIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldMaxBuyIn, vs...))
}

// MaxBuyInNotIn applies the NotIn predicate on the "max_buy_in" field.
func MaxBuyInNotIn(vs ...uint64) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldMaxBuyIn, vs...))
}

// MaxBuyInGT applies the GT predicate on the "max_buy_in" field.
func MaxBuyInGT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldMaxBuyIn, v))
}

// MaxBuyInGTE applies the GTE predicate on the "max_buy_in" field.
func MaxBuyInGTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldMaxBuyIn, v))
}

// MaxBuyInLT applies the LT predicate on the "max_buy_in" field.
func MaxBuyInLT(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldMaxBuyIn, v))
}

// MaxBuyInLTE applies the LTE predicate on the "max_buy_in" field.
func MaxBuyInLTE(v uint64) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldMaxBuyIn, v))
}

// IsVisibleEQ applies the EQ predicate on the "is_visible" field.
func IsVisibleEQ(v bool) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldIsVisible, v))
}

// IsVisibleNEQ applies the NEQ predicate on the "is_visible" field.
func IsVisibleNEQ(v bool) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldIsVisible, v))
}

// ExpiredAtEQ applies the EQ predicate on the "expired_at" field.
func ExpiredAtEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldExpiredAt, v))
}

// ExpiredAtNEQ applies the NEQ predicate on the "expired_at" field.
func ExpiredAtNEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldExpiredAt, v))
}

// ExpiredAtIn applies the In predicate on the "expired_at" field.
func ExpiredAtIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldExpiredAt, vs...))
}

// ExpiredAtNotIn applies the NotIn predicate on the "expired_at" field.
func ExpiredAtNotIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldExpiredAt, vs...))
}

// ExpiredAtGT applies the GT predicate on the "expired_at" field.
func ExpiredAtGT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldExpiredAt, v))
}

// ExpiredAtGTE applies the GTE predicate on the "expired_at" field.
func ExpiredAtGTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldExpiredAt, v))
}

// ExpiredAtLT applies the LT predicate on the "expired_at" field.
func ExpiredAtLT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldExpiredAt, v))
}

// ExpiredAtLTE applies the LTE predicate on the "expired_at" field.
func ExpiredAtLTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldExpiredAt, v))
}

// ExpiredAtIsNil applies the IsNil predicate on the "expired_at" field.
func ExpiredAtIsNil() predicate.Tables {
	return predicate.Tables(sql.FieldIsNull(FieldExpiredAt))
}

// ExpiredAtNotNil applies the NotNil predicate on the "expired_at" field.
func ExpiredAtNotNil() predicate.Tables {
	return predicate.Tables(sql.FieldNotNull(FieldExpiredAt))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldCreatedAt, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.Tables {
	return predicate.Tables(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.Tables {
	return predicate.Tables(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContainsFold(FieldCreatedBy, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v string) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...string) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v string) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByContains applies the Contains predicate on the "updated_by" field.
func UpdatedByContains(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContains(FieldUpdatedBy, v))
}

// UpdatedByHasPrefix applies the HasPrefix predicate on the "updated_by" field.
func UpdatedByHasPrefix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasPrefix(FieldUpdatedBy, v))
}

// UpdatedByHasSuffix applies the HasSuffix predicate on the "updated_by" field.
func UpdatedByHasSuffix(v string) predicate.Tables {
	return predicate.Tables(sql.FieldHasSuffix(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.Tables {
	return predicate.Tables(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.Tables {
	return predicate.Tables(sql.FieldNotNull(FieldUpdatedBy))
}

// UpdatedByEqualFold applies the EqualFold predicate on the "updated_by" field.
func UpdatedByEqualFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldEqualFold(FieldUpdatedBy, v))
}

// UpdatedByContainsFold applies the ContainsFold predicate on the "updated_by" field.
func UpdatedByContainsFold(v string) predicate.Tables {
	return predicate.Tables(sql.FieldContainsFold(FieldUpdatedBy, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Tables {
	return predicate.Tables(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Tables {
	return predicate.Tables(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Tables {
	return predicate.Tables(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Tables) predicate.Tables {
	return predicate.Tables(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Tables) predicate.Tables {
	return predicate.Tables(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Tables) predicate.Tables {
	return predicate.Tables(sql.NotPredicates(p))
}
