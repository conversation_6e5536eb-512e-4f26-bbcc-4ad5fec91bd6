// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"hamster/internal/db/ent/games"
	"hamster/internal/db/ent/internal"
	"hamster/internal/db/ent/predicate"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// GamesUpdate is the builder for updating Games entities.
type GamesUpdate struct {
	config
	hooks    []Hook
	mutation *GamesMutation
}

// Where appends a list predicates to the GamesUpdate builder.
func (gu *GamesUpdate) Where(ps ...predicate.Games) *GamesUpdate {
	gu.mutation.Where(ps...)
	return gu
}

// SetTableID sets the "table_id" field.
func (gu *GamesUpdate) SetTableID(u uint64) *GamesUpdate {
	gu.mutation.ResetTableID()
	gu.mutation.SetTableID(u)
	return gu
}

// SetNillableTableID sets the "table_id" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableTableID(u *uint64) *GamesUpdate {
	if u != nil {
		gu.SetTableID(*u)
	}
	return gu
}

// AddTableID adds u to the "table_id" field.
func (gu *GamesUpdate) AddTableID(u int64) *GamesUpdate {
	gu.mutation.AddTableID(u)
	return gu
}

// SetStatus sets the "status" field.
func (gu *GamesUpdate) SetStatus(ga games.Status) *GamesUpdate {
	gu.mutation.SetStatus(ga)
	return gu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableStatus(ga *games.Status) *GamesUpdate {
	if ga != nil {
		gu.SetStatus(*ga)
	}
	return gu
}

// SetSeats sets the "seats" field.
func (gu *GamesUpdate) SetSeats(s []string) *GamesUpdate {
	gu.mutation.SetSeats(s)
	return gu
}

// AppendSeats appends s to the "seats" field.
func (gu *GamesUpdate) AppendSeats(s []string) *GamesUpdate {
	gu.mutation.AppendSeats(s)
	return gu
}

// SetPot sets the "pot" field.
func (gu *GamesUpdate) SetPot(u uint64) *GamesUpdate {
	gu.mutation.ResetPot()
	gu.mutation.SetPot(u)
	return gu
}

// SetNillablePot sets the "pot" field if the given value is not nil.
func (gu *GamesUpdate) SetNillablePot(u *uint64) *GamesUpdate {
	if u != nil {
		gu.SetPot(*u)
	}
	return gu
}

// AddPot adds u to the "pot" field.
func (gu *GamesUpdate) AddPot(u int64) *GamesUpdate {
	gu.mutation.AddPot(u)
	return gu
}

// SetButton sets the "button" field.
func (gu *GamesUpdate) SetButton(i int) *GamesUpdate {
	gu.mutation.ResetButton()
	gu.mutation.SetButton(i)
	return gu
}

// SetNillableButton sets the "button" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableButton(i *int) *GamesUpdate {
	if i != nil {
		gu.SetButton(*i)
	}
	return gu
}

// AddButton adds i to the "button" field.
func (gu *GamesUpdate) AddButton(i int) *GamesUpdate {
	gu.mutation.AddButton(i)
	return gu
}

// SetSb sets the "sb" field.
func (gu *GamesUpdate) SetSb(i int) *GamesUpdate {
	gu.mutation.ResetSb()
	gu.mutation.SetSb(i)
	return gu
}

// SetNillableSb sets the "sb" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableSb(i *int) *GamesUpdate {
	if i != nil {
		gu.SetSb(*i)
	}
	return gu
}

// AddSb adds i to the "sb" field.
func (gu *GamesUpdate) AddSb(i int) *GamesUpdate {
	gu.mutation.AddSb(i)
	return gu
}

// SetBb sets the "bb" field.
func (gu *GamesUpdate) SetBb(i int) *GamesUpdate {
	gu.mutation.ResetBb()
	gu.mutation.SetBb(i)
	return gu
}

// SetNillableBb sets the "bb" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableBb(i *int) *GamesUpdate {
	if i != nil {
		gu.SetBb(*i)
	}
	return gu
}

// AddBb adds i to the "bb" field.
func (gu *GamesUpdate) AddBb(i int) *GamesUpdate {
	gu.mutation.AddBb(i)
	return gu
}

// SetStakes sets the "stakes" field.
func (gu *GamesUpdate) SetStakes(u []uint64) *GamesUpdate {
	gu.mutation.SetStakes(u)
	return gu
}

// AppendStakes appends u to the "stakes" field.
func (gu *GamesUpdate) AppendStakes(u []uint64) *GamesUpdate {
	gu.mutation.AppendStakes(u)
	return gu
}

// SetPlayers sets the "players" field.
func (gu *GamesUpdate) SetPlayers(ms []model.PlayerState) *GamesUpdate {
	gu.mutation.SetPlayers(ms)
	return gu
}

// AppendPlayers appends ms to the "players" field.
func (gu *GamesUpdate) AppendPlayers(ms []model.PlayerState) *GamesUpdate {
	gu.mutation.AppendPlayers(ms)
	return gu
}

// SetWinners sets the "winners" field.
func (gu *GamesUpdate) SetWinners(ms []model.PlayerState) *GamesUpdate {
	gu.mutation.SetWinners(ms)
	return gu
}

// AppendWinners appends ms to the "winners" field.
func (gu *GamesUpdate) AppendWinners(ms []model.PlayerState) *GamesUpdate {
	gu.mutation.AppendWinners(ms)
	return gu
}

// SetBoard sets the "board" field.
func (gu *GamesUpdate) SetBoard(po poker.Cards) *GamesUpdate {
	gu.mutation.SetBoard(po)
	return gu
}

// AppendBoard appends po to the "board" field.
func (gu *GamesUpdate) AppendBoard(po poker.Cards) *GamesUpdate {
	gu.mutation.AppendBoard(po)
	return gu
}

// SetDuration sets the "duration" field.
func (gu *GamesUpdate) SetDuration(i int64) *GamesUpdate {
	gu.mutation.ResetDuration()
	gu.mutation.SetDuration(i)
	return gu
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableDuration(i *int64) *GamesUpdate {
	if i != nil {
		gu.SetDuration(*i)
	}
	return gu
}

// AddDuration adds i to the "duration" field.
func (gu *GamesUpdate) AddDuration(i int64) *GamesUpdate {
	gu.mutation.AddDuration(i)
	return gu
}

// SetCompletedAt sets the "completed_at" field.
func (gu *GamesUpdate) SetCompletedAt(t time.Time) *GamesUpdate {
	gu.mutation.SetCompletedAt(t)
	return gu
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableCompletedAt(t *time.Time) *GamesUpdate {
	if t != nil {
		gu.SetCompletedAt(*t)
	}
	return gu
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (gu *GamesUpdate) ClearCompletedAt() *GamesUpdate {
	gu.mutation.ClearCompletedAt()
	return gu
}

// SetSettled sets the "settled" field.
func (gu *GamesUpdate) SetSettled(b bool) *GamesUpdate {
	gu.mutation.SetSettled(b)
	return gu
}

// SetNillableSettled sets the "settled" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableSettled(b *bool) *GamesUpdate {
	if b != nil {
		gu.SetSettled(*b)
	}
	return gu
}

// SetUpdatedAt sets the "updated_at" field.
func (gu *GamesUpdate) SetUpdatedAt(t time.Time) *GamesUpdate {
	gu.mutation.SetUpdatedAt(t)
	return gu
}

// SetDeletedAt sets the "deleted_at" field.
func (gu *GamesUpdate) SetDeletedAt(t time.Time) *GamesUpdate {
	gu.mutation.SetDeletedAt(t)
	return gu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gu *GamesUpdate) SetNillableDeletedAt(t *time.Time) *GamesUpdate {
	if t != nil {
		gu.SetDeletedAt(*t)
	}
	return gu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (gu *GamesUpdate) ClearDeletedAt() *GamesUpdate {
	gu.mutation.ClearDeletedAt()
	return gu
}

// Mutation returns the GamesMutation object of the builder.
func (gu *GamesUpdate) Mutation() *GamesMutation {
	return gu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (gu *GamesUpdate) Save(ctx context.Context) (int, error) {
	gu.defaults()
	return withHooks(ctx, gu.sqlSave, gu.mutation, gu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (gu *GamesUpdate) SaveX(ctx context.Context) int {
	affected, err := gu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (gu *GamesUpdate) Exec(ctx context.Context) error {
	_, err := gu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gu *GamesUpdate) ExecX(ctx context.Context) {
	if err := gu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gu *GamesUpdate) defaults() {
	if _, ok := gu.mutation.UpdatedAt(); !ok {
		v := games.UpdateDefaultUpdatedAt()
		gu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (gu *GamesUpdate) check() error {
	if v, ok := gu.mutation.Status(); ok {
		if err := games.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Games.status": %w`, err)}
		}
	}
	return nil
}

func (gu *GamesUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := gu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(games.Table, games.Columns, sqlgraph.NewFieldSpec(games.FieldID, field.TypeUint64))
	if ps := gu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := gu.mutation.TableID(); ok {
		_spec.SetField(games.FieldTableID, field.TypeUint64, value)
	}
	if value, ok := gu.mutation.AddedTableID(); ok {
		_spec.AddField(games.FieldTableID, field.TypeUint64, value)
	}
	if value, ok := gu.mutation.Status(); ok {
		_spec.SetField(games.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := gu.mutation.Seats(); ok {
		_spec.SetField(games.FieldSeats, field.TypeJSON, value)
	}
	if value, ok := gu.mutation.AppendedSeats(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldSeats, value)
		})
	}
	if value, ok := gu.mutation.Pot(); ok {
		_spec.SetField(games.FieldPot, field.TypeUint64, value)
	}
	if value, ok := gu.mutation.AddedPot(); ok {
		_spec.AddField(games.FieldPot, field.TypeUint64, value)
	}
	if value, ok := gu.mutation.Button(); ok {
		_spec.SetField(games.FieldButton, field.TypeInt, value)
	}
	if value, ok := gu.mutation.AddedButton(); ok {
		_spec.AddField(games.FieldButton, field.TypeInt, value)
	}
	if value, ok := gu.mutation.Sb(); ok {
		_spec.SetField(games.FieldSb, field.TypeInt, value)
	}
	if value, ok := gu.mutation.AddedSb(); ok {
		_spec.AddField(games.FieldSb, field.TypeInt, value)
	}
	if value, ok := gu.mutation.Bb(); ok {
		_spec.SetField(games.FieldBb, field.TypeInt, value)
	}
	if value, ok := gu.mutation.AddedBb(); ok {
		_spec.AddField(games.FieldBb, field.TypeInt, value)
	}
	if value, ok := gu.mutation.Stakes(); ok {
		_spec.SetField(games.FieldStakes, field.TypeJSON, value)
	}
	if value, ok := gu.mutation.AppendedStakes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldStakes, value)
		})
	}
	if value, ok := gu.mutation.Players(); ok {
		_spec.SetField(games.FieldPlayers, field.TypeJSON, value)
	}
	if value, ok := gu.mutation.AppendedPlayers(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldPlayers, value)
		})
	}
	if value, ok := gu.mutation.Winners(); ok {
		_spec.SetField(games.FieldWinners, field.TypeJSON, value)
	}
	if value, ok := gu.mutation.AppendedWinners(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldWinners, value)
		})
	}
	if value, ok := gu.mutation.Board(); ok {
		_spec.SetField(games.FieldBoard, field.TypeJSON, value)
	}
	if value, ok := gu.mutation.AppendedBoard(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldBoard, value)
		})
	}
	if value, ok := gu.mutation.Duration(); ok {
		_spec.SetField(games.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := gu.mutation.AddedDuration(); ok {
		_spec.AddField(games.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := gu.mutation.CompletedAt(); ok {
		_spec.SetField(games.FieldCompletedAt, field.TypeTime, value)
	}
	if gu.mutation.CompletedAtCleared() {
		_spec.ClearField(games.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := gu.mutation.Settled(); ok {
		_spec.SetField(games.FieldSettled, field.TypeBool, value)
	}
	if gu.mutation.CreatedByCleared() {
		_spec.ClearField(games.FieldCreatedBy, field.TypeString)
	}
	if gu.mutation.UpdatedByCleared() {
		_spec.ClearField(games.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := gu.mutation.UpdatedAt(); ok {
		_spec.SetField(games.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := gu.mutation.DeletedAt(); ok {
		_spec.SetField(games.FieldDeletedAt, field.TypeTime, value)
	}
	if gu.mutation.DeletedAtCleared() {
		_spec.ClearField(games.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = gu.schemaConfig.Games
	ctx = internal.NewSchemaConfigContext(ctx, gu.schemaConfig)
	if n, err = sqlgraph.UpdateNodes(ctx, gu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{games.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	gu.mutation.done = true
	return n, nil
}

// GamesUpdateOne is the builder for updating a single Games entity.
type GamesUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *GamesMutation
}

// SetTableID sets the "table_id" field.
func (guo *GamesUpdateOne) SetTableID(u uint64) *GamesUpdateOne {
	guo.mutation.ResetTableID()
	guo.mutation.SetTableID(u)
	return guo
}

// SetNillableTableID sets the "table_id" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableTableID(u *uint64) *GamesUpdateOne {
	if u != nil {
		guo.SetTableID(*u)
	}
	return guo
}

// AddTableID adds u to the "table_id" field.
func (guo *GamesUpdateOne) AddTableID(u int64) *GamesUpdateOne {
	guo.mutation.AddTableID(u)
	return guo
}

// SetStatus sets the "status" field.
func (guo *GamesUpdateOne) SetStatus(ga games.Status) *GamesUpdateOne {
	guo.mutation.SetStatus(ga)
	return guo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableStatus(ga *games.Status) *GamesUpdateOne {
	if ga != nil {
		guo.SetStatus(*ga)
	}
	return guo
}

// SetSeats sets the "seats" field.
func (guo *GamesUpdateOne) SetSeats(s []string) *GamesUpdateOne {
	guo.mutation.SetSeats(s)
	return guo
}

// AppendSeats appends s to the "seats" field.
func (guo *GamesUpdateOne) AppendSeats(s []string) *GamesUpdateOne {
	guo.mutation.AppendSeats(s)
	return guo
}

// SetPot sets the "pot" field.
func (guo *GamesUpdateOne) SetPot(u uint64) *GamesUpdateOne {
	guo.mutation.ResetPot()
	guo.mutation.SetPot(u)
	return guo
}

// SetNillablePot sets the "pot" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillablePot(u *uint64) *GamesUpdateOne {
	if u != nil {
		guo.SetPot(*u)
	}
	return guo
}

// AddPot adds u to the "pot" field.
func (guo *GamesUpdateOne) AddPot(u int64) *GamesUpdateOne {
	guo.mutation.AddPot(u)
	return guo
}

// SetButton sets the "button" field.
func (guo *GamesUpdateOne) SetButton(i int) *GamesUpdateOne {
	guo.mutation.ResetButton()
	guo.mutation.SetButton(i)
	return guo
}

// SetNillableButton sets the "button" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableButton(i *int) *GamesUpdateOne {
	if i != nil {
		guo.SetButton(*i)
	}
	return guo
}

// AddButton adds i to the "button" field.
func (guo *GamesUpdateOne) AddButton(i int) *GamesUpdateOne {
	guo.mutation.AddButton(i)
	return guo
}

// SetSb sets the "sb" field.
func (guo *GamesUpdateOne) SetSb(i int) *GamesUpdateOne {
	guo.mutation.ResetSb()
	guo.mutation.SetSb(i)
	return guo
}

// SetNillableSb sets the "sb" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableSb(i *int) *GamesUpdateOne {
	if i != nil {
		guo.SetSb(*i)
	}
	return guo
}

// AddSb adds i to the "sb" field.
func (guo *GamesUpdateOne) AddSb(i int) *GamesUpdateOne {
	guo.mutation.AddSb(i)
	return guo
}

// SetBb sets the "bb" field.
func (guo *GamesUpdateOne) SetBb(i int) *GamesUpdateOne {
	guo.mutation.ResetBb()
	guo.mutation.SetBb(i)
	return guo
}

// SetNillableBb sets the "bb" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableBb(i *int) *GamesUpdateOne {
	if i != nil {
		guo.SetBb(*i)
	}
	return guo
}

// AddBb adds i to the "bb" field.
func (guo *GamesUpdateOne) AddBb(i int) *GamesUpdateOne {
	guo.mutation.AddBb(i)
	return guo
}

// SetStakes sets the "stakes" field.
func (guo *GamesUpdateOne) SetStakes(u []uint64) *GamesUpdateOne {
	guo.mutation.SetStakes(u)
	return guo
}

// AppendStakes appends u to the "stakes" field.
func (guo *GamesUpdateOne) AppendStakes(u []uint64) *GamesUpdateOne {
	guo.mutation.AppendStakes(u)
	return guo
}

// SetPlayers sets the "players" field.
func (guo *GamesUpdateOne) SetPlayers(ms []model.PlayerState) *GamesUpdateOne {
	guo.mutation.SetPlayers(ms)
	return guo
}

// AppendPlayers appends ms to the "players" field.
func (guo *GamesUpdateOne) AppendPlayers(ms []model.PlayerState) *GamesUpdateOne {
	guo.mutation.AppendPlayers(ms)
	return guo
}

// SetWinners sets the "winners" field.
func (guo *GamesUpdateOne) SetWinners(ms []model.PlayerState) *GamesUpdateOne {
	guo.mutation.SetWinners(ms)
	return guo
}

// AppendWinners appends ms to the "winners" field.
func (guo *GamesUpdateOne) AppendWinners(ms []model.PlayerState) *GamesUpdateOne {
	guo.mutation.AppendWinners(ms)
	return guo
}

// SetBoard sets the "board" field.
func (guo *GamesUpdateOne) SetBoard(po poker.Cards) *GamesUpdateOne {
	guo.mutation.SetBoard(po)
	return guo
}

// AppendBoard appends po to the "board" field.
func (guo *GamesUpdateOne) AppendBoard(po poker.Cards) *GamesUpdateOne {
	guo.mutation.AppendBoard(po)
	return guo
}

// SetDuration sets the "duration" field.
func (guo *GamesUpdateOne) SetDuration(i int64) *GamesUpdateOne {
	guo.mutation.ResetDuration()
	guo.mutation.SetDuration(i)
	return guo
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableDuration(i *int64) *GamesUpdateOne {
	if i != nil {
		guo.SetDuration(*i)
	}
	return guo
}

// AddDuration adds i to the "duration" field.
func (guo *GamesUpdateOne) AddDuration(i int64) *GamesUpdateOne {
	guo.mutation.AddDuration(i)
	return guo
}

// SetCompletedAt sets the "completed_at" field.
func (guo *GamesUpdateOne) SetCompletedAt(t time.Time) *GamesUpdateOne {
	guo.mutation.SetCompletedAt(t)
	return guo
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableCompletedAt(t *time.Time) *GamesUpdateOne {
	if t != nil {
		guo.SetCompletedAt(*t)
	}
	return guo
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (guo *GamesUpdateOne) ClearCompletedAt() *GamesUpdateOne {
	guo.mutation.ClearCompletedAt()
	return guo
}

// SetSettled sets the "settled" field.
func (guo *GamesUpdateOne) SetSettled(b bool) *GamesUpdateOne {
	guo.mutation.SetSettled(b)
	return guo
}

// SetNillableSettled sets the "settled" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableSettled(b *bool) *GamesUpdateOne {
	if b != nil {
		guo.SetSettled(*b)
	}
	return guo
}

// SetUpdatedAt sets the "updated_at" field.
func (guo *GamesUpdateOne) SetUpdatedAt(t time.Time) *GamesUpdateOne {
	guo.mutation.SetUpdatedAt(t)
	return guo
}

// SetDeletedAt sets the "deleted_at" field.
func (guo *GamesUpdateOne) SetDeletedAt(t time.Time) *GamesUpdateOne {
	guo.mutation.SetDeletedAt(t)
	return guo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (guo *GamesUpdateOne) SetNillableDeletedAt(t *time.Time) *GamesUpdateOne {
	if t != nil {
		guo.SetDeletedAt(*t)
	}
	return guo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (guo *GamesUpdateOne) ClearDeletedAt() *GamesUpdateOne {
	guo.mutation.ClearDeletedAt()
	return guo
}

// Mutation returns the GamesMutation object of the builder.
func (guo *GamesUpdateOne) Mutation() *GamesMutation {
	return guo.mutation
}

// Where appends a list predicates to the GamesUpdate builder.
func (guo *GamesUpdateOne) Where(ps ...predicate.Games) *GamesUpdateOne {
	guo.mutation.Where(ps...)
	return guo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (guo *GamesUpdateOne) Select(field string, fields ...string) *GamesUpdateOne {
	guo.fields = append([]string{field}, fields...)
	return guo
}

// Save executes the query and returns the updated Games entity.
func (guo *GamesUpdateOne) Save(ctx context.Context) (*Games, error) {
	guo.defaults()
	return withHooks(ctx, guo.sqlSave, guo.mutation, guo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (guo *GamesUpdateOne) SaveX(ctx context.Context) *Games {
	node, err := guo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (guo *GamesUpdateOne) Exec(ctx context.Context) error {
	_, err := guo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (guo *GamesUpdateOne) ExecX(ctx context.Context) {
	if err := guo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (guo *GamesUpdateOne) defaults() {
	if _, ok := guo.mutation.UpdatedAt(); !ok {
		v := games.UpdateDefaultUpdatedAt()
		guo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (guo *GamesUpdateOne) check() error {
	if v, ok := guo.mutation.Status(); ok {
		if err := games.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Games.status": %w`, err)}
		}
	}
	return nil
}

func (guo *GamesUpdateOne) sqlSave(ctx context.Context) (_node *Games, err error) {
	if err := guo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(games.Table, games.Columns, sqlgraph.NewFieldSpec(games.FieldID, field.TypeUint64))
	id, ok := guo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Games.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := guo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, games.FieldID)
		for _, f := range fields {
			if !games.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != games.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := guo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := guo.mutation.TableID(); ok {
		_spec.SetField(games.FieldTableID, field.TypeUint64, value)
	}
	if value, ok := guo.mutation.AddedTableID(); ok {
		_spec.AddField(games.FieldTableID, field.TypeUint64, value)
	}
	if value, ok := guo.mutation.Status(); ok {
		_spec.SetField(games.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := guo.mutation.Seats(); ok {
		_spec.SetField(games.FieldSeats, field.TypeJSON, value)
	}
	if value, ok := guo.mutation.AppendedSeats(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldSeats, value)
		})
	}
	if value, ok := guo.mutation.Pot(); ok {
		_spec.SetField(games.FieldPot, field.TypeUint64, value)
	}
	if value, ok := guo.mutation.AddedPot(); ok {
		_spec.AddField(games.FieldPot, field.TypeUint64, value)
	}
	if value, ok := guo.mutation.Button(); ok {
		_spec.SetField(games.FieldButton, field.TypeInt, value)
	}
	if value, ok := guo.mutation.AddedButton(); ok {
		_spec.AddField(games.FieldButton, field.TypeInt, value)
	}
	if value, ok := guo.mutation.Sb(); ok {
		_spec.SetField(games.FieldSb, field.TypeInt, value)
	}
	if value, ok := guo.mutation.AddedSb(); ok {
		_spec.AddField(games.FieldSb, field.TypeInt, value)
	}
	if value, ok := guo.mutation.Bb(); ok {
		_spec.SetField(games.FieldBb, field.TypeInt, value)
	}
	if value, ok := guo.mutation.AddedBb(); ok {
		_spec.AddField(games.FieldBb, field.TypeInt, value)
	}
	if value, ok := guo.mutation.Stakes(); ok {
		_spec.SetField(games.FieldStakes, field.TypeJSON, value)
	}
	if value, ok := guo.mutation.AppendedStakes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldStakes, value)
		})
	}
	if value, ok := guo.mutation.Players(); ok {
		_spec.SetField(games.FieldPlayers, field.TypeJSON, value)
	}
	if value, ok := guo.mutation.AppendedPlayers(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldPlayers, value)
		})
	}
	if value, ok := guo.mutation.Winners(); ok {
		_spec.SetField(games.FieldWinners, field.TypeJSON, value)
	}
	if value, ok := guo.mutation.AppendedWinners(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldWinners, value)
		})
	}
	if value, ok := guo.mutation.Board(); ok {
		_spec.SetField(games.FieldBoard, field.TypeJSON, value)
	}
	if value, ok := guo.mutation.AppendedBoard(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, games.FieldBoard, value)
		})
	}
	if value, ok := guo.mutation.Duration(); ok {
		_spec.SetField(games.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := guo.mutation.AddedDuration(); ok {
		_spec.AddField(games.FieldDuration, field.TypeInt64, value)
	}
	if value, ok := guo.mutation.CompletedAt(); ok {
		_spec.SetField(games.FieldCompletedAt, field.TypeTime, value)
	}
	if guo.mutation.CompletedAtCleared() {
		_spec.ClearField(games.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := guo.mutation.Settled(); ok {
		_spec.SetField(games.FieldSettled, field.TypeBool, value)
	}
	if guo.mutation.CreatedByCleared() {
		_spec.ClearField(games.FieldCreatedBy, field.TypeString)
	}
	if guo.mutation.UpdatedByCleared() {
		_spec.ClearField(games.FieldUpdatedBy, field.TypeString)
	}
	if value, ok := guo.mutation.UpdatedAt(); ok {
		_spec.SetField(games.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := guo.mutation.DeletedAt(); ok {
		_spec.SetField(games.FieldDeletedAt, field.TypeTime, value)
	}
	if guo.mutation.DeletedAtCleared() {
		_spec.ClearField(games.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = guo.schemaConfig.Games
	ctx = internal.NewSchemaConfigContext(ctx, guo.schemaConfig)
	_node = &Games{config: guo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, guo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{games.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	guo.mutation.done = true
	return _node, nil
}
