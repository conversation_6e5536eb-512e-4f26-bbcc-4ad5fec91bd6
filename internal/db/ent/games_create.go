// Copyright 2019-present Facebook
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by entc, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"hamster/internal/db/ent/games"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GamesCreate is the builder for creating a Games entity.
type GamesCreate struct {
	config
	mutation *GamesMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTableID sets the "table_id" field.
func (gc *GamesCreate) SetTableID(u uint64) *GamesCreate {
	gc.mutation.SetTableID(u)
	return gc
}

// SetStatus sets the "status" field.
func (gc *GamesCreate) SetStatus(ga games.Status) *GamesCreate {
	gc.mutation.SetStatus(ga)
	return gc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gc *GamesCreate) SetNillableStatus(ga *games.Status) *GamesCreate {
	if ga != nil {
		gc.SetStatus(*ga)
	}
	return gc
}

// SetSeats sets the "seats" field.
func (gc *GamesCreate) SetSeats(s []string) *GamesCreate {
	gc.mutation.SetSeats(s)
	return gc
}

// SetPot sets the "pot" field.
func (gc *GamesCreate) SetPot(u uint64) *GamesCreate {
	gc.mutation.SetPot(u)
	return gc
}

// SetButton sets the "button" field.
func (gc *GamesCreate) SetButton(i int) *GamesCreate {
	gc.mutation.SetButton(i)
	return gc
}

// SetSb sets the "sb" field.
func (gc *GamesCreate) SetSb(i int) *GamesCreate {
	gc.mutation.SetSb(i)
	return gc
}

// SetBb sets the "bb" field.
func (gc *GamesCreate) SetBb(i int) *GamesCreate {
	gc.mutation.SetBb(i)
	return gc
}

// SetStakes sets the "stakes" field.
func (gc *GamesCreate) SetStakes(u []uint64) *GamesCreate {
	gc.mutation.SetStakes(u)
	return gc
}

// SetPlayers sets the "players" field.
func (gc *GamesCreate) SetPlayers(ms []model.PlayerState) *GamesCreate {
	gc.mutation.SetPlayers(ms)
	return gc
}

// SetWinners sets the "winners" field.
func (gc *GamesCreate) SetWinners(ms []model.PlayerState) *GamesCreate {
	gc.mutation.SetWinners(ms)
	return gc
}

// SetBoard sets the "board" field.
func (gc *GamesCreate) SetBoard(po poker.Cards) *GamesCreate {
	gc.mutation.SetBoard(po)
	return gc
}

// SetDuration sets the "duration" field.
func (gc *GamesCreate) SetDuration(i int64) *GamesCreate {
	gc.mutation.SetDuration(i)
	return gc
}

// SetStartedAt sets the "started_at" field.
func (gc *GamesCreate) SetStartedAt(t time.Time) *GamesCreate {
	gc.mutation.SetStartedAt(t)
	return gc
}

// SetNillableStartedAt sets the "started_at" field if the given value is not nil.
func (gc *GamesCreate) SetNillableStartedAt(t *time.Time) *GamesCreate {
	if t != nil {
		gc.SetStartedAt(*t)
	}
	return gc
}

// SetCompletedAt sets the "completed_at" field.
func (gc *GamesCreate) SetCompletedAt(t time.Time) *GamesCreate {
	gc.mutation.SetCompletedAt(t)
	return gc
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (gc *GamesCreate) SetNillableCompletedAt(t *time.Time) *GamesCreate {
	if t != nil {
		gc.SetCompletedAt(*t)
	}
	return gc
}

// SetSettled sets the "settled" field.
func (gc *GamesCreate) SetSettled(b bool) *GamesCreate {
	gc.mutation.SetSettled(b)
	return gc
}

// SetNillableSettled sets the "settled" field if the given value is not nil.
func (gc *GamesCreate) SetNillableSettled(b *bool) *GamesCreate {
	if b != nil {
		gc.SetSettled(*b)
	}
	return gc
}

// SetCreatedAt sets the "created_at" field.
func (gc *GamesCreate) SetCreatedAt(t time.Time) *GamesCreate {
	gc.mutation.SetCreatedAt(t)
	return gc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (gc *GamesCreate) SetNillableCreatedAt(t *time.Time) *GamesCreate {
	if t != nil {
		gc.SetCreatedAt(*t)
	}
	return gc
}

// SetCreatedBy sets the "created_by" field.
func (gc *GamesCreate) SetCreatedBy(s string) *GamesCreate {
	gc.mutation.SetCreatedBy(s)
	return gc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (gc *GamesCreate) SetNillableCreatedBy(s *string) *GamesCreate {
	if s != nil {
		gc.SetCreatedBy(*s)
	}
	return gc
}

// SetUpdatedBy sets the "updated_by" field.
func (gc *GamesCreate) SetUpdatedBy(s string) *GamesCreate {
	gc.mutation.SetUpdatedBy(s)
	return gc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (gc *GamesCreate) SetNillableUpdatedBy(s *string) *GamesCreate {
	if s != nil {
		gc.SetUpdatedBy(*s)
	}
	return gc
}

// SetUpdatedAt sets the "updated_at" field.
func (gc *GamesCreate) SetUpdatedAt(t time.Time) *GamesCreate {
	gc.mutation.SetUpdatedAt(t)
	return gc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (gc *GamesCreate) SetNillableUpdatedAt(t *time.Time) *GamesCreate {
	if t != nil {
		gc.SetUpdatedAt(*t)
	}
	return gc
}

// SetDeletedAt sets the "deleted_at" field.
func (gc *GamesCreate) SetDeletedAt(t time.Time) *GamesCreate {
	gc.mutation.SetDeletedAt(t)
	return gc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gc *GamesCreate) SetNillableDeletedAt(t *time.Time) *GamesCreate {
	if t != nil {
		gc.SetDeletedAt(*t)
	}
	return gc
}

// SetID sets the "id" field.
func (gc *GamesCreate) SetID(u uint64) *GamesCreate {
	gc.mutation.SetID(u)
	return gc
}

// Mutation returns the GamesMutation object of the builder.
func (gc *GamesCreate) Mutation() *GamesMutation {
	return gc.mutation
}

// Save creates the Games in the database.
func (gc *GamesCreate) Save(ctx context.Context) (*Games, error) {
	gc.defaults()
	return withHooks(ctx, gc.sqlSave, gc.mutation, gc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (gc *GamesCreate) SaveX(ctx context.Context) *Games {
	v, err := gc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gc *GamesCreate) Exec(ctx context.Context) error {
	_, err := gc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gc *GamesCreate) ExecX(ctx context.Context) {
	if err := gc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gc *GamesCreate) defaults() {
	if _, ok := gc.mutation.Status(); !ok {
		v := games.DefaultStatus
		gc.mutation.SetStatus(v)
	}
	if _, ok := gc.mutation.StartedAt(); !ok {
		v := games.DefaultStartedAt()
		gc.mutation.SetStartedAt(v)
	}
	if _, ok := gc.mutation.Settled(); !ok {
		v := games.DefaultSettled
		gc.mutation.SetSettled(v)
	}
	if _, ok := gc.mutation.CreatedAt(); !ok {
		v := games.DefaultCreatedAt()
		gc.mutation.SetCreatedAt(v)
	}
	if _, ok := gc.mutation.UpdatedAt(); !ok {
		v := games.DefaultUpdatedAt()
		gc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (gc *GamesCreate) check() error {
	if _, ok := gc.mutation.TableID(); !ok {
		return &ValidationError{Name: "table_id", err: errors.New(`ent: missing required field "Games.table_id"`)}
	}
	if _, ok := gc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Games.status"`)}
	}
	if v, ok := gc.mutation.Status(); ok {
		if err := games.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Games.status": %w`, err)}
		}
	}
	if _, ok := gc.mutation.Seats(); !ok {
		return &ValidationError{Name: "seats", err: errors.New(`ent: missing required field "Games.seats"`)}
	}
	if _, ok := gc.mutation.Pot(); !ok {
		return &ValidationError{Name: "pot", err: errors.New(`ent: missing required field "Games.pot"`)}
	}
	if _, ok := gc.mutation.Button(); !ok {
		return &ValidationError{Name: "button", err: errors.New(`ent: missing required field "Games.button"`)}
	}
	if _, ok := gc.mutation.Sb(); !ok {
		return &ValidationError{Name: "sb", err: errors.New(`ent: missing required field "Games.sb"`)}
	}
	if _, ok := gc.mutation.Bb(); !ok {
		return &ValidationError{Name: "bb", err: errors.New(`ent: missing required field "Games.bb"`)}
	}
	if _, ok := gc.mutation.Stakes(); !ok {
		return &ValidationError{Name: "stakes", err: errors.New(`ent: missing required field "Games.stakes"`)}
	}
	if _, ok := gc.mutation.Players(); !ok {
		return &ValidationError{Name: "players", err: errors.New(`ent: missing required field "Games.players"`)}
	}
	if _, ok := gc.mutation.Winners(); !ok {
		return &ValidationError{Name: "winners", err: errors.New(`ent: missing required field "Games.winners"`)}
	}
	if _, ok := gc.mutation.Board(); !ok {
		return &ValidationError{Name: "board", err: errors.New(`ent: missing required field "Games.board"`)}
	}
	if _, ok := gc.mutation.Duration(); !ok {
		return &ValidationError{Name: "duration", err: errors.New(`ent: missing required field "Games.duration"`)}
	}
	if _, ok := gc.mutation.StartedAt(); !ok {
		return &ValidationError{Name: "started_at", err: errors.New(`ent: missing required field "Games.started_at"`)}
	}
	if _, ok := gc.mutation.Settled(); !ok {
		return &ValidationError{Name: "settled", err: errors.New(`ent: missing required field "Games.settled"`)}
	}
	if _, ok := gc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Games.created_at"`)}
	}
	if _, ok := gc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Games.updated_at"`)}
	}
	return nil
}

func (gc *GamesCreate) sqlSave(ctx context.Context) (*Games, error) {
	if err := gc.check(); err != nil {
		return nil, err
	}
	_node, _spec := gc.createSpec()
	if err := sqlgraph.CreateNode(ctx, gc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint64(id)
	}
	gc.mutation.id = &_node.ID
	gc.mutation.done = true
	return _node, nil
}

func (gc *GamesCreate) createSpec() (*Games, *sqlgraph.CreateSpec) {
	var (
		_node = &Games{config: gc.config}
		_spec = sqlgraph.NewCreateSpec(games.Table, sqlgraph.NewFieldSpec(games.FieldID, field.TypeUint64))
	)
	_spec.Schema = gc.schemaConfig.Games
	_spec.OnConflict = gc.conflict
	if id, ok := gc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := gc.mutation.TableID(); ok {
		_spec.SetField(games.FieldTableID, field.TypeUint64, value)
		_node.TableID = value
	}
	if value, ok := gc.mutation.Status(); ok {
		_spec.SetField(games.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := gc.mutation.Seats(); ok {
		_spec.SetField(games.FieldSeats, field.TypeJSON, value)
		_node.Seats = value
	}
	if value, ok := gc.mutation.Pot(); ok {
		_spec.SetField(games.FieldPot, field.TypeUint64, value)
		_node.Pot = value
	}
	if value, ok := gc.mutation.Button(); ok {
		_spec.SetField(games.FieldButton, field.TypeInt, value)
		_node.Button = value
	}
	if value, ok := gc.mutation.Sb(); ok {
		_spec.SetField(games.FieldSb, field.TypeInt, value)
		_node.Sb = value
	}
	if value, ok := gc.mutation.Bb(); ok {
		_spec.SetField(games.FieldBb, field.TypeInt, value)
		_node.Bb = value
	}
	if value, ok := gc.mutation.Stakes(); ok {
		_spec.SetField(games.FieldStakes, field.TypeJSON, value)
		_node.Stakes = value
	}
	if value, ok := gc.mutation.Players(); ok {
		_spec.SetField(games.FieldPlayers, field.TypeJSON, value)
		_node.Players = value
	}
	if value, ok := gc.mutation.Winners(); ok {
		_spec.SetField(games.FieldWinners, field.TypeJSON, value)
		_node.Winners = value
	}
	if value, ok := gc.mutation.Board(); ok {
		_spec.SetField(games.FieldBoard, field.TypeJSON, value)
		_node.Board = value
	}
	if value, ok := gc.mutation.Duration(); ok {
		_spec.SetField(games.FieldDuration, field.TypeInt64, value)
		_node.Duration = value
	}
	if value, ok := gc.mutation.StartedAt(); ok {
		_spec.SetField(games.FieldStartedAt, field.TypeTime, value)
		_node.StartedAt = value
	}
	if value, ok := gc.mutation.CompletedAt(); ok {
		_spec.SetField(games.FieldCompletedAt, field.TypeTime, value)
		_node.CompletedAt = &value
	}
	if value, ok := gc.mutation.Settled(); ok {
		_spec.SetField(games.FieldSettled, field.TypeBool, value)
		_node.Settled = value
	}
	if value, ok := gc.mutation.CreatedAt(); ok {
		_spec.SetField(games.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := gc.mutation.CreatedBy(); ok {
		_spec.SetField(games.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := gc.mutation.UpdatedBy(); ok {
		_spec.SetField(games.FieldUpdatedBy, field.TypeString, value)
		_node.UpdatedBy = value
	}
	if value, ok := gc.mutation.UpdatedAt(); ok {
		_spec.SetField(games.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := gc.mutation.DeletedAt(); ok {
		_spec.SetField(games.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Games.Create().
//		SetTableID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GamesUpsert) {
//			SetTableID(v+v).
//		}).
//		Exec(ctx)
func (gc *GamesCreate) OnConflict(opts ...sql.ConflictOption) *GamesUpsertOne {
	gc.conflict = opts
	return &GamesUpsertOne{
		create: gc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Games.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gc *GamesCreate) OnConflictColumns(columns ...string) *GamesUpsertOne {
	gc.conflict = append(gc.conflict, sql.ConflictColumns(columns...))
	return &GamesUpsertOne{
		create: gc,
	}
}

type (
	// GamesUpsertOne is the builder for "upsert"-ing
	//  one Games node.
	GamesUpsertOne struct {
		create *GamesCreate
	}

	// GamesUpsert is the "OnConflict" setter.
	GamesUpsert struct {
		*sql.UpdateSet
	}
)

// SetTableID sets the "table_id" field.
func (u *GamesUpsert) SetTableID(v uint64) *GamesUpsert {
	u.Set(games.FieldTableID, v)
	return u
}

// UpdateTableID sets the "table_id" field to the value that was provided on create.
func (u *GamesUpsert) UpdateTableID() *GamesUpsert {
	u.SetExcluded(games.FieldTableID)
	return u
}

// AddTableID adds v to the "table_id" field.
func (u *GamesUpsert) AddTableID(v uint64) *GamesUpsert {
	u.Add(games.FieldTableID, v)
	return u
}

// SetStatus sets the "status" field.
func (u *GamesUpsert) SetStatus(v games.Status) *GamesUpsert {
	u.Set(games.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GamesUpsert) UpdateStatus() *GamesUpsert {
	u.SetExcluded(games.FieldStatus)
	return u
}

// SetSeats sets the "seats" field.
func (u *GamesUpsert) SetSeats(v []string) *GamesUpsert {
	u.Set(games.FieldSeats, v)
	return u
}

// UpdateSeats sets the "seats" field to the value that was provided on create.
func (u *GamesUpsert) UpdateSeats() *GamesUpsert {
	u.SetExcluded(games.FieldSeats)
	return u
}

// SetPot sets the "pot" field.
func (u *GamesUpsert) SetPot(v uint64) *GamesUpsert {
	u.Set(games.FieldPot, v)
	return u
}

// UpdatePot sets the "pot" field to the value that was provided on create.
func (u *GamesUpsert) UpdatePot() *GamesUpsert {
	u.SetExcluded(games.FieldPot)
	return u
}

// AddPot adds v to the "pot" field.
func (u *GamesUpsert) AddPot(v uint64) *GamesUpsert {
	u.Add(games.FieldPot, v)
	return u
}

// SetButton sets the "button" field.
func (u *GamesUpsert) SetButton(v int) *GamesUpsert {
	u.Set(games.FieldButton, v)
	return u
}

// UpdateButton sets the "button" field to the value that was provided on create.
func (u *GamesUpsert) UpdateButton() *GamesUpsert {
	u.SetExcluded(games.FieldButton)
	return u
}

// AddButton adds v to the "button" field.
func (u *GamesUpsert) AddButton(v int) *GamesUpsert {
	u.Add(games.FieldButton, v)
	return u
}

// SetSb sets the "sb" field.
func (u *GamesUpsert) SetSb(v int) *GamesUpsert {
	u.Set(games.FieldSb, v)
	return u
}

// UpdateSb sets the "sb" field to the value that was provided on create.
func (u *GamesUpsert) UpdateSb() *GamesUpsert {
	u.SetExcluded(games.FieldSb)
	return u
}

// AddSb adds v to the "sb" field.
func (u *GamesUpsert) AddSb(v int) *GamesUpsert {
	u.Add(games.FieldSb, v)
	return u
}

// SetBb sets the "bb" field.
func (u *GamesUpsert) SetBb(v int) *GamesUpsert {
	u.Set(games.FieldBb, v)
	return u
}

// UpdateBb sets the "bb" field to the value that was provided on create.
func (u *GamesUpsert) UpdateBb() *GamesUpsert {
	u.SetExcluded(games.FieldBb)
	return u
}

// AddBb adds v to the "bb" field.
func (u *GamesUpsert) AddBb(v int) *GamesUpsert {
	u.Add(games.FieldBb, v)
	return u
}

// SetStakes sets the "stakes" field.
func (u *GamesUpsert) SetStakes(v []uint64) *GamesUpsert {
	u.Set(games.FieldStakes, v)
	return u
}

// UpdateStakes sets the "stakes" field to the value that was provided on create.
func (u *GamesUpsert) UpdateStakes() *GamesUpsert {
	u.SetExcluded(games.FieldStakes)
	return u
}

// SetPlayers sets the "players" field.
func (u *GamesUpsert) SetPlayers(v []model.PlayerState) *GamesUpsert {
	u.Set(games.FieldPlayers, v)
	return u
}

// UpdatePlayers sets the "players" field to the value that was provided on create.
func (u *GamesUpsert) UpdatePlayers() *GamesUpsert {
	u.SetExcluded(games.FieldPlayers)
	return u
}

// SetWinners sets the "winners" field.
func (u *GamesUpsert) SetWinners(v []model.PlayerState) *GamesUpsert {
	u.Set(games.FieldWinners, v)
	return u
}

// UpdateWinners sets the "winners" field to the value that was provided on create.
func (u *GamesUpsert) UpdateWinners() *GamesUpsert {
	u.SetExcluded(games.FieldWinners)
	return u
}

// SetBoard sets the "board" field.
func (u *GamesUpsert) SetBoard(v poker.Cards) *GamesUpsert {
	u.Set(games.FieldBoard, v)
	return u
}

// UpdateBoard sets the "board" field to the value that was provided on create.
func (u *GamesUpsert) UpdateBoard() *GamesUpsert {
	u.SetExcluded(games.FieldBoard)
	return u
}

// SetDuration sets the "duration" field.
func (u *GamesUpsert) SetDuration(v int64) *GamesUpsert {
	u.Set(games.FieldDuration, v)
	return u
}

// UpdateDuration sets the "duration" field to the value that was provided on create.
func (u *GamesUpsert) UpdateDuration() *GamesUpsert {
	u.SetExcluded(games.FieldDuration)
	return u
}

// AddDuration adds v to the "duration" field.
func (u *GamesUpsert) AddDuration(v int64) *GamesUpsert {
	u.Add(games.FieldDuration, v)
	return u
}

// SetCompletedAt sets the "completed_at" field.
func (u *GamesUpsert) SetCompletedAt(v time.Time) *GamesUpsert {
	u.Set(games.FieldCompletedAt, v)
	return u
}

// UpdateCompletedAt sets the "completed_at" field to the value that was provided on create.
func (u *GamesUpsert) UpdateCompletedAt() *GamesUpsert {
	u.SetExcluded(games.FieldCompletedAt)
	return u
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (u *GamesUpsert) ClearCompletedAt() *GamesUpsert {
	u.SetNull(games.FieldCompletedAt)
	return u
}

// SetSettled sets the "settled" field.
func (u *GamesUpsert) SetSettled(v bool) *GamesUpsert {
	u.Set(games.FieldSettled, v)
	return u
}

// UpdateSettled sets the "settled" field to the value that was provided on create.
func (u *GamesUpsert) UpdateSettled() *GamesUpsert {
	u.SetExcluded(games.FieldSettled)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GamesUpsert) SetUpdatedAt(v time.Time) *GamesUpsert {
	u.Set(games.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GamesUpsert) UpdateUpdatedAt() *GamesUpsert {
	u.SetExcluded(games.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GamesUpsert) SetDeletedAt(v time.Time) *GamesUpsert {
	u.Set(games.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GamesUpsert) UpdateDeletedAt() *GamesUpsert {
	u.SetExcluded(games.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GamesUpsert) ClearDeletedAt() *GamesUpsert {
	u.SetNull(games.FieldDeletedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Games.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(games.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GamesUpsertOne) UpdateNewValues() *GamesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(games.FieldID)
		}
		if _, exists := u.create.mutation.StartedAt(); exists {
			s.SetIgnore(games.FieldStartedAt)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(games.FieldCreatedAt)
		}
		if _, exists := u.create.mutation.CreatedBy(); exists {
			s.SetIgnore(games.FieldCreatedBy)
		}
		if _, exists := u.create.mutation.UpdatedBy(); exists {
			s.SetIgnore(games.FieldUpdatedBy)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Games.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *GamesUpsertOne) Ignore() *GamesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GamesUpsertOne) DoNothing() *GamesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GamesCreate.OnConflict
// documentation for more info.
func (u *GamesUpsertOne) Update(set func(*GamesUpsert)) *GamesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GamesUpsert{UpdateSet: update})
	}))
	return u
}

// SetTableID sets the "table_id" field.
func (u *GamesUpsertOne) SetTableID(v uint64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetTableID(v)
	})
}

// AddTableID adds v to the "table_id" field.
func (u *GamesUpsertOne) AddTableID(v uint64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddTableID(v)
	})
}

// UpdateTableID sets the "table_id" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateTableID() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateTableID()
	})
}

// SetStatus sets the "status" field.
func (u *GamesUpsertOne) SetStatus(v games.Status) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateStatus() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateStatus()
	})
}

// SetSeats sets the "seats" field.
func (u *GamesUpsertOne) SetSeats(v []string) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetSeats(v)
	})
}

// UpdateSeats sets the "seats" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateSeats() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSeats()
	})
}

// SetPot sets the "pot" field.
func (u *GamesUpsertOne) SetPot(v uint64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetPot(v)
	})
}

// AddPot adds v to the "pot" field.
func (u *GamesUpsertOne) AddPot(v uint64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddPot(v)
	})
}

// UpdatePot sets the "pot" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdatePot() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdatePot()
	})
}

// SetButton sets the "button" field.
func (u *GamesUpsertOne) SetButton(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetButton(v)
	})
}

// AddButton adds v to the "button" field.
func (u *GamesUpsertOne) AddButton(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddButton(v)
	})
}

// UpdateButton sets the "button" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateButton() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateButton()
	})
}

// SetSb sets the "sb" field.
func (u *GamesUpsertOne) SetSb(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetSb(v)
	})
}

// AddSb adds v to the "sb" field.
func (u *GamesUpsertOne) AddSb(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddSb(v)
	})
}

// UpdateSb sets the "sb" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateSb() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSb()
	})
}

// SetBb sets the "bb" field.
func (u *GamesUpsertOne) SetBb(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetBb(v)
	})
}

// AddBb adds v to the "bb" field.
func (u *GamesUpsertOne) AddBb(v int) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddBb(v)
	})
}

// UpdateBb sets the "bb" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateBb() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateBb()
	})
}

// SetStakes sets the "stakes" field.
func (u *GamesUpsertOne) SetStakes(v []uint64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetStakes(v)
	})
}

// UpdateStakes sets the "stakes" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateStakes() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateStakes()
	})
}

// SetPlayers sets the "players" field.
func (u *GamesUpsertOne) SetPlayers(v []model.PlayerState) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetPlayers(v)
	})
}

// UpdatePlayers sets the "players" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdatePlayers() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdatePlayers()
	})
}

// SetWinners sets the "winners" field.
func (u *GamesUpsertOne) SetWinners(v []model.PlayerState) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetWinners(v)
	})
}

// UpdateWinners sets the "winners" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateWinners() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateWinners()
	})
}

// SetBoard sets the "board" field.
func (u *GamesUpsertOne) SetBoard(v poker.Cards) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetBoard(v)
	})
}

// UpdateBoard sets the "board" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateBoard() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateBoard()
	})
}

// SetDuration sets the "duration" field.
func (u *GamesUpsertOne) SetDuration(v int64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetDuration(v)
	})
}

// AddDuration adds v to the "duration" field.
func (u *GamesUpsertOne) AddDuration(v int64) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.AddDuration(v)
	})
}

// UpdateDuration sets the "duration" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateDuration() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateDuration()
	})
}

// SetCompletedAt sets the "completed_at" field.
func (u *GamesUpsertOne) SetCompletedAt(v time.Time) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetCompletedAt(v)
	})
}

// UpdateCompletedAt sets the "completed_at" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateCompletedAt() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateCompletedAt()
	})
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (u *GamesUpsertOne) ClearCompletedAt() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.ClearCompletedAt()
	})
}

// SetSettled sets the "settled" field.
func (u *GamesUpsertOne) SetSettled(v bool) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetSettled(v)
	})
}

// UpdateSettled sets the "settled" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateSettled() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSettled()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GamesUpsertOne) SetUpdatedAt(v time.Time) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateUpdatedAt() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GamesUpsertOne) SetDeletedAt(v time.Time) *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GamesUpsertOne) UpdateDeletedAt() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GamesUpsertOne) ClearDeletedAt() *GamesUpsertOne {
	return u.Update(func(s *GamesUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *GamesUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GamesCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GamesUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *GamesUpsertOne) ID(ctx context.Context) (id uint64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *GamesUpsertOne) IDX(ctx context.Context) uint64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// GamesCreateBulk is the builder for creating many Games entities in bulk.
type GamesCreateBulk struct {
	config
	err      error
	builders []*GamesCreate
	conflict []sql.ConflictOption
}

// Save creates the Games entities in the database.
func (gcb *GamesCreateBulk) Save(ctx context.Context) ([]*Games, error) {
	if gcb.err != nil {
		return nil, gcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(gcb.builders))
	nodes := make([]*Games, len(gcb.builders))
	mutators := make([]Mutator, len(gcb.builders))
	for i := range gcb.builders {
		func(i int, root context.Context) {
			builder := gcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*GamesMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, gcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = gcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, gcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, gcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (gcb *GamesCreateBulk) SaveX(ctx context.Context) []*Games {
	v, err := gcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gcb *GamesCreateBulk) Exec(ctx context.Context) error {
	_, err := gcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gcb *GamesCreateBulk) ExecX(ctx context.Context) {
	if err := gcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Games.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GamesUpsert) {
//			SetTableID(v+v).
//		}).
//		Exec(ctx)
func (gcb *GamesCreateBulk) OnConflict(opts ...sql.ConflictOption) *GamesUpsertBulk {
	gcb.conflict = opts
	return &GamesUpsertBulk{
		create: gcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Games.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gcb *GamesCreateBulk) OnConflictColumns(columns ...string) *GamesUpsertBulk {
	gcb.conflict = append(gcb.conflict, sql.ConflictColumns(columns...))
	return &GamesUpsertBulk{
		create: gcb,
	}
}

// GamesUpsertBulk is the builder for "upsert"-ing
// a bulk of Games nodes.
type GamesUpsertBulk struct {
	create *GamesCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Games.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(games.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GamesUpsertBulk) UpdateNewValues() *GamesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(games.FieldID)
			}
			if _, exists := b.mutation.StartedAt(); exists {
				s.SetIgnore(games.FieldStartedAt)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(games.FieldCreatedAt)
			}
			if _, exists := b.mutation.CreatedBy(); exists {
				s.SetIgnore(games.FieldCreatedBy)
			}
			if _, exists := b.mutation.UpdatedBy(); exists {
				s.SetIgnore(games.FieldUpdatedBy)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Games.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *GamesUpsertBulk) Ignore() *GamesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GamesUpsertBulk) DoNothing() *GamesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GamesCreateBulk.OnConflict
// documentation for more info.
func (u *GamesUpsertBulk) Update(set func(*GamesUpsert)) *GamesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GamesUpsert{UpdateSet: update})
	}))
	return u
}

// SetTableID sets the "table_id" field.
func (u *GamesUpsertBulk) SetTableID(v uint64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetTableID(v)
	})
}

// AddTableID adds v to the "table_id" field.
func (u *GamesUpsertBulk) AddTableID(v uint64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddTableID(v)
	})
}

// UpdateTableID sets the "table_id" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateTableID() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateTableID()
	})
}

// SetStatus sets the "status" field.
func (u *GamesUpsertBulk) SetStatus(v games.Status) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateStatus() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateStatus()
	})
}

// SetSeats sets the "seats" field.
func (u *GamesUpsertBulk) SetSeats(v []string) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetSeats(v)
	})
}

// UpdateSeats sets the "seats" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateSeats() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSeats()
	})
}

// SetPot sets the "pot" field.
func (u *GamesUpsertBulk) SetPot(v uint64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetPot(v)
	})
}

// AddPot adds v to the "pot" field.
func (u *GamesUpsertBulk) AddPot(v uint64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddPot(v)
	})
}

// UpdatePot sets the "pot" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdatePot() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdatePot()
	})
}

// SetButton sets the "button" field.
func (u *GamesUpsertBulk) SetButton(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetButton(v)
	})
}

// AddButton adds v to the "button" field.
func (u *GamesUpsertBulk) AddButton(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddButton(v)
	})
}

// UpdateButton sets the "button" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateButton() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateButton()
	})
}

// SetSb sets the "sb" field.
func (u *GamesUpsertBulk) SetSb(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetSb(v)
	})
}

// AddSb adds v to the "sb" field.
func (u *GamesUpsertBulk) AddSb(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddSb(v)
	})
}

// UpdateSb sets the "sb" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateSb() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSb()
	})
}

// SetBb sets the "bb" field.
func (u *GamesUpsertBulk) SetBb(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetBb(v)
	})
}

// AddBb adds v to the "bb" field.
func (u *GamesUpsertBulk) AddBb(v int) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddBb(v)
	})
}

// UpdateBb sets the "bb" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateBb() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateBb()
	})
}

// SetStakes sets the "stakes" field.
func (u *GamesUpsertBulk) SetStakes(v []uint64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetStakes(v)
	})
}

// UpdateStakes sets the "stakes" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateStakes() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateStakes()
	})
}

// SetPlayers sets the "players" field.
func (u *GamesUpsertBulk) SetPlayers(v []model.PlayerState) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetPlayers(v)
	})
}

// UpdatePlayers sets the "players" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdatePlayers() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdatePlayers()
	})
}

// SetWinners sets the "winners" field.
func (u *GamesUpsertBulk) SetWinners(v []model.PlayerState) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetWinners(v)
	})
}

// UpdateWinners sets the "winners" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateWinners() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateWinners()
	})
}

// SetBoard sets the "board" field.
func (u *GamesUpsertBulk) SetBoard(v poker.Cards) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetBoard(v)
	})
}

// UpdateBoard sets the "board" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateBoard() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateBoard()
	})
}

// SetDuration sets the "duration" field.
func (u *GamesUpsertBulk) SetDuration(v int64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetDuration(v)
	})
}

// AddDuration adds v to the "duration" field.
func (u *GamesUpsertBulk) AddDuration(v int64) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.AddDuration(v)
	})
}

// UpdateDuration sets the "duration" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateDuration() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateDuration()
	})
}

// SetCompletedAt sets the "completed_at" field.
func (u *GamesUpsertBulk) SetCompletedAt(v time.Time) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetCompletedAt(v)
	})
}

// UpdateCompletedAt sets the "completed_at" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateCompletedAt() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateCompletedAt()
	})
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (u *GamesUpsertBulk) ClearCompletedAt() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.ClearCompletedAt()
	})
}

// SetSettled sets the "settled" field.
func (u *GamesUpsertBulk) SetSettled(v bool) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetSettled(v)
	})
}

// UpdateSettled sets the "settled" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateSettled() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateSettled()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GamesUpsertBulk) SetUpdatedAt(v time.Time) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateUpdatedAt() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GamesUpsertBulk) SetDeletedAt(v time.Time) *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GamesUpsertBulk) UpdateDeletedAt() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GamesUpsertBulk) ClearDeletedAt() *GamesUpsertBulk {
	return u.Update(func(s *GamesUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *GamesUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the GamesCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GamesCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GamesUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
