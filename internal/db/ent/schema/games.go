package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"hamster/internal/model"
	"hamster/pkg/poker"
	"time"
)

// Games holds the schema definition for the Games entity.
type Games struct {
	ent.Schema
}

// Annotations of the Games.
func (v Games) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Schema("public"),
		entsql.Annotation{
			Table: "games",
		},
	}
}

// Fields of the Games.
func (Games) Fields() []ent.Field {
	return []ent.Field{
		field.Uint64("id").Unique().SchemaType(map[string]string{
			dialect.Postgres: "serial",
		}),
		field.Uint64("table_id"),
		field.Enum("status").Values("running", "completed").Default("running"),
		field.JSON("seats", []string{}),
		field.Uint64("pot"),
		field.Int("button"),
		field.Int("sb"),
		field.Int("bb"),
		field.JSON("stakes", []uint64{}),
		field.JSON("players", []model.PlayerState{}),
		field.JSON("winners", []model.PlayerState{}),
		field.JSON("board", poker.Cards{}),
		field.Int64("duration"),
		field.Time("started_at").Default(time.Now).Immutable(),
		field.Time("completed_at").Optional().Nillable().Annotations(),
		field.Bool("settled").Default(false),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.String("created_by").Optional().Immutable().Annotations(),
		field.String("updated_by").Optional().Immutable().Annotations(),
		field.Time("updated_at").Default(time.Now).UpdateDefault(time.Now),
		field.Time("deleted_at").Optional().Nillable().Annotations(),
	}
}

// Edges of the Games.
func (Games) Edges() []ent.Edge {
	return nil
}

// Indexes of the Games.
func (v Games) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("table_id"),
	}
}
