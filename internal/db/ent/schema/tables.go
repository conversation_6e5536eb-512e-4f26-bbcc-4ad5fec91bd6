package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"time"
)

// Tables holds the schema definition for the Tables entity.
type Tables struct {
	ent.Schema
}

// Annotations of the Tables.
func (v Tables) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Schema("public"),
		entsql.Annotation{
			Table: "tables",
		},
	}
}

// Fields of the Tables.
func (Tables) Fields() []ent.Field {
	return []ent.Field{
		field.Uint64("id").Unique().SchemaType(map[string]string{
			dialect.Postgres: "serial",
		}),
		field.String("name").NotEmpty(),
		field.String("code").NotEmpty(),
		field.Enum("type").Values("6p", "9p").Default("6p"),
		field.Enum("status").Values("active", "inactive").Default("active"),
		field.Uint64("big_blind"),
		field.Uint64("small_blind"),
		field.Uint64("min_buy_in"),
		field.Uint64("max_buy_in"),
		field.Bool("is_visible").Default(true),
		field.Time("expired_at").Optional().Nillable(),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.String("created_by").Optional().Immutable().Annotations(),
		field.String("updated_by").Optional().Immutable().Annotations(),
		field.Time("updated_at").Default(time.Now).UpdateDefault(time.Now),
		field.Time("deleted_at").Optional().Nillable().Annotations(),
	}
}

// Edges of the Tables.
func (Tables) Edges() []ent.Edge {
	return nil
}

// Indexes of the Tables.
func (v Tables) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("code").Unique(),
	}
}
