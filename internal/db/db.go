package db

import (
	"database/sql"
	"entgo.io/ent/dialect"
	entsql "entgo.io/ent/dialect/sql"
	"fmt"
	d "github.com/dimiro1/health/db"
	_ "github.com/lib/pq"
	"hamster/config"
	"hamster/internal/db/ent"
	"hamster/internal/db/ent/hook"
	"hamster/internal/db/plugins/softdelete"
	"hamster/pkg/log"

	"time"
)

var txKey = struct{ name string }{"tx"}

type Client struct {
	*ent.Client
}

func New(cfg config.Config) (*Client, error) {
	pg := cfg.DB

	sslMode := "disable"
	if pg.SSLMode != "" {
		sslMode = pg.SSLMode
	}

	dsn := fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
		pg.Host, pg.Port, pg.UserName,
		pg.DBName, pg.Password, sslMode)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}
	db.SetMaxOpenConns(pg.MaxOpen)
	db.SetMaxIdleConns(pg.MaxIdle)
	db.SetConnMaxIdleTime(time.Duration(pg.MaxIdleTime) * time.Second)
	db.SetConnMaxLifetime(time.Duration(pg.MaxLifetime) * time.Second)

	checker := d.NewPostgreSQLChecker(db)
	err = checker.DB.Ping()
	if err != nil {
		return nil, err
	}

	options := []ent.Option{
		ent.Driver(entsql.OpenDB(dialect.Postgres, db)),
		ent.Log(func(args ...interface{}) {
			log.Infof("%v", args...)
		}),
	}
	if cfg.Logger.Debug {
		options = append(options, ent.Debug())
	}
	cli := ent.NewClient(options...)
	cli.Use(hook.On(softdelete.Hook, ent.OpDeleteOne|ent.OpDelete))
	cli.Intercept(softdelete.Interceptor())
	return &Client{cli}, nil
}
