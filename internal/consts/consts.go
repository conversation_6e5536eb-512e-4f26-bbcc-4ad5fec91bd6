package consts

import "time"

const (
	MaxPacketSize         = 65536
	BetTimeout            = 30 * time.Second
	BuyInTimeout          = 30 * time.Second
	PingTimeout           = 10 * time.Second
	ShowdownTimeout       = 3 * time.Second
	MaxWaitingListSize    = 20
	RoundInterval         = 1 * time.Second
	BetRoundInterval      = 200 * time.Millisecond
	FlopRoundInterval     = 3 * time.Second
	AnnounceRoundInterval = 5 * time.Second
	GameInterval          = 5 * time.Second
	GameStartCountdown    = 5 * time.Second
)

var MessageCodes = map[uint16]string{
	0:  "Nice Hand!",
	1:  "Good Game",
	2:  "I’m Lucky!",
	3:  "Almost There!",
	4:  "Thanks!",
	5:  "Congratulations!",
	6:  "Sorry!",
	7:  "Good Luck!",
	8:  "😠",
	9:  "👍",
	10: "👋",
	11: "❤️",
	12: "😑",
	13: "😄",
	14: "🤔",
	15: "😇",
}
