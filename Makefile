include .env
export

GOPATH := $(shell go env GOPATH)

.SILENT:

run:
	go mod tidy -compat=1.22 && go mod download && \
	DISABLE_SWAGGER_HTTP_HANDLER='' GIN_MODE=debug go run main.go
.PHONY: run

stop:
	@echo "Stopping hamster server"
	PIDS=$$(lsof -i:$(SERVER_HTTP_PORT) -i:$(SERVER_TCP_PORT) -i:$(SERVER_WEBSOCKET_PORT) -t); \
	if [ -n "$$PIDS" ]; then \
		echo "PIDs to kill: $$PIDS"; \
		kill -9 $$PIDS; \
		echo "Hamster server stopped."; \
	else \
		echo "No hamster server processes found on ports $$PORTS."; \
	fi
.PHONY: stop

docker-run:
	docker compose up --build -d
.PHONY: docker-run

docker-stop:
	docker compose down
.PHONY: docker-stop

docker-restart:
	docker compose down && docker compose up --build -d
.PHONY: docker-restart

ent-generate:
	go run -mod=mod cmd/ent/generate/main.go
.PHONY: ent-generate

ent-migrate:
	@read -p "Enter migration label: " label; \
	go run -mod=mod ariga.io/atlas/cmd/atlas@latest migrate hash --dir file://./migration && \
	go run -mod=mod cmd/ent/migrate/main.go $$label
.PHONY: ent-migrate