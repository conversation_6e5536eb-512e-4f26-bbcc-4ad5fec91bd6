# build
FROM golang:1.23.3-alpine as build
RUN apk update \
    && export PKGS="git docker openssh-client make gcc g++ linux-headers libgcc libstdc++ bash curl" \
    && apk add --no-cache $PKGS \
    && apk add --no-cache libc6-compat

WORKDIR /build
COPY . .

ENV GO111MODULE=on
RUN go mod download
RUN go build -o server main.go

# runtime
FROM registry.cn-zhangjiakou.aliyuncs.com/caminer/alpine:latest
RUN mkdir -p /app
WORKDIR /app
COPY --from=build /build/server .
CMD ["/app/server"]