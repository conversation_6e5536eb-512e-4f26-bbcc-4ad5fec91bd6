'use client';

import { GameSeat, convertServerCard } from '@/types/poker';
import PokerCard from './PokerCard';
import { User, DollarSign } from 'lucide-react';

interface PokerSeatProps {
  seat: GameSeat;
  position: number;
  isCurrentPlayer?: boolean;
  isDealer?: boolean;
  isSmallBlind?: boolean;
  isBigBlind?: boolean;
  isActive?: boolean;
  onSitDown?: () => void;
  isEmpty?: boolean;
}

export default function PokerSeat({
  seat,
  position,
  isCurrentPlayer = false,
  isDealer = false,
  isSmallBlind = false,
  isBigBlind = false,
  isActive = false,
  onSitDown,
  isEmpty = false
}: PokerSeatProps) {
  if (isEmpty) {
    return (
      <div className="relative z-20">
        <button
          onClick={onSitDown}
          className="w-24 h-24 rounded-full border-3 border-dashed border-gray-500
                   hover:border-green-400 transition-all duration-300 flex items-center justify-center
                   bg-gray-800 bg-opacity-80 hover:bg-gray-700 hover:bg-opacity-90 group
                   hover:scale-110 hover:shadow-lg hover:shadow-green-400/20"
        >
          <User className="w-8 h-8 text-gray-400 group-hover:text-green-400 transition-colors" />
        </button>
        <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2">
          <div className="text-xs text-gray-300 text-center font-medium bg-gray-800 bg-opacity-80 px-2 py-1 rounded">
            Seat {position + 1}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative z-20">
      {/* Player Avatar */}
      <div className={`
        w-16 h-16 rounded-full border-3 flex items-center justify-center relative transition-all duration-300
        ${isCurrentPlayer ? 'border-green-400 bg-green-900 shadow-lg shadow-green-400/30' :
          isActive ? 'border-yellow-400 bg-yellow-900 shadow-lg shadow-yellow-400/30 animate-pulse' :
          'border-gray-600 bg-gray-700 bg-opacity-90'}
        ${seat.f ? 'opacity-50 grayscale' : ''}
        ${isActive ? 'scale-110' : 'scale-100'}
      `}>
        <User className={`w-6 h-6 ${
          isCurrentPlayer ? 'text-green-300' :
          isActive ? 'text-yellow-300' :
          'text-gray-300'
        }`} />

        {/* Dealer Button */}
        {isDealer && (
          <div className="absolute -top-4 -right-4 w-6 h-6 bg-white rounded-full
                         flex items-center justify-center text-xs font-bold text-black
                         shadow-lg border-2 border-gray-300 animate-bounce">
            D
          </div>
        )}

        {/* Blind Indicators */}
        {isSmallBlind && (
          <div className="absolute -top-4 -left-4 w-6 h-6 bg-blue-500 rounded-full
                         flex items-center justify-center text-xs font-bold text-white
                         shadow-lg border-2 border-blue-300">
            SB
          </div>
        )}
        {isBigBlind && (
          <div className="absolute -top-4 -left-4 w-6 h-6 bg-red-500 rounded-full
                         flex items-center justify-center text-xs font-bold text-white
                         shadow-lg border-2 border-red-300">
            BB
          </div>
        )}
      </div>

      {/* Player Info */}
      <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 w-28">
        <div className="bg-gray-900 bg-opacity-90 rounded-lg p-2 border border-gray-600 shadow-lg">
          <div className="text-xs text-white text-center font-medium truncate mb-1">
            {seat.pi}
          </div>
          <div className="text-xs text-green-400 text-center flex items-center justify-center font-bold">
            <DollarSign className="w-3 h-3 mr-1" />
            {seat.b}
          </div>
          {seat.ca && (
            <div className="text-xs text-center mt-2">
              <span className={`px-2 py-1 rounded-full text-xs font-bold shadow-md ${
                seat.ca === 'fold' ? 'bg-red-600 text-white' :
                seat.ca === 'call' ? 'bg-blue-600 text-white' :
                seat.ca === 'raise' ? 'bg-green-600 text-white' :
                seat.ca === 'check' ? 'bg-gray-600 text-white' :
                'bg-purple-600 text-white'
              }`}>
                {seat.ca.toUpperCase()}
                {seat.cb && seat.cb > 0 && ` $${seat.cb}`}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Player Cards */}
      {seat.cs && seat.cs.length > 0 && (
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 flex space-x-1">
          {seat.cs.map((serverCard, index) => (
            <PokerCard
              key={index}
              card={convertServerCard(serverCard)}
              size="small"
              faceDown={!seat.sd && !isCurrentPlayer}
            />
          ))}
        </div>
      )}

      {/* Show card backs for players in game who don't have visible cards */}
      {!seat.cs && seat.ss === 'occupied' && seat.p && (
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 flex space-x-1">
          <PokerCard faceDown size="small" />
          <PokerCard faceDown size="small" />
        </div>
      )}

      {/* All-in indicator */}
      {seat.ai && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
                       bg-red-600 text-white text-xs px-2 py-1 rounded font-bold">
          ALL IN
        </div>
      )}

      {/* Folded overlay */}
      {seat.f && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
                       bg-gray-900 bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          FOLDED
        </div>
      )}
    </div>
  );
}