'use client';

import { useState } from 'react';
import { BetAction } from '@/types/poker';
import { DollarSign, Minus, Plus } from 'lucide-react';

interface BettingControlsProps {
  balance: number;
  minBet: number;
  maxBet: number;
  currentBet: number;
  potSize: number;
  canCheck: boolean;
  canCall: boolean;
  callAmount: number;
  onAction: (action: BetAction, amount?: number) => void;
  disabled?: boolean;
}

export default function BettingControls({
  balance,
  minBet,
  maxBet,
  currentBet,
  potSize,
  canCheck,
  canCall,
  callAmount,
  onAction,
  disabled = false
}: BettingControlsProps) {
  const [betAmount, setBetAmount] = useState(minBet);
  const [showBetInput, setShowBetInput] = useState(false);

  // Debug logging
  console.log('BettingControls props:', {
    balance,
    minBet,
    maxBet,
    currentBet,
    potSize,
    canCheck,
    canCall,
    callAmount,
    disabled
  });

  const handleBetAmountChange = (amount: number) => {
    const newAmount = Math.max(minBet, Math.min(maxBet, amount));
    setBetAmount(newAmount);
  };

  const quickBetAmounts = [
    { label: '1/4 Pot', amount: Math.round(potSize * 0.25) },
    { label: '1/2 Pot', amount: Math.round(potSize * 0.5) },
    { label: '3/4 Pot', amount: Math.round(potSize * 0.75) },
    { label: 'Pot', amount: potSize },
  ].filter(bet => bet.amount >= minBet && bet.amount <= maxBet);

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-300 mb-2">
          <span>Balance: ${balance}</span>
          <span>Pot: ${potSize}</span>
        </div>
        <div className="flex justify-between text-sm text-gray-300 mb-2">
          <span>Min: ${minBet}</span>
          <span>Max: ${maxBet}</span>
        </div>
        <div className="flex justify-between text-xs text-gray-400">
          <span>Current Bet: ${currentBet}</span>
          <span>Call Amount: ${callAmount}</span>
        </div>
      </div>

      {/* Primary Actions */}
      <div className="grid grid-cols-3 gap-2 mb-4">
        <button
          onClick={() => onAction('fold')}
          disabled={disabled}
          className="px-4 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 
                   text-white rounded-lg font-medium transition-colors"
        >
          Fold
        </button>

        {canCheck ? (
          <button
            onClick={() => {
              console.log('Check action triggered');
              onAction('check');
            }}
            disabled={disabled}
            className="px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600
                     text-white rounded-lg font-medium transition-colors"
          >
            Check
          </button>
        ) : canCall && callAmount > 0 ? (
          <button
            onClick={() => {
              console.log('Call action triggered, amount:', callAmount);
              // If call amount equals balance, it's an all-in
              if (callAmount >= balance) {
                onAction('all_in', balance);
              } else {
                onAction('call', callAmount);
              }
            }}
            disabled={disabled}
            className="px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600
                     text-white rounded-lg font-medium transition-colors"
          >
            {callAmount >= balance ? `All In $${balance}` : `Call $${callAmount}`}
          </button>
        ) : canCall && callAmount === 0 ? (
          <button
            onClick={() => {
              console.log('Check action triggered (call amount is 0)');
              onAction('check');
            }}
            disabled={disabled}
            className="px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600
                     text-white rounded-lg font-medium transition-colors"
          >
            Check
          </button>
        ) : (
          <button
            disabled
            className="px-4 py-3 bg-gray-600 text-white rounded-lg font-medium"
          >
            -
          </button>
        )}

        <button
          onClick={() => setShowBetInput(!showBetInput)}
          disabled={disabled}
          className="px-4 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 
                   text-white rounded-lg font-medium transition-colors"
        >
          {currentBet > 0 ? 'Raise' : 'Bet'}
        </button>
      </div>

      {/* Bet Input Section */}
      {showBetInput && (
        <div className="border-t border-gray-700 pt-4">
          <div className="mb-3">
            <label className="block text-sm text-gray-300 mb-2">
              {currentBet > 0 ? 'Raise to:' : 'Bet amount:'}
            </label>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBetAmountChange(betAmount - minBet)}
                disabled={betAmount <= minBet}
                className="p-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 
                         text-white rounded transition-colors"
              >
                <Minus className="w-4 h-4" />
              </button>
              
              <div className="flex-1 relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 
                                    w-4 h-4 text-gray-400" />
                <input
                  type="number"
                  value={betAmount}
                  onChange={(e) => handleBetAmountChange(parseInt(e.target.value) || 0)}
                  min={minBet}
                  max={maxBet}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 
                           rounded-lg text-white focus:outline-none focus:border-green-500"
                />
              </div>
              
              <button
                onClick={() => handleBetAmountChange(betAmount + minBet)}
                disabled={betAmount >= maxBet}
                className="p-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 
                         text-white rounded transition-colors"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Quick Bet Buttons */}
          {quickBetAmounts.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-gray-400 mb-2">Quick bets:</div>
              <div className="grid grid-cols-2 gap-2">
                {quickBetAmounts.map((quick) => (
                  <button
                    key={quick.label}
                    onClick={() => setBetAmount(quick.amount)}
                    className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white 
                             rounded text-sm transition-colors"
                  >
                    {quick.label} (${quick.amount})
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* All-in and Confirm Buttons */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => onAction('all_in', balance)}
              disabled={disabled}
              className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 
                       text-white rounded-lg font-medium transition-colors"
            >
              All In (${balance})
            </button>
            
            <button
              onClick={() => {
                if (currentBet > 0) {
                  onAction('raise', betAmount);
                } else {
                  onAction('bet', betAmount);
                }
                setShowBetInput(false);
              }}
              disabled={disabled || betAmount < minBet || betAmount > maxBet}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 
                       text-white rounded-lg font-medium transition-colors"
            >
              {currentBet > 0 ? `Raise $${betAmount}` : `Bet $${betAmount}`}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}