'use client';

import { Card } from '@/types/poker';

interface PokerCardProps {
  card?: Card;
  faceDown?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export default function PokerCard({
  card,
  faceDown = false,
  size = 'medium',
  className = ''
}: PokerCardProps) {
  // Card dimensions: 88x124 pixels
  // Use exact pixel values for proper sprite display
  const sizeClasses = {
    small: 'w-[66px] h-[93px]',   // 0.75x scale
    medium: 'w-[88px] h-[124px]', // Full size (1:1)
    large: 'w-[110px] h-[155px]'  // 1.25x scale
  };

  // Get sprite position for a card rank
  const getSpritePosition = (rank: string) => {
    const rankMap: { [key: string]: { x: number, y: number } } = {
      'A': { x: 0, y: 0 },    // Position 1 (top-left)
      '2': { x: 1, y: 0 },    // Position 2
      '3': { x: 2, y: 0 },    // Position 3
      '4': { x: 3, y: 0 },    // Position 4
      '5': { x: 4, y: 0 },    // Position 5
      '6': { x: 0, y: 1 },    // Position 6 (second row)
      '7': { x: 1, y: 1 },    // Position 7
      '8': { x: 2, y: 1 },    // Position 8
      '9': { x: 3, y: 1 },    // Position 9
      '10': { x: 4, y: 1 },   // Position 10
      'J': { x: 0, y: 2 },    // Position J (third row)
      'Q': { x: 1, y: 2 },    // Position Q
      'K': { x: 2, y: 2 },    // Position K
    };
    return rankMap[rank] || { x: 0, y: 0 };
  };

  // Get suit sprite file
  const getSuitSprite = (suit: string) => {
    const suitMap: { [key: string]: string } = {
      'spades': 'Spades - Top Down 88x124.png',
      'hearts': 'Hearts - Top Down 88x124.png',
      'diamonds': 'Diamonds - Top Down 88x124.png',
      'clubs': 'Clubs - Top Down 88x124.png',
      'spade': 'Spades - Top Down 88x124.png',
      'heart': 'Hearts - Top Down 88x124.png',
      'diamond': 'Diamonds - Top Down 88x124.png',
      'club': 'Clubs - Top Down 88x124.png'
    };
    return suitMap[suit] || 'Spades - Top Down 88x124.png';
  };

  if (faceDown || !card) {
    // Get scale factor based on size
    const getScaleFactor = () => {
      switch (size) {
        case 'small': return 0.75;
        case 'medium': return 1.0;
        case 'large': return 1.25;
        default: return 1.0;
      }
    };

    const scale = getScaleFactor();

    // Card back sprite has 2 backs side by side, we want the blue one (left side, position 0)
    const backSpriteWidth = 88 * 2; // 176px total width (2 card backs)
    const backSpriteHeight = 124; // 124px height

    // Scale the sprite
    const scaledBackSpriteWidth = backSpriteWidth * scale;
    const scaledBackSpriteHeight = backSpriteHeight * scale;

    return (
      <div
        className={`
          ${sizeClasses[size]}
          ${className}
          rounded-lg shadow-md overflow-hidden
        `}
        style={{
          backgroundImage: `url('/Cards/Back - Top Down 88x124.png')`,
          backgroundPosition: `0px 0px`, // Blue card back is at position (0,0)
          backgroundSize: `${scaledBackSpriteWidth}px ${scaledBackSpriteHeight}px`,
          backgroundRepeat: 'no-repeat'
        }}
      />
    );
  }

  const position = getSpritePosition(card.rank);
  const spriteFile = getSuitSprite(card.suit);

  // Get scale factor based on size
  const getScaleFactor = () => {
    switch (size) {
      case 'small': return 0.75;
      case 'medium': return 1.0;
      case 'large': return 1.25;
      default: return 1.0;
    }
  };

  const scale = getScaleFactor();

  // Each card in sprite is 88x124, sprite has 5 columns and 3 rows
  const cardWidth = 88;
  const cardHeight = 124;
  const spriteWidth = cardWidth * 5; // 440px total width
  const spriteHeight = cardHeight * 3; // 372px total height

  // Scale everything proportionally
  const scaledSpriteWidth = spriteWidth * scale;
  const scaledSpriteHeight = spriteHeight * scale;
  const scaledCardWidth = cardWidth * scale;
  const scaledCardHeight = cardHeight * scale;

  return (
    <div
      className={`
        ${sizeClasses[size]}
        ${className}
        rounded-lg shadow-md overflow-hidden
      `}
      style={{
        backgroundImage: `url('/Cards/${spriteFile}')`,
        backgroundPosition: `-${position.x * scaledCardWidth}px -${position.y * scaledCardHeight}px`,
        backgroundSize: `${scaledSpriteWidth}px ${scaledSpriteHeight}px`,
        backgroundRepeat: 'no-repeat'
      }}
    />
  );
}