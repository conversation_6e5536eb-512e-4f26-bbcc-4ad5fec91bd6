'use client';

import { Table } from '@/types/poker';
import { Users, DollarSign, Clock } from 'lucide-react';

interface LobbyCardProps {
  table: Table;
  onJoin: (tableId: number) => void;
}

export default function LobbyCard({ table, onJoin }: LobbyCardProps) {
  const maxPlayers = table.t === '9p' ? 9 : 6;
  
  return (
    <div className="bg-gray-800 rounded-lg p-6 hover:bg-gray-750 transition-colors border border-gray-700">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-semibold text-white">{table.n}</h3>
          <p className="text-gray-400 text-sm">Code: {table.c}</p>
        </div>
        <div className="text-right">
          <div className="text-green-400 font-semibold">
            ${table.sb}/${table.bb}
          </div>
          <div className="text-gray-400 text-sm">
            {table.t === '9p' ? '9-max' : '6-max'}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div className="flex items-center text-gray-300">
          <Users className="w-4 h-4 mr-2" />
          <span>{table.sd}/{maxPlayers} seated</span>
        </div>
        <div className="flex items-center text-gray-300">
          <DollarSign className="w-4 h-4 mr-2" />
          <span>Avg pot: ${table.ap}</span>
        </div>
      </div>

      <div className="flex justify-between items-center mb-4 text-sm">
        <div className="text-gray-400">
          Buy-in: ${table.nbi} - ${table.xbi}
        </div>
        <div className="text-gray-400">
          {table.th} hands played
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            table.sd === 0 ? 'bg-red-400' : 
            table.sd < maxPlayers / 2 ? 'bg-yellow-400' : 'bg-green-400'
          }`} />
          <span className="text-xs text-gray-400">
            {table.sd === 0 ? 'Empty' : 
             table.sd < maxPlayers / 2 ? 'Available' : 'Active'}
          </span>
        </div>
        
        <button
          onClick={() => onJoin(table.id)}
          disabled={table.sd >= maxPlayers}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            table.sd >= maxPlayers
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {table.sd >= maxPlayers ? 'Full' : 'Join Table'}
        </button>
      </div>
    </div>
  );
}