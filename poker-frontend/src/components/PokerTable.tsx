'use client';

import { GameState, TableState, Table, Card, convertServerCard, ServerCard } from '@/types/poker';
import PokerSeat from './PokerSeat';
import PokerCard from './PokerCard';
import { DollarSign } from 'lucide-react';

// Helper function to get community cards to show based on game state
function getCommunityCardsToShow(gameState?: GameState): (ServerCard | null)[] {
  if (!gameState) {
    return Array(5).fill(null);
  }

  const cardCount = getCommunityCardCount(gameState.s);
  const result: (ServerCard | null)[] = Array(5).fill(null);

  // Fill with actual cards from the board
  const boardCards = gameState.bd || [];
  for (let i = 0; i < Math.min(cardCount, boardCards.length); i++) {
    result[i] = boardCards[i];
  }

  return result;
}

// Helper function to get number of community cards based on game state
function getCommunityCardCount(state: string): number {
  switch (state) {
    case 'init':
    case 'pre_flop':
      return 0;
    case 'flop':
      return 3;
    case 'turn':
      return 4;
    case 'river':
    case 'announce':
    case 'settle':
      return 5;
    default:
      return 0;
  }
}

// Helper function to display game state in human-readable format
function getGameStateDisplay(state: string): string {
  const stateMap: { [key: string]: string } = {
    'init': 'GAME STARTING',
    'pre_flop': 'PRE-FLOP',
    'flop': 'FLOP',
    'turn': 'TURN',
    'river': 'RIVER',
    'announce': 'SHOWDOWN',
    'settle': 'SETTLING',
    'waiting': 'WAITING FOR PLAYERS'
  };
  return stateMap[state] || state.toUpperCase().replace('_', ' ');
}

interface PokerTableProps {
  gameState?: GameState;
  tableState?: TableState;
  table?: Table;
  currentPlayerId?: string;
  onSitDown?: (seat: number) => void;
}

export default function PokerTable({
  gameState,
  tableState,
  table,
  currentPlayerId,
  onSitDown
}: PokerTableProps) {
  // Determine max seats based on table type
  const maxSeats = table?.t === '6p' ? 6 : 9;

  // Create seat positions based on table type
  const getSeatPosition = (seatIndex: number) => {
    if (maxSeats === 6) {
      // 6-player layout:
      // 0   1   2
      //
      // 5   4   3
      const positions6p = [
        { x: -160, y: -180 }, // Seat 0 - top left
        { x: 0, y: -180 },    // Seat 1 - top center
        { x: 160, y: -180 },  // Seat 2 - top right
        { x: 160, y: 140 },   // Seat 3 - bottom right
        { x: 0, y: 140 },     // Seat 4 - bottom center
        { x: -160, y: 140 },  // Seat 5 - bottom left
      ];
      return positions6p[seatIndex] || { x: 0, y: 0 };
    } else {
      // 9-player layout:
      //   0   1   2   3
      // 8               4
      //   7   6   5
      const positions9p = [
        { x: -180, y: -200 }, // Seat 0 - top left
        { x: -60, y: -200 },  // Seat 1 - top center-left
        { x: 60, y: -200 },   // Seat 2 - top center-right
        { x: 180, y: -200 },  // Seat 3 - top right
        { x: 280, y: -20 },   // Seat 4 - right center
        { x: 160, y: 160 },   // Seat 5 - bottom right
        { x: 0, y: 160 },     // Seat 6 - bottom center
        { x: -160, y: 160 },  // Seat 7 - bottom left
        { x: -280, y: -20 },  // Seat 8 - left center
      ];
      return positions9p[seatIndex] || { x: 0, y: 0 };
    }
  };

  const renderSeat = (seatIndex: number) => {
    const position = getSeatPosition(seatIndex);

    // Find if there's a player in this seat from table state or game state
    const tableSeat = tableState?.ss?.find((s: any) => s.s === seatIndex);
    const gameSeat = gameState?.ss?.find((s: any) => s.s === seatIndex);

    // Use game state if available, otherwise fall back to table state
    const seatData = gameSeat || tableSeat;

    // Check if seat is occupied
    const isEmpty = !seatData || seatData.ss === 'empty' || !seatData.pi;
    const isCurrentPlayer = seatData?.pi === currentPlayerId;
    const isDealer = gameState?.b === seatIndex;
    const isSmallBlind = gameState?.sb === seatIndex;
    const isBigBlind = gameState?.bb === seatIndex;
    const isActive = gameState?.a === seatIndex;

    return (
      <div
        key={seatIndex}
        className="absolute"
        style={{
          left: `calc(50% + ${position.x}px)`,
          top: `calc(50% + ${position.y}px)`,
          transform: 'translate(-50%, -50%)'
        }}
      >
        <PokerSeat
          seat={seatData!}
          position={seatIndex}
          isCurrentPlayer={isCurrentPlayer}
          isDealer={isDealer}
          isSmallBlind={isSmallBlind}
          isBigBlind={isBigBlind}
          isActive={isActive}
          isEmpty={isEmpty}
          onSitDown={() => onSitDown?.(seatIndex)}
        />
      </div>
    );
  };

  return (
    <div className="relative w-full max-w-4xl h-full mx-auto">
      {/* Table Background Image */}
      <div className="relative w-full h-full shadow-2xl overflow-hidden bg-green-800">
        {/* Background Image */}
        <img
          src="/table1.png"
          alt="Poker Table"
          className="absolute inset-0 w-full h-full object-cover"
          onError={(e) => {
            console.error('Failed to load table image');
            e.currentTarget.style.display = 'none';
          }}
        />

        {/* Community Cards */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex space-x-2 z-10">
          {getCommunityCardsToShow(gameState).map((card: ServerCard | null, index: number) => {
            // Only render if we should show this card position
            const shouldShowCard = card !== null;
            const cardCount = getCommunityCardCount(gameState?.s || '');
            const shouldShowPosition = index < cardCount;

            if (!shouldShowPosition) {
              return null; // Don't render positions that shouldn't be visible yet
            }

            return (
              <div key={index} className="animate-fadeIn" style={{ animationDelay: `${index * 200}ms` }}>
                {shouldShowCard ? (
                  <PokerCard card={convertServerCard(card)} size="medium" />
                ) : (
                  <div className="w-[88px] h-[124px]" /> // Empty placeholder for unrevealed cards
                )}
              </div>
            );
          })}
        </div>

        {/* Pot Information */}
        {gameState?.pt && gameState.pt > 0 && (
          <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
            <div className="bg-gradient-to-r from-yellow-600 to-yellow-500 text-white px-6 py-3 rounded-full flex items-center space-x-2 shadow-lg border-2 border-yellow-400 animate-pulse">
              <DollarSign className="w-5 h-5" />
              <span className="font-bold text-lg">${gameState.pt}</span>
              <span className="text-sm font-medium">POT</span>
            </div>
          </div>
        )}


        {/* Seats */}
        {Array.from({ length: maxSeats }).map((_, index) => renderSeat(index))}

        {/* Betting Actions Indicator */}
        {gameState?.a !== undefined && gameState.a >= 0 && (
          <div className="absolute top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg border border-red-400 z-10 animate-bounce">
            🎯 Action on Seat {gameState.a + 1}
          </div>
        )}
      </div>
    </div>
  );
}