'use client';

import { PokerEvent, GameState, TableState, Table, PlayerState } from '@/types/poker';
import { v4 as uuidv4 } from 'uuid';

export class PokerWebSocket {
  private ws: WebSocket | null = null;
  private listeners: Map<string, Set<(event: any) => void>> = new Map();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private userId: string;

  constructor(userId: string) {
    this.userId = userId;
  }

  async connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = process.env.NEXT_PUBLIC_HAMSTER_WS_URL || 'ws://localhost:7655';
        console.log('Attempting to connect to WebSocket:', wsUrl);
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = async () => {
          console.log('WebSocket connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          try {
            // Get auth
            await this.authorize();
            this.startHeartbeat();
            resolve(true);
          } catch (authError) {
            console.error('Authorization failed:', authError);
            this.ws?.close();
            reject(authError);
          }
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            //ignore pong messages
            if (data.t === 'pong') return;
            console.log('WebSocket message received:', data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error, 'Raw data:', event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnected = false;
          this.stopHeartbeat();
          
          // Notify listeners about disconnection
          const disconnectListeners = this.listeners.get('disconnect');
          if (disconnectListeners) {
            disconnectListeners.forEach(listener => listener({ event: 'disconnect', code: event.code, reason: event.reason }));
          }
          
          // Only attempt to reconnect if it wasn't a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            const delay = Math.min(2000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s
            console.log(`Attempting reconnection ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts} in ${delay}ms`);
            setTimeout(() => {
              this.reconnectAttempts++;
              this.connect().catch(error => {
                console.error('Reconnection failed:', error);
              });
            }, delay);
          } else if (event.code !== 1000) {
            console.error('Max reconnection attempts reached or clean close');
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error occurred:', {
            error,
            readyState: this.ws?.readyState,
            url: wsUrl,
            timestamp: new Date().toISOString()
          });
          
          // Don't reject immediately, let onclose handle reconnection
          if (this.ws?.readyState === WebSocket.CONNECTING) {
            reject(new Error(`Failed to connect to WebSocket at ${wsUrl}. Make sure the Hamster server is running on port 7655.`));
          }
        };

        // Set a connection timeout
        setTimeout(() => {
          if (this.ws?.readyState === WebSocket.CONNECTING) {
            console.error('WebSocket connection timeout');
            this.ws?.close();
            reject(new Error('WebSocket connection timeout. Check if the server is running.'));
          }
        }, 10000); // 10 second timeout

      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        reject(error);
      }
    });
  }

  private async authorize(): Promise<void> {
    try {
      // Get session token from localStorage
      const sessionToken = localStorage.getItem('session_token');
      console.log('Authorization attempt - session token:', sessionToken ? 'found' : 'NOT FOUND');

      if (!sessionToken) {
        // List all localStorage keys for debugging
        console.log('All localStorage keys:', Object.keys(localStorage));
        throw new Error('No session token found. Please log in again.');
      }

      // Send authorize event with session token directly
      this.send({
        id: Date.now(),
        t: 'authorize',
        session_token: sessionToken,
      });
    } catch (error) {
      console.error('Authorization failed:', error);
      throw error;
    }
  }

  private handleMessage(data: any): void {
    const eventType = data.t;
    
    // Handle authorization errors or unauthorized events
    if (eventType === 'error' || (data.error && data.error.includes('unauthorized'))) {
      console.error('Authorization error received:', data);
      console.log('Attempting to re-authorize...');
      this.authorize().catch(error => {
        console.error('Re-authorization failed:', error);
        // Don't close connection immediately, let it try again
      });
      return;
    }
    
    // Log successful authorization
    if (eventType === 'authorized') {
      console.log('Successfully authorized');
    }
    
    // Notify specific listeners
    const typeListeners = this.listeners.get(eventType);
    if (typeListeners) {
      typeListeners.forEach(listener => listener(data));
    }
    
    // Notify all listeners
    const allListeners = this.listeners.get('*');
    if (allListeners) {
      allListeners.forEach(listener => listener(data));
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
        this.send({
          id: Date.now(),
          t: 'ping',
        });
      }
    }, 5000); // 5 seconds (well within the 10 second server timeout)
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  send(event: any): void {
    if (this.ws && this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(event));
    } else {
      console.warn('Cannot send message, WebSocket not ready:', {
        connected: this.isConnected,
        readyState: this.ws?.readyState,
        event
      });
    }
  }

  on(eventType: string, listener: (event: any) => void): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    this.listeners.get(eventType)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      const typeListeners = this.listeners.get(eventType);
      if (typeListeners) {
        typeListeners.delete(listener);
        if (typeListeners.size === 0) {
          this.listeners.delete(eventType);
        }
      }
    };
  }

  // Game actions
  getTables(): void {
    this.send({
      id: Date.now(),
      t: 'get_tables',
    });
  }

  joinTable(tableId: number): void {
    this.send({
      id: Date.now(),
      t: 'join_table',
      table_id: tableId,
    });
  }

  leaveTable(tableId: number): void {
    this.send({
      id: Date.now(),
      t: 'leave_table',
      table_id: tableId,
    });
  }

  sitDown(tableId: number, seat: number): void {
    this.send({
      id: Date.now(),
      t: 'sit_down',
      table_id: tableId,
      seat,
    });
  }

  standUp(tableId: number): void {
    this.send({
      id: Date.now(),
      t: 'stand_up',
      table_id: tableId,
    });
  }

  bet(action: string, amount: number = 0): void {
    this.send({
      id: Date.now(),
      t: 'bet',
      action,
      amount,
    });
  }

  buyIn(amount: number): void {
    this.send({
      id: Date.now(),
      t: 'buy_in',
      action: 'buy_in',
      amount,
    });
  }

  sendMessage(code: number): void {
    this.send({
      id: Date.now(),
      t: 'send_message',
      code,
    });
  }

  showdown(): void {
    this.send({
      id: Date.now(),
      t: 'showdown',
    });
  }

  getTableState(tableId: number): void {
    this.send({
      id: Date.now(),
      t: 'get_table_state',
      table_id: tableId,
    });
  }

  getGameState(tableId: number): void {
    this.send({
      id: Date.now(),
      t: 'get_game_state',
      table_id: tableId,
    });
  }

  disconnect(): void {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
    }
  }
}