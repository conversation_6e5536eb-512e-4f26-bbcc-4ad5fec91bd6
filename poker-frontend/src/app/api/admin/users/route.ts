import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Simple admin endpoint that returns mock user data
  // In a real implementation, you might want to fetch this from your database
  // or connect to your Hamster server's user management system
  
  const mockUsers = [
    {
      uid: 'user1',
      email: '<EMAIL>',
      displayName: '<PERSON> Chau',
      creationTime: new Date().toISOString(),
      lastSignInTime: new Date().toISOString(),
      disabled: false,
    },
    {
      uid: 'user2', 
      email: '<EMAIL>',
      displayName: 'Abby MD',
      creationTime: new Date().toISOString(),
      lastSignInTime: new Date().toISOString(),
      disabled: false,
    }
  ];

  return NextResponse.json({ users: mockUsers });
}