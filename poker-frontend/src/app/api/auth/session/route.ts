import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { firebase_token, user_email, user_id, display_name } = body;

    if (!firebase_token || !user_email || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate a unique session token
    const sessionToken = uuidv4();
    
    // Get Redis client
    const redis = await getRedisClient();
    
    // Store session data in Redis with 24 hour expiration
    const sessionData = {
      user_email,
      user_id,
      display_name: display_name || '',
      firebase_token,
      created_at: new Date().toISOString(),
    };
    
    await redis.setEx(
      `session:${sessionToken}`, 
      24 * 60 * 60, // 24 hours in seconds
      JSON.stringify(sessionData)
    );

    return NextResponse.json({
      success: true,
      session_token: sessionToken,
      expires_in: 24 * 60 * 60 // 24 hours
    });

  } catch (error) {
    console.error('Session creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create session' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.headers.get('x-session-token');
    
    if (!sessionToken) {
      return NextResponse.json(
        { error: 'No session token provided' },
        { status: 401 }
      );
    }

    const redis = await getRedisClient();
    const sessionData = await redis.get(`session:${sessionToken}`);
    
    if (!sessionData) {
      return NextResponse.json(
        { error: 'Invalid or expired session' },
        { status: 401 }
      );
    }

    const session = JSON.parse(sessionData);
    
    return NextResponse.json({
      success: true,
      user: {
        email: session.user_email,
        user_id: session.user_id,
        display_name: session.display_name
      }
    });

  } catch (error) {
    console.error('Session validation error:', error);
    return NextResponse.json(
      { error: 'Failed to validate session' },
      { status: 500 }
    );
  }
}