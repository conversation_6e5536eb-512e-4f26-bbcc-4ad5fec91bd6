'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';
import PokerTable from '@/components/PokerTable';
import BettingControls from '@/components/BettingControls';
import PokerCard from '@/components/PokerCard';
import { useAuth } from '@/contexts/AuthContext';
import { usePoker } from '@/contexts/PokerContext';
import { convertServerCard } from '@/types/poker';
import { ArrowLeft, MessageCircle, Users, Settings } from 'lucide-react';

// Helper function to display game state in human-readable format
function getGameStateDisplay(state: string): string {
  const stateMap: { [key: string]: string } = {
    'init': 'GAME STARTING',
    'pre_flop': 'PRE-FLOP',
    'flop': 'FLOP',
    'turn': 'TURN',
    'river': 'RIVER',
    'settle': 'SETTLING',
    'waiting': 'WAITING FOR PLAYERS'
  };
  return stateMap[state] || state.toUpperCase().replace('_', ' ');
}

// Helper function to get number of community cards to show based on game state
function getCommunityCardCount(state: string): number {
  switch (state) {
    case 'init':
    case 'pre_flop':
      return 0;
    case 'flop':
      return 3;
    case 'turn':
      return 4;
    case 'river':
    case 'announce':
    case 'settle':
      return 5;
    default:
      return 0;
  }
}

export default function TablePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { 
    connected, 
    currentTable, 
    tableState, 
    gameState, 
    playerState,
    joinTable,
    leaveTable,
    sitDown,
    standUp,
    bet,
    buyIn,
    ws
  } = usePoker();

  const [showBuyInModal, setShowBuyInModal] = useState(false);
  const [buyInAmount, setBuyInAmount] = useState(0);
  const [chatMessages, setChatMessages] = useState<Array<{id: string, player: string, message: string}>>([]);
  const [quickActions, setQuickActions] = useState<{[key: string]: boolean}>({});
  const [showdownPrompt, setShowdownPrompt] = useState<{show: boolean, timeout: number}>({show: false, timeout: 0});

  const tableId = parseInt(params.id as string);
  const currentPlayerId = user?.email || '';
  
  // Find current player's seat
  const currentPlayerSeat = gameState?.ss.find(s => s.pi === currentPlayerId);
  const isMyTurn = gameState?.a !== undefined && gameState?.ss?.[gameState.a]?.pi === currentPlayerId;

  useEffect(() => {
    if (!connected) return;

    // Set up WebSocket listeners for this table
    const unsubscribeChat = ws?.on('chat', (event) => {
      setChatMessages(prev => [...prev, {
        id: event.id,
        player: event.player_id,
        message: event.message
      }]);
    });

    const unsubscribeBuyInPrompt = ws?.on('buy_in_prompt', (event) => {
      setBuyInAmount(event.min_buy_in);
      setShowBuyInModal(true);
    });

    const unsubscribeGoToTable = ws?.on('go_to_table', (event) => {
      console.log('Successfully joined table:', event.table);
    });

    const unsubscribeJoinedTable = ws?.on('joined_table', (event) => {
      console.log('Joined table event:', event);
    });

    const unsubscribeShowdownPrompt = ws?.on('showdown_prompt', (event) => {
      console.log('Showdown prompt received:', event);
      setShowdownPrompt({show: true, timeout: event.timeout});
    });

    return () => {
      unsubscribeChat?.();
      unsubscribeBuyInPrompt?.();
      unsubscribeGoToTable?.();
      unsubscribeJoinedTable?.();
      unsubscribeShowdownPrompt?.();
    };
  }, [connected, ws]);

  // Auto-join table when connected and not already in the table
  useEffect(() => {
    if (connected && (!currentTable || currentTable.id !== tableId)) {
      console.log('Auto-joining table:', tableId);
      joinTable(tableId);
    }
  }, [connected, currentTable, tableId, joinTable]);

  const handleLeaveTable = () => {
    leaveTable(tableId);
    router.push('/lobby');
  };

  const handleSitDown = (seat: number) => {
    if (!currentTable || currentTable.id !== tableId) {
      console.log('Must join table first before sitting down');
      // Auto-join if not already in table
      joinTable(tableId);
      // Retry sitting down after a delay
      setTimeout(() => {
        sitDown(tableId, seat);
      }, 1000);
    } else {
      sitDown(tableId, seat);
    }
  };

  const handleStandUp = () => {
    standUp(tableId);
  };

  const handleBet = (action: string, amount?: number) => {
    console.log('handleBet called with:', { action, amount });
    bet(action, amount);
  };

  const handleShowdown = (show: boolean) => {
    if (show) {
      ws?.showdown();
    }
    setShowdownPrompt({show: false, timeout: 0});
  };

  const handleBuyIn = () => {
    if (buyInAmount > 0) {
      buyIn(buyInAmount);
      setShowBuyInModal(false);
    }
  };



  if (!connected) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-white">Connecting to server...</div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-900 bg-opacity-95 backdrop-blur-sm border-b border-gray-700 px-6 py-3 shadow-lg">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLeaveTable}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-gray-700 to-gray-600
                         hover:from-gray-600 hover:to-gray-500 rounded-lg transition-all duration-300
                         shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Lobby
              </button>
              
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                  {currentTable?.n || `Table ${tableId}`}
                </h1>
                {currentTable && (
                  <p className="text-sm text-gray-300 font-medium">
                    💰 ${currentTable.sb}/${currentTable.bb} • 🎯 Code: {currentTable.c}
                  </p>
                )}
              </div>

              {/* User ID */}
              <div className="flex items-center space-x-2 px-3 py-1 bg-gray-700 rounded-full text-sm">
                <span className="text-gray-300">👤 {currentPlayerId}</span>
              </div>

              {/* Connection Status */}
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                connected ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connected ? 'bg-green-300' : 'bg-red-300'
                }`} />
                <span>{connected ? 'Connected' : 'Disconnected'}</span>
              </div>

              {/* Table Status */}
              {!currentTable && connected && (
                <div className="text-yellow-400 text-sm">
                  Joining table...
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {currentPlayerSeat && (
                <button
                  onClick={handleStandUp}
                  className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                >
                  Stand Up
                </button>
              )}
              
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <Users className="w-4 h-4" />
                <span>{tableState?.ss?.filter(s => s.ss === 'occupied').length || 0}/{currentTable?.t === '6p' ? 6 : 9}</span>
              </div>
            </div>
          </div>
        </header>

        <div className="flex h-[calc(100vh-80px)]">
          {/* Main Table Area */}
          <div className="flex-1 p-6 relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="w-full h-full" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, #10b981 0%, transparent 50%),
                                 radial-gradient(circle at 75% 75%, #3b82f6 0%, transparent 50%)`,
                backgroundSize: '100px 100px'
              }} />
            </div>

            <div className="h-full flex flex-col relative z-10">
              {/* Table */}
              <div className="flex-1 flex items-center justify-center">
                <PokerTable
                  gameState={gameState || undefined}
                  tableState={tableState || undefined}
                  table={currentTable || undefined}
                  currentPlayerId={currentPlayerId}
                  onSitDown={handleSitDown}
                />
              </div>

              {/* Player Hand */}
              {(gameState?.c || currentPlayerSeat?.cs) && (
                <div className="mt-6 flex justify-center">
                  <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl px-6 py-3 border border-gray-600 shadow-2xl">
                    <div className="text-center mb-2 text-sm text-gray-300 font-medium">🃏 Your Cards</div>
                    <div className="flex space-x-3 justify-center">
                      {/* Use gameState.c (current player's cards) first, fallback to seat cards */}
                      {(gameState?.c || currentPlayerSeat?.cs || []).map((serverCard, index) => {
                        // Convert server card format to frontend format
                        const card = convertServerCard(serverCard);
                        return (
                          <div key={index} className="animate-fadeIn" style={{ animationDelay: `${index * 100}ms` }}>
                            <PokerCard card={card} size="large" />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}

              {/* Game Status */}
              {gameState && (
                <div className="mt-4 flex justify-center">
                  <div className="bg-gray-800 bg-opacity-80 rounded-lg px-6 py-3 border border-gray-600">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">
                        {getGameStateDisplay(gameState.s)}
                      </div>
                      {/* Show additional game info like the client does */}
                      {gameState.s === 'pre_flop' && currentPlayerSeat && (
                        <div className="text-xs text-yellow-400 mt-1">
                          {gameState.b === currentPlayerSeat.s && '🔘 You are Button'}
                          {gameState.sb === currentPlayerSeat.s && '🔵 You are Small Blind'}
                          {gameState.bb === currentPlayerSeat.s && '🔴 You are Big Blind'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Betting Controls */}
              {isMyTurn && currentPlayerSeat && gameState && (
                <div className="mt-6">
                  <div className="bg-gradient-to-r from-yellow-600 to-orange-600 text-white text-center py-2 rounded-t-lg font-bold">
                    🎯 Your Turn to Act!
                  </div>
                  <BettingControls
                    balance={currentPlayerSeat.b}
                    minBet={gameState.mr}
                    maxBet={currentPlayerSeat.b}
                    currentBet={gameState.mb}
                    potSize={gameState.pt}
                    canCheck={(gameState.mb - (currentPlayerSeat.cb || 0)) === 0}
                    canCall={(gameState.mb - (currentPlayerSeat.cb || 0)) > 0 && currentPlayerSeat.b > 0}
                    callAmount={Math.min(gameState.mb - (currentPlayerSeat.cb || 0), currentPlayerSeat.b)}
                    onAction={handleBet}
                  />
                </div>
              )}

              {/* Showdown Prompt */}
              {showdownPrompt.show && gameState?.s === 'announce' && (
                <div className="mt-6">
                  <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center py-2 rounded-t-lg font-bold">
                    🃏 Show Your Cards?
                  </div>
                  <div className="bg-gray-800 p-4 rounded-b-lg border border-gray-700">
                    <div className="text-center mb-4">
                      <div className="text-sm text-gray-300 mb-2">
                        You have {showdownPrompt.timeout} seconds to decide
                      </div>
                      <div className="text-xs text-gray-400">
                        Showing your cards reveals your hand to other players
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={() => handleShowdown(true)}
                        className="px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                      >
                        Show Cards
                      </button>
                      <button
                        onClick={() => handleShowdown(false)}
                        className="px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
                      >
                        Muck Cards
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Waiting for Action */}
              {!isMyTurn && gameState && gameState.a !== undefined && gameState.a >= 0 && (
                <div className="mt-6 flex justify-center">
                  <div className="bg-blue-600 bg-opacity-80 text-white px-6 py-3 rounded-lg text-center">
                    <div className="text-sm">Waiting for action from</div>
                    <div className="font-bold">Seat {gameState.a + 1}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="w-80 bg-gray-900 bg-opacity-95 backdrop-blur-sm border-l border-gray-600 flex flex-col shadow-2xl">
            {/* Quick Actions */}
            {currentPlayerSeat && (
              <div className="p-4 border-b border-gray-600 bg-gradient-to-r from-gray-800 to-gray-700">
                <h3 className="font-semibold flex items-center text-purple-400 mb-3">
                  <Settings className="w-5 h-5 mr-2" />
                  ⚡ Quick Actions
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setQuickActions(prev => ({...prev, autoFold: !prev.autoFold}))}
                    className={`px-3 py-2 rounded-lg text-xs font-medium transition-all ${
                      quickActions.autoFold
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    Auto Fold
                  </button>
                  <button
                    onClick={() => setQuickActions(prev => ({...prev, autoCheck: !prev.autoCheck}))}
                    className={`px-3 py-2 rounded-lg text-xs font-medium transition-all ${
                      quickActions.autoCheck
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    Auto Check
                  </button>
                </div>
              </div>
            )}

            {/* Table Stats */}
            {currentTable && (
              <div className="p-4 border-b border-gray-600 bg-gradient-to-r from-gray-800 to-gray-700">
                <h3 className="font-semibold flex items-center text-blue-400 mb-3">
                  <Users className="w-5 h-5 mr-2" />
                  📊 Table Stats
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Players:</span>
                    <span className="text-white font-medium">
                      {tableState?.ss?.filter(s => s.ss === 'occupied').length || 0}/9
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Blinds:</span>
                    <span className="text-green-400 font-medium">${currentTable.sb}/${currentTable.bb}</span>
                  </div>
                  {gameState?.pt && gameState.pt > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Current Pot:</span>
                      <span className="text-yellow-400 font-bold">${gameState.pt}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Chat */}
            <div className="flex-1 flex flex-col">
              <div className="p-4 border-b border-gray-600 bg-gradient-to-r from-gray-800 to-gray-700">
                <h3 className="font-semibold flex items-center text-green-400">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  💬 Chat
                </h3>
              </div>
              
              <div className="flex-1 p-4 overflow-y-auto bg-gray-800 bg-opacity-50">
                <div className="space-y-3">
                  {chatMessages.length === 0 ? (
                    <div className="text-center text-gray-500 text-sm italic py-8">
                      No messages yet. Start the conversation! 🎲
                    </div>
                  ) : (
                    chatMessages.map((msg) => (
                      <div key={msg.id} className="text-sm bg-gray-700 bg-opacity-50 rounded-lg p-2">
                        <span className="font-medium text-blue-400">{msg.player}:</span>
                        <span className="ml-2 text-gray-200">{msg.message}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
              
              <div className="p-4 border-t border-gray-600 bg-gray-800 bg-opacity-50">
                {/* Quick Chat Messages */}
                <div className="mb-3">
                  <div className="text-xs text-gray-400 mb-2">Quick messages:</div>
                  <div className="grid grid-cols-2 gap-1">
                    {[
                      { text: 'Nice Hand!', code: 0 },
                      { text: 'Good Game', code: 1 },
                      { text: 'I\'m Lucky!', code: 2 },
                      { text: 'Thanks!', code: 4 },
                      { text: 'Good Luck!', code: 7 },
                      { text: '👍', code: 9 },
                      { text: '👋', code: 10 },
                      { text: '😄', code: 13 }
                    ].map((msg) => (
                      <button
                        key={msg.code}
                        onClick={() => {
                          ws?.sendMessage(msg.code);
                        }}
                        className="px-2 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded text-xs transition-colors"
                      >
                        {msg.text}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Buy-in Modal */}
        {showBuyInModal && currentTable && (
          <div className="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-8 w-96 border border-gray-600 shadow-2xl">
              <h3 className="text-2xl font-bold mb-4 text-center bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                💰 Buy In
              </h3>
              <p className="text-gray-300 mb-6 text-center">
                Choose your buy-in amount for this table.
              </p>
              
              <div className="mb-6">
                <label className="block text-sm text-gray-300 mb-3 font-medium">💵 Amount ($)</label>
                <input
                  type="number"
                  value={buyInAmount}
                  onChange={(e) => setBuyInAmount(parseInt(e.target.value) || 0)}
                  min={currentTable.nbi}
                  max={currentTable.xbi}
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg
                           text-white focus:outline-none focus:border-green-500 focus:ring-2
                           focus:ring-green-500 focus:ring-opacity-50 transition-all text-lg font-bold text-center"
                />
                <div className="text-xs text-gray-400 mt-2 text-center bg-gray-700 bg-opacity-50 rounded p-2">
                  💡 Min: ${currentTable.nbi} - Max: ${currentTable.xbi}
                </div>
              </div>
              
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowBuyInModal(false)}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800
                           rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBuyIn}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600
                           rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  🎯 Buy In
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}