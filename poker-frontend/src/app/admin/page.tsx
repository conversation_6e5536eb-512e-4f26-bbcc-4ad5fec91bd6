'use client';

import { useState, useEffect } from 'react';
import AdminRoute from '@/components/AdminRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Users, Activity, Settings, BarChart3 } from 'lucide-react';

interface User {
  uid: string;
  email: string;
  displayName: string;
  creationTime: string;
  lastSignInTime: string;
  disabled: boolean;
}

export default function AdminPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('users');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users');

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">Admin Portal</h1>
          
          {/* Tab Navigation */}
          <div className="flex space-x-1 mb-8">
            {[
              { id: 'users', label: 'Users', icon: Users },
              { id: 'tables', label: 'Tables', icon: Activity },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 },
              { id: 'settings', label: 'Settings', icon: Settings },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  activeTab === id
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'users' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">User Management</h2>
              
              {loading ? (
                <div className="text-center py-8">Loading users...</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="pb-3">Email</th>
                        <th className="pb-3">Display Name</th>
                        <th className="pb-3">Created</th>
                        <th className="pb-3">Last Sign In</th>
                        <th className="pb-3">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.uid} className="border-b border-gray-700/50">
                          <td className="py-3">{user.email}</td>
                          <td className="py-3">{user.displayName || 'N/A'}</td>
                          <td className="py-3">
                            {new Date(user.creationTime).toLocaleDateString()}
                          </td>
                          <td className="py-3">
                            {user.lastSignInTime 
                              ? new Date(user.lastSignInTime).toLocaleDateString()
                              : 'Never'
                            }
                          </td>
                          <td className="py-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              user.disabled 
                                ? 'bg-red-600 text-white' 
                                : 'bg-green-600 text-white'
                            }`}>
                              {user.disabled ? 'Disabled' : 'Active'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {activeTab === 'tables' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Table Management</h2>
              <p className="text-gray-400">Table management features coming soon...</p>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Analytics</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-2">Total Users</h3>
                  <p className="text-2xl font-bold text-green-400">{users.length}</p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-2">Active Tables</h3>
                  <p className="text-2xl font-bold text-blue-400">0</p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-2">Games Played</h3>
                  <p className="text-2xl font-bold text-purple-400">0</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Server Settings</h2>
              <p className="text-gray-400">Server configuration settings coming soon...</p>
            </div>
          )}
        </div>
      </div>
    </AdminRoute>
  );
}