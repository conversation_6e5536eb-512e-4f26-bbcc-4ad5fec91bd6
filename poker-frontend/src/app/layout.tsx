import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/contexts/AuthContext';
import { PokerProvider } from '@/contexts/PokerContext';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: "X-Poker",
  description: "Texas Hold'em Poker Game",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <PokerProvider>
            {children}
          </PokerProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
