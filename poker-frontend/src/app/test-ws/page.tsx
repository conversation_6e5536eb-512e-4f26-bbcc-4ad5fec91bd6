'use client';

import { useState } from 'react';

export default function TestWebSocket() {
  const [status, setStatus] = useState('Not connected');
  const [messages, setMessages] = useState<string[]>([]);
  const [ws, setWs] = useState<WebSocket | null>(null);

  const connect = () => {
    setStatus('Connecting...');
    setMessages([]);
    
    const websocket = new WebSocket('ws://localhost:7655');
    
    websocket.onopen = () => {
      setStatus('Connected');
      setMessages(prev => [...prev, 'WebSocket connected']);
      
      // Send a test message after connection
      setTimeout(() => {
        const testMessage = {
          id: Date.now(),
          t: 'ping'
        };
        websocket.send(JSON.stringify(testMessage));
        setMessages(prev => [...prev, `Sent: ${JSON.stringify(testMessage)}`]);
      }, 1000);
    };
    
    websocket.onmessage = (event) => {
      setMessages(prev => [...prev, `Received: ${event.data}`]);
    };
    
    websocket.onclose = (event) => {
      setStatus(`Disconnected (${event.code}: ${event.reason})`);
      setMessages(prev => [...prev, `Connection closed: ${event.code} - ${event.reason}`]);
    };
    
    websocket.onerror = (error) => {
      setStatus('Error');
      setMessages(prev => [...prev, `Error: ${JSON.stringify(error)}`]);
      console.error('WebSocket error:', error);
    };
    
    setWs(websocket);
  };

  const disconnect = () => {
    if (ws) {
      ws.close();
      setWs(null);
    }
  };

  const sendAuth = async () => {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      setMessages(prev => [...prev, 'WebSocket not connected']);
      return;
    }

    try {
      // Get session token from localStorage
      const sessionToken = localStorage.getItem('session_token');
      
      if (!sessionToken) {
        setMessages(prev => [...prev, 'No session token found. Please log in first.']);
        return;
      }

      // Send authorize event with session token directly
      const authMessage = {
        id: Date.now(),
        t: 'authorize',
        session_token: sessionToken
      };

      ws.send(JSON.stringify(authMessage));
      setMessages(prev => [...prev, `Sent auth: ${JSON.stringify(authMessage)}`]);
    } catch (error) {
      setMessages(prev => [...prev, `Auth error: ${error}`]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <h1 className="text-2xl font-bold mb-4">WebSocket Test</h1>
      
      <div className="mb-4">
        <div className="mb-2">Status: <span className="font-bold">{status}</span></div>
        <div className="space-x-2">
          <button
            onClick={connect}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded"
            disabled={ws?.readyState === WebSocket.OPEN}
          >
            Connect
          </button>
          <button
            onClick={disconnect}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded"
            disabled={!ws || ws.readyState !== WebSocket.OPEN}
          >
            Disconnect
          </button>
          <button
            onClick={sendAuth}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
            disabled={!ws || ws.readyState !== WebSocket.OPEN}
          >
            Send Auth
          </button>
        </div>
      </div>

      <div className="bg-gray-800 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Messages:</h2>
        <div className="space-y-1 text-sm font-mono">
          {messages.length === 0 ? (
            <div className="text-gray-400">No messages yet...</div>
          ) : (
            messages.map((msg, index) => (
              <div key={index} className="break-all">{msg}</div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}