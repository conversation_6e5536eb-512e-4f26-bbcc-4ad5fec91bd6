'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';
import LobbyCard from '@/components/LobbyCard';
import { useAuth } from '@/contexts/AuthContext';
import { usePoker } from '@/contexts/PokerContext';
import { RefreshCw, Settings, LogOut, Shield } from 'lucide-react';

export default function LobbyPage() {
  const { user, logout, isAdmin } = useAuth();
  const { tables, connected, getTables, joinTable } = usePoker();
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (connected) {
      getTables();
    }
  }, [connected, getTables]);

  const handleRefresh = async () => {
    setRefreshing(true);
    getTables();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleJoinTable = (tableId: number) => {
    joinTable(tableId);
    router.push(`/table/${tableId}`);
  };

  const handleLogout = async () => {
    await logout();
    router.push('/login');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold">X-Poker</h1>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                connected ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connected ? 'bg-green-300' : 'bg-red-300'
                }`} />
                <span>{connected ? 'Connected' : 'Disconnected'}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-gray-300">Welcome, {user?.displayName || user?.email}</span>
              
              {isAdmin && (
                <button
                  onClick={() => router.push('/admin')}
                  className="flex items-center px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-md transition-colors"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Admin
                </button>
              )}
              
              <button
                onClick={handleRefresh}
                disabled={refreshing || !connected}
                className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-md transition-colors"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              
              <button
                onClick={handleLogout}
                className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 rounded-md transition-colors"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h2 className="text-3xl font-bold mb-2">Poker Tables</h2>
            <p className="text-gray-400">Choose a table to join and start playing!</p>
          </div>

          {!connected ? (
            <div className="text-center py-12">
              <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 max-w-md mx-auto">
                <h3 className="text-xl font-semibold text-red-400 mb-2">Connection Error</h3>
                <p className="text-gray-300">
                  Unable to connect to the poker server. Please check your connection and try again.
                </p>
                <button
                  onClick={handleRefresh}
                  className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                >
                  Retry Connection
                </button>
              </div>
            </div>
          ) : tables.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-800 rounded-lg p-8 max-w-md mx-auto">
                <h3 className="text-xl font-semibold mb-4">No Tables Available</h3>
                <p className="text-gray-400 mb-4">
                  There are currently no poker tables available. Check back later or contact an administrator.
                </p>
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                >
                  Refresh Tables
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tables.map((table) => (
                <LobbyCard
                  key={table.id}
                  table={table}
                  onJoin={handleJoinTable}
                />
              ))}
            </div>
          )}

          {/* Stats Section */}
          {tables.length > 0 && (
            <div className="mt-12 bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Lobby Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-400">
                    {tables.length}
                  </div>
                  <div className="text-gray-400 text-sm">Active Tables</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">
                    {tables.reduce((sum, table) => sum + table.sd, 0)}
                  </div>
                  <div className="text-gray-400 text-sm">Players Online</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400">
                    {tables.reduce((sum, table) => sum + table.th, 0)}
                  </div>
                  <div className="text-gray-400 text-sm">Total Hands</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-400">
                    ${Math.round(tables.reduce((sum, table) => sum + table.ap, 0) / tables.length)}
                  </div>
                  <div className="text-gray-400 text-sm">Avg Pot Size</div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}