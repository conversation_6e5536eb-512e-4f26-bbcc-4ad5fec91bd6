'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { PokerWebSocket } from '@/lib/websocket';
import { Table, GameState, TableState, PlayerState } from '@/types/poker';
import { useAuth } from './AuthContext';

interface PokerContextType {
  ws: PokerWebSocket | null;
  connected: boolean;
  tables: Table[];
  currentTable: Table | null;
  tableState: TableState | null;
  gameState: GameState | null;
  playerState: PlayerState | null;
  connect: () => Promise<void>;
  disconnect: () => void;
  getTables: () => void;
  joinTable: (tableId: number) => void;
  leaveTable: (tableId: number) => void;
  sitDown: (tableId: number, seat: number) => void;
  standUp: (tableId: number) => void;
  bet: (action: string, amount?: number) => void;
  buyIn: (amount: number) => void;
}

const PokerContext = createContext<PokerContextType | undefined>(undefined);

export function PokerProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [ws, setWs] = useState<PokerWebSocket | null>(null);
  const [connected, setConnected] = useState(false);
  const [tables, setTables] = useState<Table[]>([]);
  const [currentTable, setCurrentTable] = useState<Table | null>(null);
  const [tableState, setTableState] = useState<TableState | null>(null);
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [playerState, setPlayerState] = useState<PlayerState | null>(null);

  const connect = useCallback(async () => {
    if (!user?.email || ws) return;

    // Check if session token exists before connecting
    const sessionToken = localStorage.getItem('session_token');
    if (!sessionToken) {
      console.log('No session token found, waiting for session creation...');
      // Retry after a short delay
      setTimeout(() => connect(), 1000);
      return;
    }

    try {
      const newWs = new PokerWebSocket(user.email);
      
      // Set up event listeners
      newWs.on('authorized', () => {
        setConnected(true);
        console.log('Authorized successfully');
      });

      newWs.on('tables', (event) => {
        setTables(event.tables || []);
      });

      newWs.on('go_to_table', (event) => {
        setCurrentTable(event.table);
      });

      newWs.on('table_state', (event) => {
        setTableState(event.table_state);
      });

      newWs.on('game_state', (event) => {
        console.log('Game state received:', event.game_state);
        console.log('Game state:', event.game_state?.s);
        console.log('Game round:', event.game_state?.r);
        console.log('Board cards:', event.game_state?.bd);
        console.log('Player cards in game state:', event.game_state?.c);
        setGameState(event.game_state);
      });

      newWs.on('player_state', (event) => {
        setPlayerState(event.player_state);
      });

      newWs.on('seated', (event) => {
        console.log('Player seated:', event);
        // Refresh table state when someone sits down
      });

      newWs.on('stood', (event) => {
        console.log('Player stood up:', event);
        // Refresh table state when someone stands up
      });

      newWs.on('chat', (event) => {
        console.log('Chat message:', event);
        // Chat messages are handled in the table page
      });

      newWs.on('bet_prompt', (event) => {
        console.log('Bet prompt:', event);
        // Betting prompts are handled in the table page
      });

      newWs.on('buy_in_prompt', (event) => {
        console.log('Buy-in prompt:', event);
        // Buy-in prompts are handled in the table page
      });

      newWs.on('pong', () => {
        // Heartbeat received
      });

      newWs.on('disconnect', (event) => {
        console.log('WebSocket disconnected:', event);
        setConnected(false);
      });

      newWs.on('error', (event) => {
        console.error('WebSocket error event:', event);
      });

      // Global event listener for debugging (removed to avoid duplicate logging)
      // The WebSocket class already handles logging in websocket.ts

      await newWs.connect();
      setWs(newWs);
    } catch (error) {
      console.error('Connection failed:', error);
      setConnected(false);
    }
  }, [user?.email, ws]);

  const disconnect = useCallback(() => {
    if (ws) {
      ws.disconnect();
      setWs(null);
      setConnected(false);
      setTables([]);
      setCurrentTable(null);
      setTableState(null);
      setGameState(null);
      setPlayerState(null);
    }
  }, [ws]);

  const getTables = useCallback(() => {
    ws?.getTables();
  }, [ws]);

  const joinTable = useCallback((tableId: number) => {
    ws?.joinTable(tableId);
  }, [ws]);

  const leaveTable = useCallback((tableId: number) => {
    ws?.leaveTable(tableId);
    setCurrentTable(null);
    setTableState(null);
    setGameState(null);
  }, [ws]);

  const sitDown = useCallback((tableId: number, seat: number) => {
    ws?.sitDown(tableId, seat);
  }, [ws]);

  const standUp = useCallback((tableId: number) => {
    ws?.standUp(tableId);
  }, [ws]);

  const bet = useCallback((action: string, amount?: number) => {
    ws?.bet(action, amount || 0);
  }, [ws]);

  const buyIn = useCallback((amount: number) => {
    ws?.buyIn(amount);
  }, [ws]);

  // Auto-connect when user is available
  useEffect(() => {
    if (user && !ws) {
      connect();
    }
  }, [user, connect, ws]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const value = {
    ws,
    connected,
    tables,
    currentTable,
    tableState,
    gameState,
    playerState,
    connect,
    disconnect,
    getTables,
    joinTable,
    leaveTable,
    sitDown,
    standUp,
    bet,
    buyIn,
  };

  return <PokerContext.Provider value={value}>{children}</PokerContext.Provider>;
}

export function usePoker() {
  const context = useContext(PokerContext);
  if (context === undefined) {
    throw new Error('usePoker must be used within a PokerProvider');
  }
  return context;
}