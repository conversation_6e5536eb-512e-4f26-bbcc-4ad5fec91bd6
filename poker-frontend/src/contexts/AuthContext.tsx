'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, onAuthStateChanged, signInWithPopup, signOut } from 'firebase/auth';
import { auth, googleProvider, githubProvider } from '@/lib/firebase';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithGithub: () => Promise<void>;
  logout: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isAdmin = user?.email ? ADMIN_EMAILS.includes(user.email) : false;

  const createSession = async (user: User) => {
    try {
      console.log('Creating session for user:', user.email);
      const firebaseToken = await user.getIdToken();
      
      const response = await fetch('/api/auth/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firebase_token: firebaseToken,
          user_email: user.email,
          user_id: user.uid,
          display_name: user.displayName,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        // Store session token in localStorage
        localStorage.setItem('session_token', data.session_token);
        console.log('Session created successfully, token stored:', data.session_token);
        
        // Verify it was stored
        const storedToken = localStorage.getItem('session_token');
        console.log('Verified stored token:', storedToken);
      } else {
        const errorData = await response.text();
        console.error('Failed to create session:', response.status, errorData);
      }
    } catch (error) {
      console.error('Session creation error:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      
      if (user) {
        // Check if we already have a session token
        const existingToken = localStorage.getItem('session_token');
        if (!existingToken) {
          console.log('User logged in but no session token found, creating one...');
          await createSession(user);
        } else {
          console.log('User logged in with existing session token:', existingToken);
        }
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signInWithGoogle = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      await createSession(result.user);
    } catch (error) {
      console.error('Google sign in error:', error);
    }
  };

  const signInWithGithub = async () => {
    try {
      const result = await signInWithPopup(auth, githubProvider);
      await createSession(result.user);
    } catch (error) {
      console.error('Github sign in error:', error);
    }
  };

  const logout = async () => {
    try {
      // Clear session token from localStorage
      localStorage.removeItem('session_token');
      await signOut(auth);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    user,
    loading,
    signInWithGoogle,
    signInWithGithub,
    logout,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}