export interface PokerEvent {
  id: string;
  src_id?: string;
  t: string;
}

export interface Table {
  id: number;
  n: string; // name
  c: string; // code
  t: '6p' | '9p'; // type
  bb: number; // big blind
  sb: number; // small blind
  ea?: string; // expired at
  nbi: number; // min buy in
  xbi: number; // max buy in
  iv: boolean; // is visible
  tp: number; // total pot
  th: number; // total hands
  ap: number; // avg pot
  sd: number; // seated
}

export interface GameSeat {
  pi: string; // player id
  s: number; // seat
  ss: 'empty' | 'occupied' | 'reserved'; // seat status
  b: number; // balance
  p: boolean; // playing
  ai?: boolean; // all in
  f?: boolean; // folded
  tb?: number; // total bet
  cb?: number; // current bet
  ca?: string; // current action
  w?: number; // winnings
  h?: TexasHand; // hand
  cs?: ServerCard[]; // cards (server format)
  sd?: boolean; // showdown
}

export interface GameState {
  ti: number; // table id
  s: string; // state
  r: string; // round
  b: number; // button
  bb: number; // big blind seat
  sb: number; // small blind seat
  a: number; // active seat
  bd: ServerCard[]; // board (server format)
  pt: number; // pot
  c: ServerCard[]; // cards (server format)
  mb: number; // max bet
  mr: number; // min raise
  es: any[]; // events
  ss: GameSeat[]; // seats
}

export interface Card {
  suit: 'spades' | 'hearts' | 'diamonds' | 'clubs' | 'spade' | 'heart' | 'diamond' | 'club';
  rank: 'A' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K';
}

// Server card format
export interface ServerCard {
  key: number;
  suit: 'spade' | 'heart' | 'diamond' | 'club';
}

// Convert server card format to frontend card format
export function convertServerCard(serverCard: ServerCard): Card {
  const rankMap: { [key: number]: string } = {
    1: 'A', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9', 10: '10', 11: 'J', 12: 'Q', 13: 'K'
  };

  const suitMap: { [key: string]: string } = {
    'spade': 'spades',
    'heart': 'hearts',
    'diamond': 'diamonds',
    'club': 'clubs'
  };

  return {
    rank: rankMap[serverCard.key] as Card['rank'],
    suit: suitMap[serverCard.suit] as Card['suit']
  };
}

export interface TexasHand {
  cards: Card[];
  rank: number;
  name: string;
}

export interface TableSeat {
  player_id: string;
  seat: number;
  status: 'empty' | 'occupied' | 'reserved';
  balance: number;
  playing: boolean;
}

export interface TableState {
  ti: number; // table id
  ss: GameSeat[]; // seats (same structure as game state)
  sd: number; // seated count
  ps: { [key: string]: boolean }; // players
  wl: string[]; // waiting list
}

export interface PlayerState {
  id: string;
  table_id?: number;
  seat?: number;
  balance: number;
  status: string;
}

export type BetAction = 'fold' | 'check' | 'call' | 'bet' | 'raise' | 'all_in';