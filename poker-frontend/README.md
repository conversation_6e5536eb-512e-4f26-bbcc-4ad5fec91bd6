# X-Poker Frontend

A Next.js-based poker frontend that connects to the X-Poker server via WebSocket.

## Features

- **Firebase Authentication**: Google and GitHub OAuth login
- **Real-time Poker**: WebSocket connection to Hamster server
- **Admin Portal**: User management for admin users
- **Responsive Design**: Works on desktop and mobile
- **Texas Hold'em**: Full poker game implementation

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   Copy `.env.local` and update with your Firebase configuration:
   ```bash
   cp .env.local .env.local.example
   ```

3. **Firebase Setup:**
   - Create a Firebase project
   - Enable Authentication with Google and GitHub providers
   - Copy your Firebase config to `.env.local`

4. **Admin Configuration:**
   Admin users are defined in `src/contexts/AuthContext.tsx`:
   - `<EMAIL>`
   - `<EMAIL>`

## Running

```bash
npm run dev
```

Visit `http://localhost:3000`

## Project Structure

- **`/src/app`**: Next.js app router pages
- **`/src/components`**: Reusable React components
- **`/src/contexts`**: React contexts for auth and poker state
- **`/src/lib`**: Utility libraries (Firebase, WebSocket)
- **`/src/types`**: TypeScript type definitions

## Key Components

- **AuthContext**: Firebase authentication management
- **PokerContext**: WebSocket connection and game state
- **PokerTable**: Visual poker table component
- **BettingControls**: Poker action controls
- **AdminRoute**: Protected admin routes

## Pages

- **`/login`**: Authentication page
- **`/lobby`**: Table selection lobby
- **`/table/[id]`**: Individual poker table
- **`/admin`**: Admin portal (admin users only)

## Environment Variables

```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=

# Hamster Server
NEXT_PUBLIC_HAMSTER_HTTP_URL=http://localhost:7653
NEXT_PUBLIC_HAMSTER_WS_URL=ws://localhost:7655

# Firebase Admin (for API routes)
FIREBASE_PROJECT_ID=
FIREBASE_CLIENT_EMAIL=
FIREBASE_PRIVATE_KEY=
```
