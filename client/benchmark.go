package client

import (
	"fmt"
	"github.com/spf13/cast"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/util/async"
	"time"
)

type Benchmark struct {
	host     string
	port     int
	duration time.Duration
}

func NewBenchmark(host string, port int, duration time.Duration) *Benchmark {
	return &Benchmark{host: host, port: port, duration: duration}
}

func (b *Benchmark) Run(tableIds []uint64) {
	for _, tableId := range tableIds {
		for i := 0; i < 9; i++ {
			index := i
			name := fmt.Sprintf("p-%d-%d", i, tableId)
			async.Go(func() error {
				return b.runSingleClient(name, tableId, index)
			})
		}
	}
	time.Sleep(b.duration)
}

func (b *Benchmark) runSingleClient(name string, tableId uint64, seat int) error {
	c, err := New(b.host, b.port, "ws", name)
	if err != nil {
		return err
	}

	userId := c.name
	// For testing purposes, create a mock session token
	sessionToken := fmt.Sprintf("test-session-%s", userId)

	conn, err := connectors[c.net](c.addr)
	if err != nil {
		return err
	}

	c.peer = peer.New(conn, userId)
	async.Go(func() error {
		return b.initHandlers(c, tableId, seat)
	})
	async.Go(c.heartbeat)
	c.peer.Write(event.NewAuthorizeEvent(sessionToken))
	return c.peer.Listening()

}

func (b *Benchmark) initHandlers(c *Client, tableId uint64, seat int) error {
	hook := c.peer.AddHook(event.TypeAll, 100)
	for packet := range hook.OnPacket() {
		err := b.handleEvent(c, tableId, seat, packet)
		if err != nil {
			c.print(fmt.Sprintf("handle event error: %v\n", err))
			continue
		}
	}
	return nil
}

func (b *Benchmark) handleEvent(c *Client, tableId uint64, seat int, packet *net.Packet) error {
	base := map[string]any{}
	err := packet.Unmarshal(&base)
	if err != nil {
		return err
	}
	t := event.Type(cast.ToString(base["t"]))
	switch t {
	case event.TypeBuyInPrompt:
		e := event.BuyInPromptEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.println(fmt.Sprintf("%s auto buy in 50", c.name))
		c.peer.Write(event.NewBuyInEvent(event.BuyInActionBuyIn, 50))
	case event.TypeGoToTable:
		e := event.GoToTableEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.table = e.Table
		c.println(fmt.Sprintf("%s goto table: %d", c.name, e.Table.ID))
		c.peer.Write(event.NewSitDownEvent(tableId, seat))
	case event.TypeGameState:
		e := event.GameStateEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.gameState = e.GameState
	case event.TypeBetPrompt:
		var seat *event.GameSeat
		for _, s := range c.gameState.Seats {
			if s.PlayerID == c.peer.ID {
				seat = s
				break
			}
		}
		if seat != nil {
			a := c.gameState.MaxBet - seat.TotalBet
			if a == 0 {
				c.peer.Write(event.NewBetEvent(model.BetActionCheck, 0))
				c.println(fmt.Sprintf("%s check", c.name))
			} else if a > 0 && seat.Balance >= a {
				c.peer.Write(event.NewBetEvent(model.BetActionCall, a))
				c.println(fmt.Sprintf("%s call %d", c.name, a))
			} else {
				c.peer.Write(event.NewBetEvent(model.BetActionAllIn, 0))
				c.println(fmt.Sprintf("%s all in", c.name))
			}
		}
	case event.TypeAuthorized:
		c.println(fmt.Sprintf("%s authorized", c.name))
		c.peer.Write(event.NewJoinTableEvent(tableId))
	case event.TypePong:
	}
	return nil
}
