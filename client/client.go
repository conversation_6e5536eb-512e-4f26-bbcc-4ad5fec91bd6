package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"hamster/internal/consts"
	"hamster/internal/db/ent/tables"
	"hamster/internal/event"
	"hamster/internal/model"
	"hamster/internal/net"
	"hamster/internal/peer"
	"hamster/pkg/util/async"
	"io"
	"math/rand/v2"
	"net/http"
	"sync"
	"time"
)

const cleanLine = "\r\r                                                                                              \r\r"

type Client struct {
	sync.Mutex
	peer            *peer.Peer
	host, addr, net string
	port            int
	name            string
	state           string
	table           *event.Table
	tableState      *model.TableState
	gameState       event.GameState
	playerState     *model.PlayerState
}

func New(host string, port int, net string, name string) (*Client, error) {
	return &Client{addr: fmt.Sprintf("%s:%d", host, port), host: host, port: port, net: net, name: name}, nil
}

func (c *Client) Run() error {
	userId := c.name
	// For testing purposes, create a mock session token
	// In a real scenario, this would come from a proper authentication flow
	sessionToken := fmt.Sprintf("test-session-%s", userId)

	conn, err := connectors[c.net](c.addr)
	if err != nil {
		return err
	}

	c.peer = peer.New(conn, userId)
	async.Go(c.initHandlers)
	async.Go(c.initPanel)
	async.Go(c.heartbeat)
	c.peer.Write(event.NewAuthorizeEvent(sessionToken))
	return c.peer.Listening()
}

func (c *Client) initHandlers() error {
	hook := c.peer.AddHook(event.TypeAll, 100)
	for packet := range hook.OnPacket() {
		err := c.handleEvent(packet)
		if err != nil {
			c.print(fmt.Sprintf("handle event error: %v\n", err))
			continue
		}
	}
	return nil
}

func (c *Client) handleEvent(packet *net.Packet) error {
	base := map[string]any{}
	err := packet.Unmarshal(&base)
	if err != nil {
		return err
	}
	t := event.Type(cast.ToString(base["t"]))
	switch t {
	case event.TypeTables:
		e := event.TablesEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.println("id\tname\tcode\tstakes\tseated")
		for _, table := range e.Tables {
			maxPlayers := 6
			if table.Type == tables.Type9p {
				maxPlayers = 9
			}
			c.println(fmt.Sprintf("%d\t%s\t%s\t%s\t%d/%d", table.ID, table.Name, table.Code, fmt.Sprintf("%d/%d", table.SmallBlind, table.BigBlind), table.Seated, maxPlayers))
		}
		c.showMainPanel()
	case event.TypeGameState:
		e := event.GameStateEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.gameState = e.GameState

		//c.println(fmt.Sprintf("game state: %v", packet.String()))

		var seat *event.GameSeat
		for _, s := range e.GameState.Seats {
			if s.PlayerID == c.peer.ID {
				seat = s
				break
			}
		}

		switch e.GameState.State {
		case "init":
			c.println("game init")
			if seat != nil {
				c.println(fmt.Sprintf("your cards %s, balance: %d", e.GameState.Cards, seat.Balance))
			}
		case "pre_flop", "flop", "turn", "river":
			c.println(fmt.Sprintf("** game round: %s", e.GameState.State))
			c.println(fmt.Sprintf("** board cards: %s", e.GameState.Board.String()))
			if seat != nil {
				if e.GameState.State == "pre_flop" {
					if e.GameState.Seats[e.GameState.Button].PlayerID == c.peer.ID {
						c.println(fmt.Sprintf("you are Button"))
					}
					if e.GameState.Seats[e.GameState.SB].PlayerID == c.peer.ID {
						c.println(fmt.Sprintf("you are SB, bet: %d", c.table.SmallBlind))
					}
					if e.GameState.Seats[e.GameState.BB].PlayerID == c.peer.ID {
						c.println(fmt.Sprintf("you are BB, bet: %d", c.table.BigBlind))
					}
				}
				c.println(fmt.Sprintf("your cards %s, balance: %d", e.GameState.Cards, seat.Balance))
			}
		case "settle":
			c.println("game settle!")
		}
	case event.TypeTableState:
		e := event.TableStateEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.tableState = e.TableState
		c.println(fmt.Sprintf("table state: %v", packet.String()))
		c.showTablePanel()
	case event.TypeBuyInPrompt:
		e := event.BuyInPromptEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.println(fmt.Sprintf("buy in prompt: %v", packet.String()))
		c.peer.Write(event.NewBuyInEvent(event.BuyInActionBuyIn, uint64(rand.IntN(50)+50)))
		c.println(fmt.Sprintf("auto buy in 50"))

	case event.TypePlayerState:
		e := event.PlayerStateEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.println(fmt.Sprintf("player state: %v", packet.String()))
		c.playerState = e.PlayerState
	case event.TypeChat:
		e := event.ChatEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.println(fmt.Sprintf("%s: %s", e.PlayerID, e.Message))
	case event.TypeGoToTable:
		e := event.GoToTableEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}
		c.table = e.Table
		c.println(fmt.Sprintf("goto table: %d", e.Table.ID))
	case event.TypeBetPrompt:
		e := event.BetPromptEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}

		if e.PlayerID != c.peer.ID {
			c.println(fmt.Sprintf("!! %s's turn, please wait !!", e.PlayerID))
			return nil
		}

		var seat *event.GameSeat
		for _, s := range c.gameState.Seats {
			if s.PlayerID == c.peer.ID {
				seat = s
				break
			}
		}
		if seat != nil {
			c.println(fmt.Sprintf("!! your turn, balance: %d, please bet in %ds !!", seat.Balance, e.Timeout))
			c.showBetPanel()
		}
	case event.TypeAuthorized:
		c.showMainPanel()
	case event.TypePong:
	case event.TypeGameStartCountdown:
		e := event.TypeGameStartCountdownEvent{}
		err = packet.Unmarshal(&e)
		if err != nil {
			return err
		}

		c.println(fmt.Sprintf("game start in %ds", e.Timeout))
	case event.TypeShowdownPrompt:
		c.println(fmt.Sprintf("showdown in prompt: %v", packet.String()))
		c.peer.Write(event.NewShowdownEvent())
		c.println(fmt.Sprintf("showdown !!"))
	}
	return nil
}

func (c *Client) heartbeat() error {
	ticker := time.NewTicker(consts.PingTimeout - 5*time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			c.peer.Write(event.NewPingEvent())
		}
	}
}

func (c *Client) print(str string) {
	c.Lock()
	defer c.Unlock()
	fmt.Print(str)
}

func (c *Client) println(str string) {
	c.Lock()
	defer c.Unlock()
	fmt.Println(str)
}


