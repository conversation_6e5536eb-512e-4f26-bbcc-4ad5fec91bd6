package client

import (
	"fmt"
	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	netx "hamster/internal/net"
	"net"
	"net/url"
)

type connector func(addr string) (netx.ReadWriteCloser, error)

var connectors = map[string]connector{
	"tcp": tcpConnector,
	"ws":  websocketConnector,
}

func tcpConnector(addr string) (netx.ReadWriteCloser, error) {
	tcpAddr, err := net.ResolveTCPAddr("tcp", addr)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("tcp server error: %v", err))
	}
	conn, err := net.DialTCP("tcp", nil, tcpAddr)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("tcp server error: %v", err))
	}
	return netx.NewTcpReadWriteCloser(conn, 0), nil
}

func websocketConnector(addr string) (netx.ReadWriteCloser, error) {
	u := url.URL{Scheme: "ws", Host: addr, Path: "/ws"}
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("ws server error: %v", err))
	}
	return netx.NewWebsocketReadWriteCloser(conn), nil
}
