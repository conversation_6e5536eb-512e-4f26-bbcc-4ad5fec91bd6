package client

import (
	"github.com/spf13/cast"
	"hamster/internal/event"
	"hamster/internal/model"
)

func (c *Client) showMainPanel() {
	c.println(`
actions:
- 1. get_tables		- 2. join_table	   - 3. get_player_state	
- 4. get_table_state	- 5. get_game_state
`)
	c.state = "main"
}

func (c *Client) showTablePanel() {
	c.println(`
actions:
- 1. leave_table	- 2. join_waiting_list   - 3. leave_waiting_list	
- 4. sit_down		- 5. stand_up
`)
	c.state = "table"
}

func (c *Client) showBetPanel() {
	c.println(`actions:
- 1. call		- 2. check		- 3. fold		- 4. raise		- 5. all_in		- 6. leave_table`)
	c.print("action: ")
	c.state = "bet"
}

func (c *Client) initPanel() error {
	for {
		line := Readline()
		switch c.state {
		case "", "main":
			c.handleMainPanel(line)
		case "table":
			c.handleTablePanel(line)
		case "bet":
			c.handleBetPanel(line)
		}
	}
}

func (c *Client) handleMainPanel(line string) {
	switch line {
	case "1":
		c.peer.Write(event.NewGetTablesEvent())
	case "2":
		c.print("table id: ")
		tableId := Readline()
		c.peer.Write(event.NewJoinTableEvent(cast.ToUint64(tableId)))
	case "3":
		c.peer.Write(event.NewGetPlayerStateEvent())
	case "4":
		c.print("table id: ")
		tableId := Readline()
		c.peer.Write(event.NewGetTableStateEvent(cast.ToUint64(tableId)))
	case "5":
		c.print("table id: ")
		tableId := Readline()
		c.peer.Write(event.NewGetGameStateEvent(cast.ToUint64(tableId)))
	default:
		if v, err := cast.ToUint16E(line); err == nil {
			c.peer.Write(event.NewSendMessageEvent(v))
		}
		c.showMainPanel()
	}
}

func (c *Client) handleTablePanel(line string) {
	switch line {
	case "1":
		c.peer.Write(event.NewLeaveTableEvent(c.table.ID))
	case "2":
		c.peer.Write(event.NewJoinWaitingListEvent(c.table.ID))
	case "3":
		c.peer.Write(event.NewLeaveWaitingListEvent(c.table.ID))
	case "4":
		c.print("seat: ")
		seat := Readline()
		c.peer.Write(event.NewSitDownEvent(c.table.ID, cast.ToInt(seat)))
	case "5":
		c.peer.Write(event.NewStandUpEvent(c.table.ID))
	default:
		if v, err := cast.ToUint16E(line); err == nil {
			c.peer.Write(event.NewSendMessageEvent(v))
		}
		c.showTablePanel()
	}
}

func (c *Client) handleBetPanel(action string) {
	var seat *event.GameSeat
	for _, s := range c.gameState.Seats {
		if s.PlayerID == c.peer.ID {
			seat = s
			break
		}
	}

	switch action {
	case "1":
		if seat != nil {
			c.peer.Write(event.NewBetEvent(model.BetActionCall, c.gameState.MaxBet-seat.TotalBet))
		}
	case "2":
		c.peer.Write(event.NewBetEvent(model.BetActionCheck, 0))
	case "3":
		c.peer.Write(event.NewBetEvent(model.BetActionFold, 0))
	case "4":
		c.print("amount: ")
		amount := Readline()
		c.peer.Write(event.NewBetEvent(model.BetActionRaise, cast.ToUint64(amount)))
	case "5":
		if seat != nil {
			c.peer.Write(event.NewBetEvent(model.BetActionAllIn, seat.Balance))
		}
	case "6":
		c.peer.Write(event.NewBetEvent(model.BetActionFold, 0))
		if c.table != nil {
			c.peer.Write(event.NewLeaveTableEvent(c.table.ID))
		}
	default:
		if v, err := cast.ToUint16E(action); err == nil {
			c.peer.Write(event.NewSendMessageEvent(v))
		}
		c.showBetPanel()
	}
}
