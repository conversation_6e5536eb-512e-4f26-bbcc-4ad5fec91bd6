{"apps": [{"name": "hamster-server", "script": "/usr/local/go/bin/go", "args": "run main.go", "cwd": "/home/<USER>/hamster", "instances": 1, "autorestart": true, "watch": true, "watch_delay": 1000, "ignore_watch": ["logs", ".git", "*.log", ".env", "*.tmp", "*.pid"], "watch_options": {"followSymlinks": false, "usePolling": false}, "max_memory_restart": "1G", "env": {"GIN_MODE": "release", "DISABLE_SWAGGER_HTTP_HANDLER": "true"}, "env_file": ".env", "log_file": "./logs/hamster.log", "out_file": "./logs/hamster-out.log", "error_file": "./logs/hamster-error.log", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "merge_logs": true, "time": true, "min_uptime": "10s", "max_restarts": 10, "restart_delay": 4000, "kill_timeout": 1600}]}