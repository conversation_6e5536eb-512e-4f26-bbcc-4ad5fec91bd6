package config

import (
	"github.com/ilyakaznacheev/cleanenv"
)

type (
	Config struct {
		Server `yaml:"server"`
		Redis  `yaml:"redis"`
		DB     `yaml:"db"`
		Logger `yaml:"logger"`
	}

	Server struct {
		HTTP      `yaml:"http"`
		TCP       `yaml:"tcp"`
		Websocket `yaml:"websocket"`
	}

	HTTP struct {
		Host         string `yaml:"host" env:"SERVER_HTTP_HOST"`
		Port         int    `yaml:"port" env:"SERVER_HTTP_PORT"`
		ReadTimeout  int64  `yaml:"read_timeout" env:"SERVER_HTTP_READ_TIMEOUT"`
		WriteTimeout int64  `yaml:"write_timeout" env:"SERVER_HTTP_WRITE_TIMEOUT"`
	}

	TCP struct {
		Host            string `yaml:"host" env:"SERVER_TCP_HOST"`
		Port            int    `yaml:"port" env:"SERVER_TCP_PORT"`
		ReadBufferSize  int    `yaml:"read_buffer_size" env:"SERVER_TCP_READ_BUFFER_SIZE"`
		WriteBufferSize int    `yaml:"write_buffer_size" env:"SERVER_TCP_WRITE_BUFFER_SIZE"`
	}

	Websocket struct {
		Host            string `yaml:"host" env:"SERVER_WEBSOCKET_HOST"`
		Port            int    `yaml:"port" env:"SERVER_WEBSOCKET_PORT"`
		ReadBufferSize  int    `yaml:"read_buffer_size" env:"SERVER_WEBSOCKET_READ_BUFFER_SIZE"`
		WriteBufferSize int    `yaml:"write_buffer_size" env:"SERVER_WEBSOCKET_WRITE_BUFFER_SIZE"`
	}

	Redis struct {
		Addr      string `yaml:"addr" env:"REDIS_ADDR"`
		Password  string `yaml:"password" env:"REDIS_PASSWORD"`
		Database  int    `yaml:"database" env:"REDIS_DATABASE"`
		MaxIdle   int    `yaml:"max_idle" env:"REDIS_MAX_IDLE"`
		MaxActive int    `yaml:"max_active" env:"REDIS_MAX_ACTIVE"`
	}



	DB struct {
		Host        string `yaml:"host" env:"DB_HOST"`
		Port        int64  `yaml:"port" env:"DB_PORT"`
		UserName    string `yaml:"username" env:"DB_USERNAME"`
		Password    string `yaml:"password" env:"DB_PASSWORD"`
		DBName      string `yaml:"dbname" env:"DB_DBNAME"`
		SSLMode     string `yaml:"sslmode" env:"DB_SSLMODE"`
		MaxOpen     int    `yaml:"max_open" env:"DB_MAX_OPEN"`
		MaxIdle     int    `yaml:"max_idle" env:"DB_MAX_IDLE"`
		MaxIdleTime int    `yaml:"max_idle_time" env:"DB_MAX_IDLE_TIME"`
		MaxLifetime int    `yaml:"max_lifetime" env:"DB_MAX_LIFETIME"`
	}

	Logger struct {
		Debug bool `yaml:"debug" env:"LOGGER_DEBUG"`
	}
)

func New() (Config, error) {
	cfg := Config{}
	err := cleanenv.ReadEnv(&cfg)
	return cfg, err
}
