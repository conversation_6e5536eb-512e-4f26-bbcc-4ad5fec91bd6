package main

import (
	atlas "ariga.io/atlas/sql/migrate"
	"context"
	"hamster/config"
	"hamster/internal/db"
	"hamster/internal/db/ent/migrate"
	"hamster/pkg/log"
	"os"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql/schema"
	"go.uber.org/fx"
)

func main() {
	if len(os.Args) != 2 {
		log.Fatal("usage: go run -mod=mod cmd/ent/migrate/main.go <name>")
	}

	err := fx.New(
		fx.Provide(config.New, db.New, New),
		fx.Invoke(func(m *Migrator) error {
			return m.Run(os.Args[1])
		}),
	).Start(context.Background())
	if err != nil {
		log.Fatal(err)
	}
}

type Migrator struct {
	db *db.Client
}

func New(db *db.Client) *Migrator {
	return &Migrator{
		db: db,
	}
}

func (m *Migrator) Run(name string) error {
	ctx := context.Background()
	// create a local migration directory
	dir, err := atlas.NewLocalDir("migration")
	if err != nil {
		log.Errorf("create local migration directory: %v", err)
		return nil
	}

	opts := []schema.MigrateOption{
		schema.WithDir(dir),
		schema.WithMigrationMode(schema.ModeInspect),
		schema.WithDialect(dialect.Postgres),
		schema.WithFormatter(atlas.DefaultFormatter),
		migrate.WithGlobalUniqueID(false),
		migrate.WithDropIndex(true),
		migrate.WithDropColumn(true),
	}

	// Run migration.
	err = m.db.Schema.NamedDiff(
		ctx,
		name,
		opts...,
	)
	if err != nil {
		log.Errorf("ent migrate falied: %v", err)
	}
	return nil
}
