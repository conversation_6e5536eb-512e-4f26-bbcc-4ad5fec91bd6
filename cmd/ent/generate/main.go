package main

import (
	"log"

	"entgo.io/ent/entc"
	"entgo.io/ent/entc/gen"
)

func main() {

	if err := entc.Generate("./internal/db/ent/schema", &gen.Config{
		Header: `
			// Copyright 2019-present Facebook
			//
			// Licensed under the Apache License, Version 2.0 (the "License");
			// you may not use this file except in compliance with the License.
			// You may obtain a copy of the License at
			//
			//     http://www.apache.org/licenses/LICENSE-2.0
			//
			// Unless required by applicable law or agreed to in writing, software
			// distributed under the License is distributed on an "AS IS" BASIS,
			// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
			// See the License for the specific language governing permissions and
			// limitations under the License.
			//
			// Code generated by entc, DO NOT EDIT.
		`,
		Hooks: []gen.Hook{},
		Features: []gen.Feature{
			gen.FeatureExecQuery,
			gen.FeatureUpsert,
			gen.FeatureEntQL,
			gen.FeaturePrivacy,
			gen.FeatureVersionedMigration,
			gen.FeatureIntercept,
			gen.FeatureLock,
		},
	}); err != nil {
		log.Fatalf("Error: failed running ent codegen: %v", err)
	}
}
