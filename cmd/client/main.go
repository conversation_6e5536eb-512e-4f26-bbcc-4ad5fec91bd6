package main

import (
	"flag"
	"hamster/client"
)

var (
	host string
	port int
	name string
)

func init() {
	flag.StringVar(&host, "h", "127.0.0.1", "host")
	flag.IntVar(&port, "p", 7655, "port")
	flag.StringVar(&name, "n", "p1", "name")
	flag.Parse()
}

func main() {
	cli, err := client.New(host, port, "ws", name)
	if err != nil {
		panic(err)
	}

	err = cli.Run()
	if err != nil {
		panic(err)
	}
}
